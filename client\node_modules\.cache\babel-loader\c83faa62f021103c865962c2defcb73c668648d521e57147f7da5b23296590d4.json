{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\WriteExam\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { getExamById } from \"../../../apicalls/exams\";\nimport { addReport } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport Instructions from \"./Instructions\";\nimport Pass from \"../../../assets/pass.gif\";\nimport Fail from \"../../../assets/fail.gif\";\nimport Confetti from \"react-confetti\";\nimport useWindowSize from \"react-use/lib/useWindowSize\";\nimport PassSound from \"../../../assets/pass.mp3\";\nimport FailSound from \"../../../assets/fail.mp3\";\nimport ContentRenderer from \"../../../components/ContentRenderer\";\nimport { chatWithChatGPTToExplainAns, chatWithChatGPTToGetAns } from \"../../../apicalls/chat\";\nimport QuizRenderer from \"../../../components/QuizRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction WriteExam() {\n  _s();\n  var _result$correctAnswer, _result$correctAnswer2, _result$correctAnswer3, _result$correctAnswer4;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [result, setResult] = useState({});\n  const params = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [view, setView] = useState(\"instructions\");\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const [explanations, setExplanations] = useState({});\n  const getExamData = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({\n        examId: params.id\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        var _response$data, _response$data2;\n        setQuestions(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || []);\n        setExamData(response.data);\n        setSecondsLeft(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.duration) || 0);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [params.id, dispatch]);\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      const indexMap = [];\n      questions.forEach((q, idx) => {\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          indexMap.push(idx);\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      const wrongPayload = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: q.correctAnswer || q.correctOption,\n              userAnswer: userAnswerKey\n            });\n          }\n        } else if (q.answerType === \"Options\") {\n          const correctKey = q.correctOption;\n          const correctValue = q.options[correctKey];\n          const userValue = q.options[userAnswerKey] || \"\";\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: correctValue,\n              userAnswer: userValue\n            });\n          }\n        }\n      });\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict\n      };\n      setResult(tempResult);\n      const response = await addReport({\n        exam: params.id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        setView(\"result\");\n        window.scrollTo(0, 0);\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const startTimer = () => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0;\n    setSecondsLeft(totalSeconds);\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  };\n  useEffect(() => {\n    if (timeUp && view === \"questions\") {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, view, intervalId, calculateResult]);\n  useEffect(() => {\n    if (params.id) {\n      getExamData();\n    }\n  }, [params.id, getExamData]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  // Repair function for fixing orphaned questions\n  const repairExamQuestions = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await fetch('/api/exams/repair-exam-questions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          examId: params.id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        message.success(data.message);\n        // Reload the exam data\n        getExamData();\n      } else {\n        message.error(data.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to repair exam questions\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Check if user is authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-10 h-10 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-8\",\n          children: \"Please log in to access the exam and start your learning journey.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          onClick: () => navigate(\"/login\"),\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this);\n  }\n  return examData ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [view !== \"instructions\" && view !== \"questions\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/80 backdrop-blur-sm border-b border-blue-100 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-6 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-white\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n              children: (examData === null || examData === void 0 ? void 0 : examData.name) || 'Loading...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mt-1\",\n              children: \"Exam Results & Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 9\n    }, this), view === \"instructions\" && /*#__PURE__*/_jsxDEV(Instructions, {\n      examData: examData,\n      setView: setView,\n      startTimer: startTimer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 9\n    }, this), view === \"questions\" && (questions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-amber-800 mb-4\",\n          children: \"No Questions Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-700 mb-8 text-lg leading-relaxed\",\n          children: \"This exam appears to have no questions. Let's fix this issue and get you started!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: repairExamQuestions,\n          className: \"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          children: \"\\uD83D\\uDD27 Repair Questions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 11\n    }, this) : questions[selectedQuestionIndex] && /*#__PURE__*/_jsxDEV(QuizRenderer, {\n      question: questions[selectedQuestionIndex],\n      questionIndex: selectedQuestionIndex,\n      totalQuestions: questions.length,\n      selectedAnswer: selectedOptions[selectedQuestionIndex],\n      onAnswerChange: answer => setSelectedOptions({\n        ...selectedOptions,\n        [selectedQuestionIndex]: answer\n      }),\n      timeLeft: secondsLeft,\n      username: (user === null || user === void 0 ? void 0 : user.name) || \"Student\",\n      examTitle: (examData === null || examData === void 0 ? void 0 : examData.name) || \"Quiz\",\n      onNext: () => {\n        if (selectedQuestionIndex === questions.length - 1) {\n          calculateResult();\n        } else {\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      },\n      onPrevious: () => setSelectedQuestionIndex(selectedQuestionIndex - 1)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 13\n    }, this)), view === \"result\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12\",\n      children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 41\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-5xl mx-auto px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-blue-100 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-6 py-8 text-center relative overflow-hidden ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-green-50 to-emerald-50\" : \"bg-gradient-to-br from-orange-50 to-amber-50\"}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-24 h-24 mx-auto mb-4 rounded-full flex items-center justify-center shadow-lg ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-green-500 to-emerald-500\" : \"bg-gradient-to-r from-orange-500 to-amber-500\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: result.verdict === \"Pass\" ? Pass : Fail,\n                  alt: result.verdict,\n                  className: \"w-16 h-16 object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-3xl font-bold mb-3 ${result.verdict === \"Pass\" ? \"text-green-700\" : \"text-orange-700\"}`,\n                children: result.verdict === \"Pass\" ? \"🎉 Congratulations!\" : \"💪 Keep Going!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-700 font-medium\",\n                children: result.verdict === \"Pass\" ? \"You've successfully passed the exam! Outstanding work!\" : \"Every attempt is a step forward. Keep practicing and try again!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-3 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200 shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-600 mb-1\",\n                  children: [Math.round((((_result$correctAnswer = result.correctAnswers) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.length) || 0) / questions.length * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-semibold text-blue-700\",\n                  children: \"Your Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-200 shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-green-600 mb-1\",\n                  children: [((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0, \"/\", questions.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-semibold text-green-700\",\n                  children: \"Correct Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-center p-4 rounded-lg border shadow-sm ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-50 to-green-50 border-emerald-200\" : \"bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-2xl font-bold mb-1 ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-orange-600\"}`,\n                  children: result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-sm font-semibold ${result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-orange-700\"}`,\n                  children: result.verdict === \"Pass\" ? \"Well Done!\" : `Need ${examData.passingMarks}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-200 rounded-full h-3 shadow-inner\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `h-full rounded-full transition-all duration-1000 shadow-sm ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-green-500 to-emerald-500\" : \"bg-gradient-to-r from-orange-500 to-amber-500\"}`,\n                    style: {\n                      width: `${(((_result$correctAnswer3 = result.correctAnswers) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.length) || 0) / questions.length * 100}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600\",\n                    children: \"0%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-semibold text-gray-700\",\n                    children: [\"Progress: \", Math.round((((_result$correctAnswer4 = result.correctAnswers) === null || _result$correctAnswer4 === void 0 ? void 0 : _result$correctAnswer4.length) || 0) / questions.length * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600\",\n                    children: \"100%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-5 py-2 bg-blue-600 hover:bg-blue-700 text-blue-100 rounded-lg font-semibold transition-all duration-300 shadow-sm\",\n                onClick: () => setView(\"review\"),\n                children: \"Review Answers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-5 py-2 bg-gray-600 hover:bg-gray-700 text-gray-100 rounded-lg font-semibold transition-all duration-300 shadow-sm\",\n                onClick: () => navigate(\"/user/dashboard\"),\n                children: \"Back to Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 9\n    }, this), view === \"review\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-5xl mx-auto px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-white\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3\",\n              children: \"Review Your Answers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-lg\",\n              children: \"Detailed breakdown of your exam performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8 mb-12\",\n          children: questions.map((question, index) => {\n            const correctAnswer = question.answerType === \"Options\" ? question.correctOption : question.correctAnswer;\n            const isCorrect = correctAnswer === selectedOptions[index];\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-100 overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-8 py-6 border-b ${isCorrect ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-12 h-12 rounded-full flex items-center justify-center font-bold text-white ${isCorrect ? 'bg-green-500' : 'bg-red-500'}`,\n                      children: index + 1\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-xl font-bold text-gray-900\",\n                      children: [\"Question \", index + 1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-6 py-3 rounded-full text-lg font-bold flex items-center space-x-2 ${isCorrect ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`,\n                    children: isCorrect ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 526,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Correct\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 528,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 533,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 532,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Incorrect\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 535,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-xl font-semibold text-gray-900 mb-6 leading-relaxed\",\n                  children: question.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 23\n                }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-block p-3 bg-white rounded-xl shadow-lg border border-gray-200\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: question.image,\n                      alt: \"Question\",\n                      className: \"max-w-full h-auto rounded-lg\",\n                      style: {\n                        maxHeight: '300px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid md:grid-cols-2 gap-6 mb-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-6 rounded-xl border-2 shadow-lg ${isCorrect ? 'border-green-300 bg-gradient-to-br from-green-50 to-emerald-50' : 'border-red-300 bg-gradient-to-br from-red-50 to-pink-50'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-8 h-8 rounded-full flex items-center justify-center ${isCorrect ? 'bg-green-500' : 'bg-red-500'}`,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"w-4 h-4 text-white\",\n                          fill: \"currentColor\",\n                          viewBox: \"0 0 20 20\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 571,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 570,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 567,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-800 text-lg\",\n                        children: \"Your Answer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 574,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-900 text-lg font-medium\",\n                      children: question.answerType === \"Options\" ? question.options[selectedOptions[index]] || \"Not answered\" : selectedOptions[index] || \"Not answered\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-6 rounded-xl border-2 border-green-300 bg-gradient-to-br from-green-50 to-emerald-50 shadow-lg\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 rounded-full flex items-center justify-center bg-green-500\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"w-4 h-4 text-white\",\n                          fill: \"currentColor\",\n                          viewBox: \"0 0 20 20\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 587,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 586,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 585,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-800 text-lg\",\n                        children: \"Correct Answer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 590,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-900 text-lg font-medium\",\n                      children: question.answerType === \"Options\" ? question.options[question.correctOption] : question.correctAnswer || question.correctOption\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 23\n                }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg flex items-center space-x-3\",\n                    onClick: () => fetchExplanation(question.name, question.answerType === \"Options\" ? question.options[question.correctOption] : question.correctAnswer || question.correctOption, question.answerType === \"Options\" ? question.options[selectedOptions[index]] || \"Not answered\" : selectedOptions[index] || \"Not answered\", question.image),\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 617,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Get AI Explanation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 619,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 27\n                  }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-6 p-6 bg-white/90 backdrop-blur-sm rounded-xl border border-blue-200 shadow-lg\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"w-4 h-4 text-white\",\n                          fill: \"currentColor\",\n                          viewBox: \"0 0 20 20\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 627,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 626,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 625,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"font-bold text-blue-800 text-lg\",\n                        children: \"AI Explanation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                      content: explanations[question.name]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-8 py-4 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl font-semibold text-lg hover:from-gray-700 hover:to-gray-800 transform hover:scale-105 transition-all duration-300 shadow-lg flex items-center justify-center space-x-3 mx-auto\",\n            onClick: () => navigate(\"/user/dashboard\"),\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Back to Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this) : null;\n}\n_s(WriteExam, \"ZNpCVL+CJB3fnQYZ800cmvPHL84=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useWindowSize];\n});\n_c = WriteExam;\nexport default WriteExam;\nvar _c;\n$RefreshReg$(_c, \"WriteExam\");", "map": {"version": 3, "names": ["message", "React", "useCallback", "useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "getExamById", "addReport", "HideLoading", "ShowLoading", "Instructions", "Pass", "Fail", "Confetti", "useWindowSize", "PassSound", "FailSound", "Content<PERSON><PERSON><PERSON>", "chatWithChatGPTToExplainAns", "chatWithChatGPTToGetAns", "Quiz<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WriteExam", "_s", "_result$correctAnswer", "_result$correctAnswer2", "_result$correctAnswer3", "_result$correctAnswer4", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "result", "setResult", "params", "dispatch", "navigate", "view", "<PERSON><PERSON><PERSON><PERSON>", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "user", "state", "width", "height", "explanations", "setExplanations", "getExamData", "response", "examId", "id", "success", "_response$data", "_response$data2", "data", "duration", "error", "checkFreeTextAnswers", "payload", "length", "calculateResult", "_id", "freeTextPayload", "indexMap", "for<PERSON>ach", "q", "idx", "answerType", "push", "question", "name", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "wrongPayload", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "correctValue", "options", "userValue", "verdict", "passingMarks", "tempResult", "exam", "window", "scrollTo", "Audio", "play", "fetchExplanation", "imageUrl", "prev", "explanation", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "repairExamQuestions", "fetch", "method", "headers", "localStorage", "getItem", "body", "JSON", "stringify", "json", "className", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "answer", "timeLeft", "username", "examTitle", "onNext", "onPrevious", "src", "alt", "Math", "round", "style", "map", "index", "image", "maxHeight", "content", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/WriteExam/index.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\nimport { chatWithChatGPTToExplainAns, chatWithChatGPTToGetAns } from \"../../../apicalls/chat\";\r\nimport QuizRenderer from \"../../../components/QuizRenderer\";\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getExamById({ examId: params.id });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setQuestions(response.data?.questions || []);\r\n        setExamData(response.data);\r\n        setSecondsLeft(response.data?.duration || 0);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = q.options[correctKey];\r\n          const userValue = q.options[userAnswerKey] || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\r\n      const tempResult = { correctAnswers, wrongAnswers, verdict };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0;\r\n    setSecondsLeft(totalSeconds);\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, [params.id, getExamData]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      {/* Enhanced Header for non-quiz views */}\r\n      {view !== \"instructions\" && view !== \"questions\" && (\r\n        <div className=\"bg-white/80 backdrop-blur-sm border-b border-blue-100 shadow-lg\">\r\n          <div className=\"max-w-6xl mx-auto px-6 py-8\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center\">\r\n                <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\r\n                  {examData?.name || 'Loading...'}\r\n                </h1>\r\n                <p className=\"text-gray-600 mt-1\">Exam Results & Review</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-8 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. Let's fix this issue and get you started!\r\n              </p>\r\n              <button\r\n                onClick={repairExamQuestions}\r\n                className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n              >\r\n                🔧 Repair Questions\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          questions[selectedQuestionIndex] && (\r\n            <QuizRenderer\r\n              question={questions[selectedQuestionIndex]}\r\n              questionIndex={selectedQuestionIndex}\r\n              totalQuestions={questions.length}\r\n              selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n              onAnswerChange={(answer) =>\r\n                setSelectedOptions({\r\n                  ...selectedOptions,\r\n                  [selectedQuestionIndex]: answer,\r\n                })\r\n              }\r\n              timeLeft={secondsLeft}\r\n              username={user?.name || \"Student\"}\r\n              examTitle={examData?.name || \"Quiz\"}\r\n              onNext={() => {\r\n                if (selectedQuestionIndex === questions.length - 1) {\r\n                  calculateResult();\r\n                } else {\r\n                  setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n                }\r\n              }}\r\n              onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}\r\n            />\r\n          )\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-5xl mx-auto px-6\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-blue-100 overflow-hidden\">\r\n              {/* Enhanced Header */}\r\n              <div className={`px-6 py-8 text-center relative overflow-hidden ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-green-50 to-emerald-50\"\r\n                  : \"bg-gradient-to-br from-orange-50 to-amber-50\"\r\n              }`}>\r\n                <div className=\"relative z-10\">\r\n                  <div className={`w-24 h-24 mx-auto mb-4 rounded-full flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-r from-green-500 to-emerald-500\"\r\n                      : \"bg-gradient-to-r from-orange-500 to-amber-500\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-16 h-16 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-3xl font-bold mb-3 ${\r\n                    result.verdict === \"Pass\" ? \"text-green-700\" : \"text-orange-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"🎉 Congratulations!\" : \"💪 Keep Going!\"}\r\n                  </h1>\r\n                  <p className=\"text-lg text-gray-700 font-medium\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've successfully passed the exam! Outstanding work!\"\r\n                      : \"Every attempt is a step forward. Keep practicing and try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Simplified Key Statistics */}\r\n              <div className=\"p-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3 mb-4\">\r\n                  {/* Score Card */}\r\n                  <div className=\"text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200 shadow-sm\">\r\n                    <div className=\"text-2xl font-bold text-blue-600 mb-1\">\r\n                      {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                    </div>\r\n                    <div className=\"text-sm font-semibold text-blue-700\">Your Score</div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-200 shadow-sm\">\r\n                    <div className=\"text-2xl font-bold text-green-600 mb-1\">\r\n                      {result.correctAnswers?.length || 0}/{questions.length}\r\n                    </div>\r\n                    <div className=\"text-sm font-semibold text-green-700\">Correct Answers</div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`text-center p-4 rounded-lg border shadow-sm ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-50 to-green-50 border-emerald-200\"\r\n                      : \"bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200\"\r\n                  }`}>\r\n                    <div className={`text-2xl font-bold mb-1 ${\r\n                      result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-orange-600\"\r\n                    }`}>\r\n                      {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                    </div>\r\n                    <div className={`text-sm font-semibold ${\r\n                      result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-orange-700\"\r\n                    }`}>\r\n                      {result.verdict === \"Pass\" ? \"Well Done!\" : `Need ${examData.passingMarks}`}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Simplified Progress Bar */}\r\n                <div className=\"mb-6\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"w-full bg-gray-200 rounded-full h-3 shadow-inner\">\r\n                      <div\r\n                        className={`h-full rounded-full transition-all duration-1000 shadow-sm ${\r\n                          result.verdict === \"Pass\"\r\n                            ? \"bg-gradient-to-r from-green-500 to-emerald-500\"\r\n                            : \"bg-gradient-to-r from-orange-500 to-amber-500\"\r\n                        }`}\r\n                        style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                      ></div>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center mt-2\">\r\n                      <span className=\"text-xs text-gray-600\">0%</span>\r\n                      <span className=\"text-sm font-semibold text-gray-700\">\r\n                        Progress: {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </span>\r\n                      <span className=\"text-xs text-gray-600\">100%</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Enhanced Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\r\n                  <button\r\n                    className=\"px-5 py-2 bg-blue-600 hover:bg-blue-700 text-blue-100 rounded-lg font-semibold transition-all duration-300 shadow-sm\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    Review Answers\r\n                  </button>\r\n\r\n                  <button\r\n                    className=\"px-5 py-2 bg-gray-600 hover:bg-gray-700 text-gray-100 rounded-lg font-semibold transition-all duration-300 shadow-sm\"\r\n                    onClick={() => navigate(\"/user/dashboard\")}\r\n                  >\r\n                    Back to Dashboard\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12\">\r\n          <div className=\"max-w-5xl mx-auto px-6\">\r\n            {/* Enhanced Header */}\r\n            <div className=\"text-center mb-12\">\r\n              <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-blue-100\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <svg className=\"w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\" />\r\n                    <path fillRule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n                <h2 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3\">\r\n                  Review Your Answers\r\n                </h2>\r\n                <p className=\"text-gray-600 text-lg\">Detailed breakdown of your exam performance</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Enhanced Questions Review */}\r\n            <div className=\"space-y-8 mb-12\">\r\n              {questions.map((question, index) => {\r\n                const correctAnswer = question.answerType === \"Options\"\r\n                  ? question.correctOption\r\n                  : question.correctAnswer;\r\n                const isCorrect = correctAnswer === selectedOptions[index];\r\n\r\n                return (\r\n                  <div key={index} className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-100 overflow-hidden\">\r\n                    {/* Enhanced Question Header */}\r\n                    <div className={`px-8 py-6 border-b ${\r\n                      isCorrect\r\n                        ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'\r\n                        : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'\r\n                    }`}>\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex items-center space-x-4\">\r\n                          <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-white ${\r\n                            isCorrect ? 'bg-green-500' : 'bg-red-500'\r\n                          }`}>\r\n                            {index + 1}\r\n                          </div>\r\n                          <h3 className=\"text-xl font-bold text-gray-900\">\r\n                            Question {index + 1}\r\n                          </h3>\r\n                        </div>\r\n                        <span className={`px-6 py-3 rounded-full text-lg font-bold flex items-center space-x-2 ${\r\n                          isCorrect\r\n                            ? 'bg-green-100 text-green-700'\r\n                            : 'bg-red-100 text-red-700'\r\n                        }`}>\r\n                          {isCorrect ? (\r\n                            <>\r\n                              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                              </svg>\r\n                              <span>Correct</span>\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                              </svg>\r\n                              <span>Incorrect</span>\r\n                            </>\r\n                          )}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Enhanced Question Content */}\r\n                    <div className=\"p-8\">\r\n                      <h4 className=\"text-xl font-semibold text-gray-900 mb-6 leading-relaxed\">{question.name}</h4>\r\n\r\n                      {question.image && (\r\n                        <div className=\"mb-8\">\r\n                          <div className=\"inline-block p-3 bg-white rounded-xl shadow-lg border border-gray-200\">\r\n                            <img\r\n                              src={question.image}\r\n                              alt=\"Question\"\r\n                              className=\"max-w-full h-auto rounded-lg\"\r\n                              style={{ maxHeight: '300px' }}\r\n                            />\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Enhanced Answer Comparison */}\r\n                      <div className=\"grid md:grid-cols-2 gap-6 mb-8\">\r\n                        <div className={`p-6 rounded-xl border-2 shadow-lg ${\r\n                          isCorrect\r\n                            ? 'border-green-300 bg-gradient-to-br from-green-50 to-emerald-50'\r\n                            : 'border-red-300 bg-gradient-to-br from-red-50 to-pink-50'\r\n                        }`}>\r\n                          <div className=\"flex items-center space-x-3 mb-3\">\r\n                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\r\n                              isCorrect ? 'bg-green-500' : 'bg-red-500'\r\n                            }`}>\r\n                              <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                <path fillRule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clipRule=\"evenodd\" />\r\n                              </svg>\r\n                            </div>\r\n                            <h5 className=\"font-bold text-gray-800 text-lg\">Your Answer</h5>\r\n                          </div>\r\n                          <p className=\"text-gray-900 text-lg font-medium\">\r\n                            {question.answerType === \"Options\"\r\n                              ? question.options[selectedOptions[index]] || \"Not answered\"\r\n                              : selectedOptions[index] || \"Not answered\"}\r\n                          </p>\r\n                        </div>\r\n\r\n                        <div className=\"p-6 rounded-xl border-2 border-green-300 bg-gradient-to-br from-green-50 to-emerald-50 shadow-lg\">\r\n                          <div className=\"flex items-center space-x-3 mb-3\">\r\n                            <div className=\"w-8 h-8 rounded-full flex items-center justify-center bg-green-500\">\r\n                              <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                              </svg>\r\n                            </div>\r\n                            <h5 className=\"font-bold text-gray-800 text-lg\">Correct Answer</h5>\r\n                          </div>\r\n                          <p className=\"text-gray-900 text-lg font-medium\">\r\n                            {question.answerType === \"Options\"\r\n                              ? question.options[question.correctOption]\r\n                              : (question.correctAnswer || question.correctOption)}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Enhanced Explanation Section */}\r\n                      {!isCorrect && (\r\n                        <div className=\"mt-6 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-lg\">\r\n                          <button\r\n                            className=\"px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg flex items-center space-x-3\"\r\n                            onClick={() => fetchExplanation(\r\n                              question.name,\r\n                              question.answerType === \"Options\"\r\n                                ? question.options[question.correctOption]\r\n                                : (question.correctAnswer || question.correctOption),\r\n                              question.answerType === \"Options\"\r\n                                ? question.options[selectedOptions[index]] || \"Not answered\"\r\n                                : selectedOptions[index] || \"Not answered\",\r\n                              question.image\r\n                            )}\r\n                          >\r\n                            <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\" clipRule=\"evenodd\" />\r\n                            </svg>\r\n                            <span>Get AI Explanation</span>\r\n                          </button>\r\n\r\n                          {explanations[question.name] && (\r\n                            <div className=\"mt-6 p-6 bg-white/90 backdrop-blur-sm rounded-xl border border-blue-200 shadow-lg\">\r\n                              <div className=\"flex items-center space-x-3 mb-4\">\r\n                                <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                                  <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                                  </svg>\r\n                                </div>\r\n                                <h6 className=\"font-bold text-blue-800 text-lg\">AI Explanation</h6>\r\n                              </div>\r\n                              <ContentRenderer content={explanations[question.name]} />\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n\r\n            {/* Enhanced Back Button */}\r\n            <div className=\"text-center\">\r\n              <button\r\n                className=\"px-8 py-4 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl font-semibold text-lg hover:from-gray-700 hover:to-gray-800 transform hover:scale-105 transition-all duration-300 shadow-lg flex items-center justify-center space-x-3 mx-auto\"\r\n                onClick={() => navigate(\"/user/dashboard\")}\r\n              >\r\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n                <span>Back to Dashboard</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,SAASC,2BAA2B,EAAEC,uBAAuB,QAAQ,wBAAwB;AAC7F,OAAOC,YAAY,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5D,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMwC,MAAM,GAAGpC,SAAS,CAAC,CAAC;EAC1B,MAAMqC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwC,IAAI,EAAEC,OAAO,CAAC,GAAG5C,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC+C,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM;IAAEmD;EAAK,CAAC,GAAGjD,WAAW,CAAEkD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM;IAAEE,KAAK;IAAEC;EAAO,CAAC,GAAGzC,aAAa,CAAC,CAAC;EACzC,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpD,MAAMyD,WAAW,GAAG3D,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF2C,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMkD,QAAQ,GAAG,MAAMrD,WAAW,CAAC;QAAEsD,MAAM,EAAEnB,MAAM,CAACoB;MAAG,CAAC,CAAC;MACzDnB,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;MACvB,IAAImD,QAAQ,CAACG,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACpB9B,YAAY,CAAC,EAAA6B,cAAA,GAAAJ,QAAQ,CAACM,IAAI,cAAAF,cAAA,uBAAbA,cAAA,CAAe9B,SAAS,KAAI,EAAE,CAAC;QAC5CD,WAAW,CAAC2B,QAAQ,CAACM,IAAI,CAAC;QAC1BlB,cAAc,CAAC,EAAAiB,eAAA,GAAAL,QAAQ,CAACM,IAAI,cAAAD,eAAA,uBAAbA,eAAA,CAAeE,QAAQ,KAAI,CAAC,CAAC;MAC9C,CAAC,MAAM;QACLrE,OAAO,CAACsE,KAAK,CAACR,QAAQ,CAAC9D,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOsE,KAAK,EAAE;MACdzB,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACsE,KAAK,CAACA,KAAK,CAACtE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC4C,MAAM,CAACoB,EAAE,EAAEnB,QAAQ,CAAC,CAAC;EAEzB,MAAM0B,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAEL;IAAK,CAAC,GAAG,MAAM9C,uBAAuB,CAACkD,OAAO,CAAC;IACvD,OAAOJ,IAAI;EACb,CAAC;EAED,MAAMM,eAAe,GAAGxE,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF;MACA,IAAI,CAACqD,IAAI,IAAI,CAACA,IAAI,CAACoB,GAAG,EAAE;QACtB3E,OAAO,CAACsE,KAAK,CAAC,sCAAsC,CAAC;QACrDxB,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAD,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMgE,eAAe,GAAG,EAAE;MAC1B,MAAMC,QAAQ,GAAG,EAAE;MAEnBzC,SAAS,CAAC0C,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAACE,UAAU,KAAK,WAAW,IAAIF,CAAC,CAACE,UAAU,KAAK,mBAAmB,EAAE;UACxEJ,QAAQ,CAACK,IAAI,CAACF,GAAG,CAAC;UAClBJ,eAAe,CAACM,IAAI,CAAC;YACnBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;YAChBC,cAAc,EAAEN,CAAC,CAACO,aAAa,IAAIP,CAAC,CAACQ,aAAa;YAClDC,UAAU,EAAEhD,eAAe,CAACwC,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMS,UAAU,GAAG,MAAMlB,oBAAoB,CAACK,eAAe,CAAC;MAC9D,MAAMc,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACX,OAAO,CAAEa,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACjD,MAAM,IAAI,OAAOiD,CAAC,CAACjD,MAAM,CAACkD,SAAS,KAAK,SAAS,EAAE;UACvDF,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAGQ,CAAC,CAACjD,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOiD,CAAC,CAACC,SAAS,KAAK,SAAS,EAAE;UAC3CF,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAG;YAAES,SAAS,EAAED,CAAC,CAACC,SAAS;YAAEC,MAAM,EAAEF,CAAC,CAACE,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,YAAY,GAAG,EAAE;MAEvB5D,SAAS,CAAC0C,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMiB,aAAa,GAAGzD,eAAe,CAACwC,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAACE,UAAU,KAAK,WAAW,IAAIF,CAAC,CAACE,UAAU,KAAK,mBAAmB,EAAE;UACxE,MAAM;YAAEW,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGH,MAAM,CAACX,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAMc,QAAQ,GAAG;YAAE,GAAGnB,CAAC;YAAES,UAAU,EAAES,aAAa;YAAEJ;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACZ,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACb,IAAI,CAACgB,QAAQ,CAAC;YAC3BF,YAAY,CAACd,IAAI,CAAC;cAChBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;cAChBC,cAAc,EAAEN,CAAC,CAACO,aAAa,IAAIP,CAAC,CAACQ,aAAa;cAClDC,UAAU,EAAES;YACd,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAIlB,CAAC,CAACE,UAAU,KAAK,SAAS,EAAE;UACrC,MAAMkB,UAAU,GAAGpB,CAAC,CAACQ,aAAa;UAClC,MAAMa,YAAY,GAAGrB,CAAC,CAACsB,OAAO,CAACF,UAAU,CAAC;UAC1C,MAAMG,SAAS,GAAGvB,CAAC,CAACsB,OAAO,CAACJ,aAAa,CAAC,IAAI,EAAE;UAEhD,MAAML,SAAS,GAAGO,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGnB,CAAC;YAAES,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIL,SAAS,EAAE;YACbE,cAAc,CAACZ,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACb,IAAI,CAACgB,QAAQ,CAAC;YAC3BF,YAAY,CAACd,IAAI,CAAC;cAChBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;cAChBC,cAAc,EAAEe,YAAY;cAC5BZ,UAAU,EAAEc;YACd,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;MAEF,MAAMC,OAAO,GAAGT,cAAc,CAACrB,MAAM,IAAIvC,QAAQ,CAACsE,YAAY,GAAG,MAAM,GAAG,MAAM;MAChF,MAAMC,UAAU,GAAG;QAAEX,cAAc;QAAEC,YAAY;QAAEQ;MAAQ,CAAC;MAE5D5D,SAAS,CAAC8D,UAAU,CAAC;MAErB,MAAM3C,QAAQ,GAAG,MAAMpD,SAAS,CAAC;QAC/BgG,IAAI,EAAE9D,MAAM,CAACoB,EAAE;QACftB,MAAM,EAAE+D,UAAU;QAClBlD,IAAI,EAAEA,IAAI,CAACoB;MACb,CAAC,CAAC;MAEF,IAAIb,QAAQ,CAACG,OAAO,EAAE;QACpBjB,OAAO,CAAC,QAAQ,CAAC;QACjB2D,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,IAAIC,KAAK,CAACN,OAAO,KAAK,MAAM,GAAGrF,SAAS,GAAGC,SAAS,CAAC,CAAC2F,IAAI,CAAC,CAAC;MAC9D,CAAC,MAAM;QACL9G,OAAO,CAACsE,KAAK,CAACR,QAAQ,CAAC9D,OAAO,CAAC;MACjC;MACA6C,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACdzB,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACsE,KAAK,CAACA,KAAK,CAACtE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACoC,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEU,MAAM,CAACoB,EAAE,EAAET,IAAI,EAAET,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE/E,MAAMkE,gBAAgB,GAAG,MAAAA,CAAO5B,QAAQ,EAAEE,cAAc,EAAEG,UAAU,EAAEwB,QAAQ,KAAK;IACjF,IAAI;MACFnE,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMkD,QAAQ,GAAG,MAAMzC,2BAA2B,CAAC;QAAE8D,QAAQ;QAAEE,cAAc;QAAEG,UAAU;QAAEwB;MAAS,CAAC,CAAC;MACtGnE,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAImD,QAAQ,CAACG,OAAO,EAAE;QACpBL,eAAe,CAAEqD,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAAC9B,QAAQ,GAAGrB,QAAQ,CAACoD;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLlH,OAAO,CAACsE,KAAK,CAACR,QAAQ,CAACQ,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdzB,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACsE,KAAK,CAACA,KAAK,CAACtE,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMmH,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG,CAAAlF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmC,QAAQ,KAAI,CAAC;IAC5CnB,cAAc,CAACkE,YAAY,CAAC;IAE5B,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCpE,cAAc,CAAEqE,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACLnE,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAAC+D,aAAa,CAAC;EAC9B,CAAC;EAEDlH,SAAS,CAAC,MAAM;IACd,IAAIgD,MAAM,IAAIJ,IAAI,KAAK,WAAW,EAAE;MAClCyE,aAAa,CAACnE,UAAU,CAAC;MACzBqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACvB,MAAM,EAAEJ,IAAI,EAAEM,UAAU,EAAEqB,eAAe,CAAC,CAAC;EAE/CvE,SAAS,CAAC,MAAM;IACd,IAAIyC,MAAM,CAACoB,EAAE,EAAE;MACbH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACjB,MAAM,CAACoB,EAAE,EAAEH,WAAW,CAAC,CAAC;EAE5B1D,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIkD,UAAU,EAAE;QACdmE,aAAa,CAACnE,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMoE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF5E,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMkD,QAAQ,GAAG,MAAM4D,KAAK,CAAC,kCAAkC,EAAE;QAC/DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAElE,MAAM,EAAEnB,MAAM,CAACoB;QAAG,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMI,IAAI,GAAG,MAAMN,QAAQ,CAACoE,IAAI,CAAC,CAAC;MAClC,IAAI9D,IAAI,CAACH,OAAO,EAAE;QAChBjE,OAAO,CAACiE,OAAO,CAACG,IAAI,CAACpE,OAAO,CAAC;QAC7B;QACA6D,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACL7D,OAAO,CAACsE,KAAK,CAACF,IAAI,CAACpE,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOsE,KAAK,EAAE;MACdtE,OAAO,CAACsE,KAAK,CAAC,iCAAiC,CAAC;IAClD,CAAC,SAAS;MACRzB,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,IAAI,CAAC4C,IAAI,EAAE;IACT,oBACE9B,OAAA;MAAK0G,SAAS,EAAC,qGAAqG;MAAAC,QAAA,eAClH3G,OAAA;QAAK0G,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBACxH3G,OAAA;UAAK0G,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/F3G,OAAA;YAAK0G,SAAS,EAAC,sBAAsB;YAACE,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,eAC3E3G,OAAA;cAAM8G,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,8JAA8J;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpH,OAAA;UAAI0G,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFpH,OAAA;UAAG0G,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvGpH,OAAA;UACE0G,SAAS,EAAC,mNAAmN;UAC7NW,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,QAAQ,CAAE;UAAAsF,QAAA,EACnC;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAO3G,QAAQ,gBACbT,OAAA;IAAK0G,SAAS,EAAC,oEAAoE;IAAAC,QAAA,GAEhFrF,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,WAAW,iBAC9CtB,OAAA;MAAK0G,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9E3G,OAAA;QAAK0G,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1C3G,OAAA;UAAK0G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C3G,OAAA;YAAK0G,SAAS,EAAC,oGAAoG;YAAAC,QAAA,eACjH3G,OAAA;cAAK0G,SAAS,EAAC,oBAAoB;cAACE,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAF,QAAA,eACzE3G,OAAA;gBAAM+G,CAAC,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpH,OAAA;YAAA2G,QAAA,gBACE3G,OAAA;cAAI0G,SAAS,EAAC,+FAA+F;cAAAC,QAAA,EAC1G,CAAAlG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkD,IAAI,KAAI;YAAY;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACLpH,OAAA;cAAG0G,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA9F,IAAI,KAAK,cAAc,iBACtBtB,OAAA,CAACZ,YAAY;MACXqB,QAAQ,EAAEA,QAAS;MACnBc,OAAO,EAAEA,OAAQ;MACjBmE,UAAU,EAAEA;IAAW;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF,EAEA9F,IAAI,KAAK,WAAW,KACnBX,SAAS,CAACqC,MAAM,KAAK,CAAC,gBACpBhD,OAAA;MAAK0G,SAAS,EAAC,sGAAsG;MAAAC,QAAA,eACnH3G,OAAA;QAAK0G,SAAS,EAAC,4GAA4G;QAAAC,QAAA,gBACzH3G,OAAA;UAAK0G,SAAS,EAAC,8HAA8H;UAAAC,QAAA,eAC3I3G,OAAA;YAAK0G,SAAS,EAAC,sBAAsB;YAACE,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,eAC3E3G,OAAA;cAAM8G,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mNAAmN;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpH,OAAA;UAAI0G,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EpH,OAAA;UAAG0G,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAE3D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJpH,OAAA;UACEqH,OAAO,EAAErB,mBAAoB;UAC7BU,SAAS,EAAC,iNAAiN;UAAAC,QAAA,EAC5N;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GAENzG,SAAS,CAACE,qBAAqB,CAAC,iBAC9Bb,OAAA,CAACF,YAAY;MACX4D,QAAQ,EAAE/C,SAAS,CAACE,qBAAqB,CAAE;MAC3CyG,aAAa,EAAEzG,qBAAsB;MACrC0G,cAAc,EAAE5G,SAAS,CAACqC,MAAO;MACjCwE,cAAc,EAAEzG,eAAe,CAACF,qBAAqB,CAAE;MACvD4G,cAAc,EAAGC,MAAM,IACrB1G,kBAAkB,CAAC;QACjB,GAAGD,eAAe;QAClB,CAACF,qBAAqB,GAAG6G;MAC3B,CAAC,CACF;MACDC,QAAQ,EAAEnG,WAAY;MACtBoG,QAAQ,EAAE,CAAA9F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,KAAI,SAAU;MAClCkE,SAAS,EAAE,CAAApH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkD,IAAI,KAAI,MAAO;MACpCmE,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAIjH,qBAAqB,KAAKF,SAAS,CAACqC,MAAM,GAAG,CAAC,EAAE;UAClDC,eAAe,CAAC,CAAC;QACnB,CAAC,MAAM;UACLnC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACFkH,UAAU,EAAEA,CAAA,KAAMjH,wBAAwB,CAACD,qBAAqB,GAAG,CAAC;IAAE;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CAEJ,CACF,EAEA9F,IAAI,KAAK,QAAQ,iBAChBtB,OAAA;MAAK0G,SAAS,EAAC,0EAA0E;MAAAC,QAAA,GACtF1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,iBAAI9E,OAAA,CAACT,QAAQ;QAACyC,KAAK,EAAEA,KAAM;QAACC,MAAM,EAAEA;MAAO;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExEpH,OAAA;QAAK0G,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrC3G,OAAA;UAAK0G,SAAS,EAAC,4FAA4F;UAAAC,QAAA,gBAEzG3G,OAAA;YAAK0G,SAAS,EAAG,kDACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACrB,+CAA+C,GAC/C,8CACL,EAAE;YAAA6B,QAAA,eACD3G,OAAA;cAAK0G,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3G,OAAA;gBAAK0G,SAAS,EAAG,kFACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACrB,gDAAgD,GAChD,+CACL,EAAE;gBAAA6B,QAAA,eACD3G,OAAA;kBACEgI,GAAG,EAAE/G,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAGzF,IAAI,GAAGC,IAAK;kBAC7C2I,GAAG,EAAEhH,MAAM,CAAC6D,OAAQ;kBACpB4B,SAAS,EAAC;gBAA0B;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpH,OAAA;gBAAI0G,SAAS,EAAG,2BACdzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,iBAChD,EAAE;gBAAA6B,QAAA,EACA1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,qBAAqB,GAAG;cAAgB;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACLpH,OAAA;gBAAG0G,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC7C1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACtB,wDAAwD,GACxD;cAAiE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpH,OAAA;YAAK0G,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB3G,OAAA;cAAK0G,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzD3G,OAAA;gBAAK0G,SAAS,EAAC,yGAAyG;gBAAAC,QAAA,gBACtH3G,OAAA;kBAAK0G,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,GACnDuB,IAAI,CAACC,KAAK,CAAE,CAAC,EAAA9H,qBAAA,GAAAY,MAAM,CAACoD,cAAc,cAAAhE,qBAAA,uBAArBA,qBAAA,CAAuB2C,MAAM,KAAI,CAAC,IAAIrC,SAAS,CAACqC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;gBAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpH,OAAA;kBAAK0G,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eAGNpH,OAAA;gBAAK0G,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,gBACzH3G,OAAA;kBAAK0G,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,GACpD,EAAArG,sBAAA,GAAAW,MAAM,CAACoD,cAAc,cAAA/D,sBAAA,uBAArBA,sBAAA,CAAuB0C,MAAM,KAAI,CAAC,EAAC,GAAC,EAACrC,SAAS,CAACqC,MAAM;gBAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACNpH,OAAA;kBAAK0G,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eAGNpH,OAAA;gBAAK0G,SAAS,EAAG,+CACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACrB,kEAAkE,GAClE,gEACL,EAAE;gBAAA6B,QAAA,gBACD3G,OAAA;kBAAK0G,SAAS,EAAG,2BACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,iBAClD,EAAE;kBAAA6B,QAAA,EACA1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG;gBAAO;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACNpH,OAAA;kBAAK0G,SAAS,EAAG,yBACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,iBAClD,EAAE;kBAAA6B,QAAA,EACA1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,YAAY,GAAI,QAAOrE,QAAQ,CAACsE,YAAa;gBAAC;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpH,OAAA;cAAK0G,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB3G,OAAA;gBAAK0G,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB3G,OAAA;kBAAK0G,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAC/D3G,OAAA;oBACE0G,SAAS,EAAG,8DACVzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACrB,gDAAgD,GAChD,+CACL,EAAE;oBACHsD,KAAK,EAAE;sBAAEpG,KAAK,EAAG,GAAG,CAAC,EAAAzB,sBAAA,GAAAU,MAAM,CAACoD,cAAc,cAAA9D,sBAAA,uBAArBA,sBAAA,CAAuByC,MAAM,KAAI,CAAC,IAAIrC,SAAS,CAACqC,MAAM,GAAI,GAAI;oBAAG;kBAAE;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNpH,OAAA;kBAAK0G,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD3G,OAAA;oBAAM0G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjDpH,OAAA;oBAAM0G,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,GAAC,YAC1C,EAACuB,IAAI,CAACC,KAAK,CAAE,CAAC,EAAA3H,sBAAA,GAAAS,MAAM,CAACoD,cAAc,cAAA7D,sBAAA,uBAArBA,sBAAA,CAAuBwC,MAAM,KAAI,CAAC,IAAIrC,SAAS,CAACqC,MAAM,GAAI,GAAG,CAAC,EAAC,GACzF;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPpH,OAAA;oBAAM0G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAI;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpH,OAAA;cAAK0G,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7D3G,OAAA;gBACE0G,SAAS,EAAC,sHAAsH;gBAChIW,OAAO,EAAEA,CAAA,KAAM9F,OAAO,CAAC,QAAQ,CAAE;gBAAAoF,QAAA,EAClC;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETpH,OAAA;gBACE0G,SAAS,EAAC,sHAAsH;gBAChIW,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,iBAAiB,CAAE;gBAAAsF,QAAA,EAC5C;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA9F,IAAI,KAAK,QAAQ,iBAChBtB,OAAA;MAAK0G,SAAS,EAAC,0EAA0E;MAAAC,QAAA,eACvF3G,OAAA;QAAK0G,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErC3G,OAAA;UAAK0G,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC3G,OAAA;YAAK0G,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5F3G,OAAA;cAAK0G,SAAS,EAAC,mHAAmH;cAAAC,QAAA,eAChI3G,OAAA;gBAAK0G,SAAS,EAAC,oBAAoB;gBAACE,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAF,QAAA,gBACzE3G,OAAA;kBAAM+G,CAAC,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CpH,OAAA;kBAAM8G,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,yIAAyI;kBAACC,QAAQ,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpH,OAAA;cAAI0G,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAAC;YAEnH;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAG0G,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAA2C;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpH,OAAA;UAAK0G,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BhG,SAAS,CAAC0H,GAAG,CAAC,CAAC3E,QAAQ,EAAE4E,KAAK,KAAK;YAClC,MAAMzE,aAAa,GAAGH,QAAQ,CAACF,UAAU,KAAK,SAAS,GACnDE,QAAQ,CAACI,aAAa,GACtBJ,QAAQ,CAACG,aAAa;YAC1B,MAAMM,SAAS,GAAGN,aAAa,KAAK9C,eAAe,CAACuH,KAAK,CAAC;YAE1D,oBACEtI,OAAA;cAAiB0G,SAAS,EAAC,2FAA2F;cAAAC,QAAA,gBAEpH3G,OAAA;gBAAK0G,SAAS,EAAG,sBACfvC,SAAS,GACL,+DAA+D,GAC/D,wDACL,EAAE;gBAAAwC,QAAA,eACD3G,OAAA;kBAAK0G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD3G,OAAA;oBAAK0G,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C3G,OAAA;sBAAK0G,SAAS,EAAG,gFACfvC,SAAS,GAAG,cAAc,GAAG,YAC9B,EAAE;sBAAAwC,QAAA,EACA2B,KAAK,GAAG;oBAAC;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACNpH,OAAA;sBAAI0G,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,GAAC,WACrC,EAAC2B,KAAK,GAAG,CAAC;oBAAA;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNpH,OAAA;oBAAM0G,SAAS,EAAG,wEAChBvC,SAAS,GACL,6BAA6B,GAC7B,yBACL,EAAE;oBAAAwC,QAAA,EACAxC,SAAS,gBACRnE,OAAA,CAAAE,SAAA;sBAAAyG,QAAA,gBACE3G,OAAA;wBAAK0G,SAAS,EAAC,SAAS;wBAACE,IAAI,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAF,QAAA,eAC9D3G,OAAA;0BAAM8G,QAAQ,EAAC,SAAS;0BAACC,CAAC,EAAC,oHAAoH;0BAACC,QAAQ,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClK,CAAC,eACNpH,OAAA;wBAAA2G,QAAA,EAAM;sBAAO;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eACpB,CAAC,gBAEHpH,OAAA,CAAAE,SAAA;sBAAAyG,QAAA,gBACE3G,OAAA;wBAAK0G,SAAS,EAAC,SAAS;wBAACE,IAAI,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAF,QAAA,eAC9D3G,OAAA;0BAAM8G,QAAQ,EAAC,SAAS;0BAACC,CAAC,EAAC,oMAAoM;0BAACC,QAAQ,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClP,CAAC,eACNpH,OAAA;wBAAA2G,QAAA,EAAM;sBAAS;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eACtB;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpH,OAAA;gBAAK0G,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB3G,OAAA;kBAAI0G,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,EAAEjD,QAAQ,CAACC;gBAAI;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAE5F1D,QAAQ,CAAC6E,KAAK,iBACbvI,OAAA;kBAAK0G,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB3G,OAAA;oBAAK0G,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eACpF3G,OAAA;sBACEgI,GAAG,EAAEtE,QAAQ,CAAC6E,KAAM;sBACpBN,GAAG,EAAC,UAAU;sBACdvB,SAAS,EAAC,8BAA8B;sBACxC0B,KAAK,EAAE;wBAAEI,SAAS,EAAE;sBAAQ;oBAAE;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGDpH,OAAA;kBAAK0G,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7C3G,OAAA;oBAAK0G,SAAS,EAAG,qCACfvC,SAAS,GACL,gEAAgE,GAChE,yDACL,EAAE;oBAAAwC,QAAA,gBACD3G,OAAA;sBAAK0G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/C3G,OAAA;wBAAK0G,SAAS,EAAG,yDACfvC,SAAS,GAAG,cAAc,GAAG,YAC9B,EAAE;wBAAAwC,QAAA,eACD3G,OAAA;0BAAK0G,SAAS,EAAC,oBAAoB;0BAACE,IAAI,EAAC,cAAc;0BAACC,OAAO,EAAC,WAAW;0BAAAF,QAAA,eACzE3G,OAAA;4BAAM8G,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,qDAAqD;4BAACC,QAAQ,EAAC;0BAAS;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpH,OAAA;wBAAI0G,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAC;sBAAW;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACNpH,OAAA;sBAAG0G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC7CjD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC9BE,QAAQ,CAACkB,OAAO,CAAC7D,eAAe,CAACuH,KAAK,CAAC,CAAC,IAAI,cAAc,GAC1DvH,eAAe,CAACuH,KAAK,CAAC,IAAI;oBAAc;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAENpH,OAAA;oBAAK0G,SAAS,EAAC,kGAAkG;oBAAAC,QAAA,gBAC/G3G,OAAA;sBAAK0G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/C3G,OAAA;wBAAK0G,SAAS,EAAC,oEAAoE;wBAAAC,QAAA,eACjF3G,OAAA;0BAAK0G,SAAS,EAAC,oBAAoB;0BAACE,IAAI,EAAC,cAAc;0BAACC,OAAO,EAAC,WAAW;0BAAAF,QAAA,eACzE3G,OAAA;4BAAM8G,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,oHAAoH;4BAACC,QAAQ,EAAC;0BAAS;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClK;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpH,OAAA;wBAAI0G,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAC;sBAAc;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC,eACNpH,OAAA;sBAAG0G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC7CjD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC9BE,QAAQ,CAACkB,OAAO,CAAClB,QAAQ,CAACI,aAAa,CAAC,GACvCJ,QAAQ,CAACG,aAAa,IAAIH,QAAQ,CAACI;oBAAc;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAACjD,SAAS,iBACTnE,OAAA;kBAAK0G,SAAS,EAAC,kGAAkG;kBAAAC,QAAA,gBAC/G3G,OAAA;oBACE0G,SAAS,EAAC,gOAAgO;oBAC1OW,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAC7B5B,QAAQ,CAACC,IAAI,EACbD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC7BE,QAAQ,CAACkB,OAAO,CAAClB,QAAQ,CAACI,aAAa,CAAC,GACvCJ,QAAQ,CAACG,aAAa,IAAIH,QAAQ,CAACI,aAAc,EACtDJ,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC7BE,QAAQ,CAACkB,OAAO,CAAC7D,eAAe,CAACuH,KAAK,CAAC,CAAC,IAAI,cAAc,GAC1DvH,eAAe,CAACuH,KAAK,CAAC,IAAI,cAAc,EAC5C5E,QAAQ,CAAC6E,KACX,CAAE;oBAAA5B,QAAA,gBAEF3G,OAAA;sBAAK0G,SAAS,EAAC,SAAS;sBAACE,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAF,QAAA,eAC9D3G,OAAA;wBAAM8G,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,wLAAwL;wBAACC,QAAQ,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtO,CAAC,eACNpH,OAAA;sBAAA2G,QAAA,EAAM;oBAAkB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,EAERlF,YAAY,CAACwB,QAAQ,CAACC,IAAI,CAAC,iBAC1B3D,OAAA;oBAAK0G,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBAChG3G,OAAA;sBAAK0G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/C3G,OAAA;wBAAK0G,SAAS,EAAC,mEAAmE;wBAAAC,QAAA,eAChF3G,OAAA;0BAAK0G,SAAS,EAAC,oBAAoB;0BAACE,IAAI,EAAC,cAAc;0BAACC,OAAO,EAAC,WAAW;0BAAAF,QAAA,eACzE3G,OAAA;4BAAM8G,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,kIAAkI;4BAACC,QAAQ,EAAC;0BAAS;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNpH,OAAA;wBAAI0G,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAC;sBAAc;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC,eACNpH,OAAA,CAACL,eAAe;sBAAC8I,OAAO,EAAEvG,YAAY,CAACwB,QAAQ,CAACC,IAAI;oBAAE;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GAzIEkB,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0IV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpH,OAAA;UAAK0G,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B3G,OAAA;YACE0G,SAAS,EAAC,2PAA2P;YACrQW,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,iBAAiB,CAAE;YAAAsF,QAAA,gBAE3C3G,OAAA;cAAK0G,SAAS,EAAC,SAAS;cAACE,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAF,QAAA,eAC9D3G,OAAA;gBAAM8G,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,uIAAuI;gBAACC,QAAQ,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL,CAAC,eACNpH,OAAA;cAAA2G,QAAA,EAAM;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,GACJ,IAAI;AACV;AAAChH,EAAA,CAjoBQD,SAAS;EAAA,QAMDpB,SAAS,EACPH,WAAW,EACXE,WAAW,EAKXD,WAAW,EAEFW,aAAa;AAAA;AAAAkJ,EAAA,GAfhCvI,SAAS;AAmoBlB,eAAeA,SAAS;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}