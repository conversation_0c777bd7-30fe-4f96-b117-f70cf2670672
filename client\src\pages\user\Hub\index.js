import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import './Hub.css';
import {
  FaHome,
  FaQuestionCircle,
  FaBook,
  FaChartLine,
  FaUser,
  FaComments,
  FaCreditCard,
  FaInfoCircle,
  FaGraduationCap,
  FaTrophy,
  FaStar,
  FaRocket
} from 'react-icons/fa';

const Hub = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);
  const [currentQuote, setCurrentQuote] = useState(0);

  const inspiringQuotes = [
    "Education is the most powerful weapon which you can use to change the world. - <PERSON>",
    "The future belongs to those who believe in the beauty of their dreams. - <PERSON>",
    "Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON>",
    "Your limitation—it's only your imagination.",
    "Great things never come from comfort zones.",
    "Dream it. Wish it. Do it."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [inspiringQuotes.length]);

  const navigationItems = [
    {
      title: 'Dashboard',
      description: 'Your learning overview',
      icon: FaHome,
      path: '/user/dashboard',
      color: 'from-blue-500 to-blue-600',
      hoverColor: 'from-blue-600 to-blue-700'
    },
    {
      title: 'Take Quiz',
      description: 'Test your knowledge',
      icon: FaQuestionCircle,
      path: '/user/quiz',
      color: 'from-green-500 to-green-600',
      hoverColor: 'from-green-600 to-green-700'
    },
    {
      title: 'Study Materials',
      description: 'Books, videos & notes',
      icon: FaBook,
      path: '/user/study-materials',
      color: 'from-purple-500 to-purple-600',
      hoverColor: 'from-purple-600 to-purple-700'
    },
    {
      title: 'Reports',
      description: 'Track your progress',
      icon: FaChartLine,
      path: '/user/reports',
      color: 'from-orange-500 to-orange-600',
      hoverColor: 'from-orange-600 to-orange-700'
    },
    {
      title: 'Profile',
      description: 'Manage your account',
      icon: FaUser,
      path: '/user/profile',
      color: 'from-indigo-500 to-indigo-600',
      hoverColor: 'from-indigo-600 to-indigo-700'
    },
    {
      title: 'Forum',
      description: 'Connect with peers',
      icon: FaComments,
      path: '/user/forum',
      color: 'from-pink-500 to-pink-600',
      hoverColor: 'from-pink-600 to-pink-700'
    },
    {
      title: 'Ranking',
      description: 'See your position',
      icon: FaTrophy,
      path: '/user/ranking',
      color: 'from-yellow-500 to-yellow-600',
      hoverColor: 'from-yellow-600 to-yellow-700'
    },
    {
      title: 'Plans',
      description: 'Upgrade your learning',
      icon: FaCreditCard,
      path: '/user/plans',
      color: 'from-emerald-500 to-emerald-600',
      hoverColor: 'from-emerald-600 to-emerald-700'
    },
    {
      title: 'About Us',
      description: 'Learn about our mission',
      icon: FaInfoCircle,
      path: '/user/about',
      color: 'from-cyan-500 to-cyan-600',
      hoverColor: 'from-cyan-600 to-cyan-700'
    }
  ];



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="text-center mb-12 animate-fadeInUp">
          <div className="inline-flex items-center gap-3 mb-4 animate-scale-pulse">
            <FaRocket className="text-4xl text-blue-600 animate-bounce-gentle" />
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent animate-gradient">
              Welcome Back, {user?.name || 'Sawiti'}!
            </h1>
            <FaStar className="text-4xl text-yellow-500 animate-rotate-gentle" />
          </div>

          <p className="text-2xl font-semibold text-gray-700 mb-6 animate-float">
            Ready to shine today? ✨
          </p>

          {/* Inspiring Quote */}
          <div
            key={currentQuote}
            className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 max-w-4xl mx-auto animate-fadeInUp animate-pulse-glow"
          >
            <p className="text-lg text-gray-700 italic font-medium leading-relaxed">
              "{inspiringQuotes[currentQuote]}"
            </p>
          </div>
        </div>

        {/* Navigation Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {navigationItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <div
                key={item.title}
                className={`group cursor-pointer hub-card animate-fadeInUp animate-delay-${(index + 1) * 100}`}
                onClick={() => navigate(item.path)}
              >
                <div className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${item.color} group-hover:${item.hoverColor} p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform`}>
                  {/* Shine Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-4">
                      <IconComponent className="text-4xl text-white drop-shadow-lg animate-float" />
                      <div className="w-2 h-2 bg-white/30 rounded-full group-hover:bg-white/50 transition-colors duration-300"></div>
                    </div>

                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-white/90 transition-colors duration-300">
                      {item.title}
                    </h3>

                    <p className="text-white/80 text-sm group-hover:text-white/70 transition-colors duration-300">
                      {item.description}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Bottom Decoration */}
        <div className="text-center mt-16 animate-fadeInUp animate-delay-600">
          <div className="inline-flex items-center gap-2 text-gray-500">
            <FaGraduationCap className="text-2xl animate-bounce-gentle" />
            <span className="text-lg font-medium">Your Learning Journey Continues</span>
            <FaGraduationCap className="text-2xl animate-bounce-gentle" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hub;
