{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\"\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Render MCQ Question\n  const renderMCQ = () => {\n    if (!question.options) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-500 text-center p-4 w-full\",\n        children: \"No options available for this question.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 w-full max-w-none\",\n      style: {\n        width: '100%'\n      },\n      children: Object.entries(question.options).map(([key, value]) => {\n        const optionKey = String(key).trim();\n        const optionValue = String(value).trim();\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.1 * parseInt(optionKey.charCodeAt(0) - 65),\n            type: \"spring\",\n            stiffness: 120\n          },\n          className: \"w-full\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            onClick: () => handleAnswerSelect(optionKey),\n            whileHover: {\n              scale: 1.01\n            },\n            whileTap: {\n              scale: 0.99\n            },\n            className: `w-full p-3 rounded-xl text-left transition-all duration-300 flex items-center space-x-4 border-2 ${currentAnswer === optionKey ? 'bg-blue-600 text-white border-blue-600 shadow-lg' : 'bg-blue-50 hover:bg-blue-100 border-blue-200 hover:border-blue-400 shadow-sm hover:shadow-md'}`,\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-12 h-10 rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0 transition-all duration-300 ${currentAnswer === optionKey ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-700 border-2 border-blue-200'}`,\n              children: [optionKey, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-base font-medium leading-relaxed flex-1 ${currentAnswer === optionKey ? 'text-white' : 'text-blue-900'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this), currentAnswer === optionKey && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 200,\n                damping: 15\n              },\n              className: \"w-6 h-6 text-white flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render Fill-in-the-blank Question\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-50 rounded-xl p-6 border-2 border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-blue-800 font-semibold mb-4 text-lg\",\n        children: \"Your Answer:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentAnswer,\n        onChange: e => handleAnswerSelect(e.target.value),\n        placeholder: \"Type your answer here...\",\n        className: \"w-full p-4 border-2 border-blue-300 rounded-lg text-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 bg-white shadow-sm font-medium\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), currentAnswer && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-green-50 rounded-xl p-4 border-2 border-green-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-700 font-semibold\",\n          children: [\"Answer recorded: \\\"\", currentAnswer, \"\\\"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n\n  // Render Image-based Question\n  const renderImageQuestion = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: question.imageUrl,\n        alt: \"Question diagram\",\n        className: \"max-w-full max-h-96 rounded-lg shadow-lg border border-gray-200 mx-auto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this), question.options ? renderMCQ() : renderFillBlank()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 pb-24\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-64 bg-red-300 rounded-full h-4 mb-2 mx-auto border-2 border-red-500\",\n            style: {\n              backgroundColor: 'red',\n              height: '16px'\n            },\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"h-full bg-green-600 rounded-full\",\n              style: {\n                backgroundColor: 'green',\n                height: '100%'\n              },\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progressPercentage}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg font-semibold text-gray-800\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 bg-blue-50 py-2 px-4 rounded-lg border border-blue-200\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-blue-900\",\n              style: {\n                color: 'blue',\n                fontSize: '24px'\n              },\n              children: examTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `inline-block px-4 py-2 rounded-lg font-mono font-bold text-lg ${timeLeft <= 60 ? 'bg-red-100 text-red-700 border border-red-200' : timeLeft <= 300 ? 'bg-orange-100 text-orange-700 border border-orange-200' : 'bg-green-100 text-green-700 border border-green-200'}`,\n            children: formatTime(timeLeft)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"h-full bg-blue-600 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progressPercentage}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex max-w-6xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 bg-blue-900 min-h-screen flex flex-col items-center py-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-800 rounded-lg cursor-pointer hover:bg-blue-700 transition-colors\",\n          title: \"Dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-700 rounded-lg cursor-pointer\",\n          title: \"Quiz\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 px-8 py-8 pb-16 w-full\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3,\n              ease: \"easeOut\"\n            },\n            className: \"w-full\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-8 w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-8 w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-3xl font-bold text-gray-900 leading-relaxed mb-4 w-full\",\n                  children: question.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6 w-full\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.image,\n                    alt: \"Question\",\n                    className: \"max-w-full max-h-80 rounded-lg border border-gray-200 mx-auto\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-20 w-full\",\n                style: {\n                  width: '100%',\n                  paddingBottom: '2rem'\n                },\n                children: [question.answerType === \"Options\" && renderMCQ(), (question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank(), question.imageUrl && renderImageQuestion()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, questionIndex, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: onPrevious,\n          disabled: questionIndex === 0,\n          whileHover: questionIndex !== 0 ? {\n            scale: 1.02\n          } : {},\n          whileTap: questionIndex !== 0 ? {\n            scale: 0.98\n          } : {},\n          className: `flex items-center space-x-3 px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-200 ${questionIndex === 0 ? 'opacity-40 cursor-not-allowed bg-gray-200 text-gray-500' : 'bg-blue-500 text-white hover:bg-blue-600 shadow-xl hover:shadow-2xl'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: onNext,\n          disabled: !isAnswered,\n          whileHover: isAnswered ? {\n            scale: 1.02\n          } : {},\n          whileTap: isAnswered ? {\n            scale: 0.98\n          } : {},\n          className: `flex items-center space-x-3 px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-200 ${!isAnswered ? 'opacity-40 cursor-not-allowed bg-gray-200 text-gray-500' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white shadow-xl hover:shadow-2xl' : 'bg-blue-600 hover:bg-blue-700 text-white shadow-xl hover:shadow-2xl'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "username", "onNext", "onPrevious", "examTitle", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "handleAnswerSelect", "answer", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "progressPercentage", "renderMCQ", "options", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "Object", "entries", "map", "key", "value", "optionKey", "String", "trim", "optionValue", "div", "initial", "opacity", "y", "animate", "transition", "delay", "parseInt", "charCodeAt", "type", "stiffness", "button", "onClick", "whileHover", "scale", "whileTap", "damping", "fill", "viewBox", "fillRule", "d", "clipRule", "renderFillBlank", "onChange", "e", "target", "placeholder", "renderImageQuestion", "imageUrl", "src", "alt", "backgroundColor", "height", "duration", "color", "fontSize", "title", "mode", "exit", "ease", "name", "image", "paddingBottom", "answerType", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Render MCQ Question\n  const renderMCQ = () => {\n    if (!question.options) {\n      return <div className=\"text-red-500 text-center p-4 w-full\">No options available for this question.</div>;\n    }\n\n    return (\n      <div className=\"space-y-4 w-full max-w-none\" style={{width: '100%'}}>\n        {Object.entries(question.options).map(([key, value]) => {\n          const optionKey = String(key).trim();\n          const optionValue = String(value).trim();\n\n          return (\n            <motion.div\n              key={optionKey}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{\n                delay: 0.1 * parseInt(optionKey.charCodeAt(0) - 65),\n                type: \"spring\",\n                stiffness: 120\n              }}\n              className=\"w-full\"\n            >\n              <motion.button\n                onClick={() => handleAnswerSelect(optionKey)}\n                whileHover={{ scale: 1.01 }}\n                whileTap={{ scale: 0.99 }}\n                className={`w-full p-3 rounded-xl text-left transition-all duration-300 flex items-center space-x-4 border-2 ${\n                  currentAnswer === optionKey\n                    ? 'bg-blue-600 text-white border-blue-600 shadow-lg'\n                    : 'bg-blue-50 hover:bg-blue-100 border-blue-200 hover:border-blue-400 shadow-sm hover:shadow-md'\n                }`}\n                style={{width: '100%'}}\n              >\n                {/* Letter badge - positioned outside */}\n                <div className={`w-12 h-10 rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0 transition-all duration-300 ${\n                  currentAnswer === optionKey\n                    ? 'bg-white text-blue-600'\n                    : 'bg-blue-100 text-blue-700 border-2 border-blue-200'\n                }`}>\n                  {optionKey})\n                </div>\n\n                {/* Answer text */}\n                <span className={`text-base font-medium leading-relaxed flex-1 ${\n                  currentAnswer === optionKey ? 'text-white' : 'text-blue-900'\n                }`}>\n                  {optionValue}\n                </span>\n\n                {/* Check icon for selected */}\n                {currentAnswer === optionKey && (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ type: \"spring\", stiffness: 200, damping: 15 }}\n                    className=\"w-6 h-6 text-white flex-shrink-0\"\n                  >\n                    <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </motion.div>\n                )}\n              </motion.button>\n            </motion.div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render Fill-in-the-blank Question\n  const renderFillBlank = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-blue-50 rounded-xl p-6 border-2 border-blue-200\">\n        <label className=\"block text-blue-800 font-semibold mb-4 text-lg\">\n          Your Answer:\n        </label>\n        <input\n          type=\"text\"\n          value={currentAnswer}\n          onChange={(e) => handleAnswerSelect(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full p-4 border-2 border-blue-300 rounded-lg text-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 bg-white shadow-sm font-medium\"\n        />\n      </div>\n      {currentAnswer && (\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-green-50 rounded-xl p-4 border-2 border-green-200\"\n        >\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <p className=\"text-green-700 font-semibold\">\n              Answer recorded: \"{currentAnswer}\"\n            </p>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n\n  // Render Image-based Question\n  const renderImageQuestion = () => (\n    <div className=\"space-y-6\">\n      {question.imageUrl && (\n        <div className=\"text-center mb-6\">\n          <img\n            src={question.imageUrl}\n            alt=\"Question diagram\"\n            className=\"max-w-full max-h-96 rounded-lg shadow-lg border border-gray-200 mx-auto\"\n          />\n        </div>\n      )}\n\n      {question.options ? renderMCQ() : renderFillBlank()}\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 pb-24\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          {/* Header Content */}\n          <div className=\"text-center mb-4\">\n            {/* Progressive filling line */}\n            <div className=\"w-64 bg-red-300 rounded-full h-4 mb-2 mx-auto border-2 border-red-500\" style={{backgroundColor: 'red', height: '16px'}}>\n              <motion.div\n                className=\"h-full bg-green-600 rounded-full\"\n                style={{backgroundColor: 'green', height: '100%'}}\n                initial={{ width: 0 }}\n                animate={{ width: `${progressPercentage}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n\n            {/* Question counter - directly after progress line */}\n            <div className=\"mb-3\">\n              <span className=\"text-lg font-semibold text-gray-800\">Question {questionIndex + 1} of {totalQuestions}</span>\n            </div>\n\n            {/* Exam Title - below question counter */}\n            <div className=\"mb-4 bg-blue-50 py-2 px-4 rounded-lg border border-blue-200\">\n              <h2 className=\"text-2xl font-bold text-blue-900\" style={{color: 'blue', fontSize: '24px'}}>{examTitle}</h2>\n            </div>\n\n            {/* Timer - below exam title */}\n            <div className={`inline-block px-4 py-2 rounded-lg font-mono font-bold text-lg ${\n              timeLeft <= 60 ? 'bg-red-100 text-red-700 border border-red-200' :\n              timeLeft <= 300 ? 'bg-orange-100 text-orange-700 border border-orange-200' :\n              'bg-green-100 text-green-700 border border-green-200'\n            }`}>\n              {formatTime(timeLeft)}\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mb-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"h-full bg-blue-600 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progressPercentage}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Sidebar and Main Content */}\n      <div className=\"flex max-w-6xl mx-auto\">\n        {/* Compact Sidebar */}\n        <div className=\"w-16 bg-blue-900 min-h-screen flex flex-col items-center py-6 space-y-4\">\n          {/* Navigation Icons */}\n          <div className=\"p-2 bg-blue-800 rounded-lg cursor-pointer hover:bg-blue-700 transition-colors\" title=\"Dashboard\">\n            <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\" />\n            </svg>\n          </div>\n\n          <div className=\"p-2 bg-blue-700 rounded-lg cursor-pointer\" title=\"Quiz\">\n            <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n\n\n        </div>\n\n        {/* Main Question Panel */}\n        <div className=\"flex-1 px-8 py-8 pb-16 w-full\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={questionIndex}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3, ease: \"easeOut\" }}\n              className=\"w-full\"\n            >\n              {/* Question Card */}\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8 w-full\">\n                {/* Question Header */}\n                <div className=\"mb-8 w-full\">\n                  <h2 className=\"text-3xl font-bold text-gray-900 leading-relaxed mb-4 w-full\">\n                    {question.name}\n                  </h2>\n                  {question.image && (\n                    <div className=\"mb-6 w-full\">\n                      <img\n                        src={question.image}\n                        alt=\"Question\"\n                        className=\"max-w-full max-h-80 rounded-lg border border-gray-200 mx-auto\"\n                      />\n                    </div>\n                  )}\n                </div>\n\n                {/* Question Options */}\n                <div className=\"mb-20 w-full\" style={{width: '100%', paddingBottom: '2rem'}}>\n                  {question.answerType === \"Options\" && renderMCQ()}\n                  {(question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank()}\n                  {question.imageUrl && renderImageQuestion()}\n                </div>\n              </div>\n            </motion.div>\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Bottom Navigation - Centered with small gap */}\n      <div className=\"fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50\">\n        <div className=\"flex items-center justify-center gap-4\">\n          {/* Previous Button */}\n          <motion.button\n            onClick={onPrevious}\n            disabled={questionIndex === 0}\n            whileHover={questionIndex !== 0 ? { scale: 1.02 } : {}}\n            whileTap={questionIndex !== 0 ? { scale: 0.98 } : {}}\n            className={`flex items-center space-x-3 px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-200 ${\n              questionIndex === 0\n                ? 'opacity-40 cursor-not-allowed bg-gray-200 text-gray-500'\n                : 'bg-blue-500 text-white hover:bg-blue-600 shadow-xl hover:shadow-2xl'\n            }`}\n          >\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n            </svg>\n            <span>Previous</span>\n          </motion.button>\n\n          {/* Next/Submit Button */}\n          <motion.button\n            onClick={onNext}\n            disabled={!isAnswered}\n            whileHover={isAnswered ? { scale: 1.02 } : {}}\n            whileTap={isAnswered ? { scale: 0.98 } : {}}\n            className={`flex items-center space-x-3 px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-200 ${\n              !isAnswered\n                ? 'opacity-40 cursor-not-allowed bg-gray-200 text-gray-500'\n                : questionIndex === totalQuestions - 1\n                ? 'bg-green-600 hover:bg-green-700 text-white shadow-xl hover:shadow-2xl'\n                : 'bg-blue-600 hover:bg-blue-700 text-white shadow-xl hover:shadow-2xl'\n            }`}\n          >\n            <span>\n              {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'}\n            </span>\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              {questionIndex === totalQuestions - 1 ? (\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              ) : (\n                <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n              )}\n            </svg>\n          </motion.button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,QAAQ,GAAG,SAAS;EACpBC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAACU,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdkB,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrCJ,gBAAgB,CAACI,MAAM,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACY,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACxB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,MAAMwB,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAAC1B,QAAQ,CAAC2B,OAAO,EAAE;MACrB,oBAAO7B,OAAA;QAAK8B,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAC3G;IAEA,oBACEnC,OAAA;MAAK8B,SAAS,EAAC,6BAA6B;MAACM,KAAK,EAAE;QAACC,KAAK,EAAE;MAAM,CAAE;MAAAN,QAAA,EACjEO,MAAM,CAACC,OAAO,CAACrC,QAAQ,CAAC2B,OAAO,CAAC,CAACW,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QACtD,MAAMC,SAAS,GAAGC,MAAM,CAACH,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC;QACpC,MAAMC,WAAW,GAAGF,MAAM,CAACF,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;QAExC,oBACE7C,OAAA,CAACH,MAAM,CAACkD,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YACVC,KAAK,EAAE,GAAG,GAAGC,QAAQ,CAACX,SAAS,CAACY,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACnDC,IAAI,EAAE,QAAQ;YACdC,SAAS,EAAE;UACb,CAAE;UACF3B,SAAS,EAAC,QAAQ;UAAAC,QAAA,eAElB/B,OAAA,CAACH,MAAM,CAAC6D,MAAM;YACZC,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC0B,SAAS,CAAE;YAC7CiB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1B/B,SAAS,EAAG,oGACVjB,aAAa,KAAK8B,SAAS,GACvB,kDAAkD,GAClD,8FACL,EAAE;YACHP,KAAK,EAAE;cAACC,KAAK,EAAE;YAAM,CAAE;YAAAN,QAAA,gBAGvB/B,OAAA;cAAK8B,SAAS,EAAG,uHACfjB,aAAa,KAAK8B,SAAS,GACvB,wBAAwB,GACxB,oDACL,EAAE;cAAAZ,QAAA,GACAY,SAAS,EAAC,GACb;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAGNnC,OAAA;cAAM8B,SAAS,EAAG,gDAChBjB,aAAa,KAAK8B,SAAS,GAAG,YAAY,GAAG,eAC9C,EAAE;cAAAZ,QAAA,EACAe;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAGNtB,aAAa,KAAK8B,SAAS,iBAC1B3C,OAAA,CAACH,MAAM,CAACkD,GAAG;cACTC,OAAO,EAAE;gBAAEa,KAAK,EAAE;cAAE,CAAE;cACtBV,OAAO,EAAE;gBAAEU,KAAK,EAAE;cAAE,CAAE;cACtBT,UAAU,EAAE;gBAAEI,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEM,OAAO,EAAE;cAAG,CAAE;cAC5DjC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAE5C/B,OAAA;gBAAKgE,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlC,QAAA,eAC1C/B,OAAA;kBAAMkE,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,oHAAoH;kBAACC,QAAQ,EAAC;gBAAS;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY;QAAC,GAlDXQ,SAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDJ,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAGA,CAAA,kBACtBrE,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/B,OAAA;MAAK8B,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjE/B,OAAA;QAAO8B,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAElE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRnC,OAAA;QACEwD,IAAI,EAAC,MAAM;QACXd,KAAK,EAAE7B,aAAc;QACrByD,QAAQ,EAAGC,CAAC,IAAKtD,kBAAkB,CAACsD,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;QACpD+B,WAAW,EAAC,0BAA0B;QACtC3C,SAAS,EAAC;MAAiK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5K,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACLtB,aAAa,iBACZb,OAAA,CAACH,MAAM,CAACkD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BpB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eAEhE/B,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C/B,OAAA;UAAK8B,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjF/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eACzE/B,OAAA;cAAMkE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnC,OAAA;UAAG8B,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAAC,qBACxB,EAAClB,aAAa,EAAC,IACnC;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAMuC,mBAAmB,GAAGA,CAAA,kBAC1B1E,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,GACvB7B,QAAQ,CAACyE,QAAQ,iBAChB3E,OAAA;MAAK8B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B/B,OAAA;QACE4E,GAAG,EAAE1E,QAAQ,CAACyE,QAAS;QACvBE,GAAG,EAAC,kBAAkB;QACtB/C,SAAS,EAAC;MAAyE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAEAjC,QAAQ,CAAC2B,OAAO,GAAGD,SAAS,CAAC,CAAC,GAAGyC,eAAe,CAAC,CAAC;EAAA;IAAArC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CACN;EAED,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5C/B,OAAA;MAAK8B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D/B,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1C/B,OAAA;UAAK8B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAE/B/B,OAAA;YAAK8B,SAAS,EAAC,uEAAuE;YAACM,KAAK,EAAE;cAAC0C,eAAe,EAAE,KAAK;cAAEC,MAAM,EAAE;YAAM,CAAE;YAAAhD,QAAA,eACrI/B,OAAA,CAACH,MAAM,CAACkD,GAAG;cACTjB,SAAS,EAAC,kCAAkC;cAC5CM,KAAK,EAAE;gBAAC0C,eAAe,EAAE,OAAO;gBAAEC,MAAM,EAAE;cAAM,CAAE;cAClD/B,OAAO,EAAE;gBAAEX,KAAK,EAAE;cAAE,CAAE;cACtBc,OAAO,EAAE;gBAAEd,KAAK,EAAG,GAAEV,kBAAmB;cAAG,CAAE;cAC7CyB,UAAU,EAAE;gBAAE4B,QAAQ,EAAE;cAAI;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnC,OAAA;YAAK8B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB/B,OAAA;cAAM8B,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,WAAS,EAAC5B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eAGNnC,OAAA;YAAK8B,SAAS,EAAC,6DAA6D;YAAAC,QAAA,eAC1E/B,OAAA;cAAI8B,SAAS,EAAC,kCAAkC;cAACM,KAAK,EAAE;gBAAC6C,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAM,CAAE;cAAAnD,QAAA,EAAEpB;YAAS;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eAGNnC,OAAA;YAAK8B,SAAS,EAAG,iEACfvB,QAAQ,IAAI,EAAE,GAAG,+CAA+C,GAChEA,QAAQ,IAAI,GAAG,GAAG,wDAAwD,GAC1E,qDACD,EAAE;YAAAwB,QAAA,EACAZ,UAAU,CAACZ,QAAQ;UAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB/B,OAAA;YAAK8B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClD/B,OAAA,CAACH,MAAM,CAACkD,GAAG;cACTjB,SAAS,EAAC,iCAAiC;cAC3CkB,OAAO,EAAE;gBAAEX,KAAK,EAAE;cAAE,CAAE;cACtBc,OAAO,EAAE;gBAAEd,KAAK,EAAG,GAAEV,kBAAmB;cAAG,CAAE;cAC7CyB,UAAU,EAAE;gBAAE4B,QAAQ,EAAE;cAAI;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC/B,OAAA;QAAK8B,SAAS,EAAC,yEAAyE;QAAAC,QAAA,gBAEtF/B,OAAA;UAAK8B,SAAS,EAAC,+EAA+E;UAACqD,KAAK,EAAC,WAAW;UAAApD,QAAA,eAC9G/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eACzE/B,OAAA;cAAMmE,CAAC,EAAC;YAAkM;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnC,OAAA;UAAK8B,SAAS,EAAC,2CAA2C;UAACqD,KAAK,EAAC,MAAM;UAAApD,QAAA,eACrE/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eACzE/B,OAAA;cAAMkE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,+IAA+I;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7L;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CAAC,eAGNnC,OAAA;QAAK8B,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5C/B,OAAA,CAACF,eAAe;UAACsF,IAAI,EAAC,MAAM;UAAArD,QAAA,eAC1B/B,OAAA,CAACH,MAAM,CAACkD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BmC,IAAI,EAAE;cAAEpC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BE,UAAU,EAAE;cAAE4B,QAAQ,EAAE,GAAG;cAAEM,IAAI,EAAE;YAAU,CAAE;YAC/CxD,SAAS,EAAC,QAAQ;YAAAC,QAAA,eAGlB/B,OAAA;cAAK8B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBAE/E/B,OAAA;gBAAK8B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B/B,OAAA;kBAAI8B,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EACzE7B,QAAQ,CAACqF;gBAAI;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,EACJjC,QAAQ,CAACsF,KAAK,iBACbxF,OAAA;kBAAK8B,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1B/B,OAAA;oBACE4E,GAAG,EAAE1E,QAAQ,CAACsF,KAAM;oBACpBX,GAAG,EAAC,UAAU;oBACd/C,SAAS,EAAC;kBAA+D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNnC,OAAA;gBAAK8B,SAAS,EAAC,cAAc;gBAACM,KAAK,EAAE;kBAACC,KAAK,EAAE,MAAM;kBAAEoD,aAAa,EAAE;gBAAM,CAAE;gBAAA1D,QAAA,GACzE7B,QAAQ,CAACwF,UAAU,KAAK,SAAS,IAAI9D,SAAS,CAAC,CAAC,EAChD,CAAC1B,QAAQ,CAACwF,UAAU,KAAK,WAAW,IAAIxF,QAAQ,CAACwF,UAAU,KAAK,mBAAmB,KAAKrB,eAAe,CAAC,CAAC,EACzGnE,QAAQ,CAACyE,QAAQ,IAAID,mBAAmB,CAAC,CAAC;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA/BDhC,aAAa;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE/B,OAAA;QAAK8B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErD/B,OAAA,CAACH,MAAM,CAAC6D,MAAM;UACZC,OAAO,EAAEjD,UAAW;UACpBiF,QAAQ,EAAExF,aAAa,KAAK,CAAE;UAC9ByD,UAAU,EAAEzD,aAAa,KAAK,CAAC,GAAG;YAAE0D,KAAK,EAAE;UAAK,CAAC,GAAG,CAAC,CAAE;UACvDC,QAAQ,EAAE3D,aAAa,KAAK,CAAC,GAAG;YAAE0D,KAAK,EAAE;UAAK,CAAC,GAAG,CAAC,CAAE;UACrD/B,SAAS,EAAG,oGACV3B,aAAa,KAAK,CAAC,GACf,yDAAyD,GACzD,qEACL,EAAE;UAAA4B,QAAA,gBAEH/B,OAAA;YAAK8B,SAAS,EAAC,SAAS;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eAC9D/B,OAAA;cAAMkE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mHAAmH;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjK,CAAC,eACNnC,OAAA;YAAA+B,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGhBnC,OAAA,CAACH,MAAM,CAAC6D,MAAM;UACZC,OAAO,EAAElD,MAAO;UAChBkF,QAAQ,EAAE,CAAC5E,UAAW;UACtB6C,UAAU,EAAE7C,UAAU,GAAG;YAAE8C,KAAK,EAAE;UAAK,CAAC,GAAG,CAAC,CAAE;UAC9CC,QAAQ,EAAE/C,UAAU,GAAG;YAAE8C,KAAK,EAAE;UAAK,CAAC,GAAG,CAAC,CAAE;UAC5C/B,SAAS,EAAG,oGACV,CAACf,UAAU,GACP,yDAAyD,GACzDZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GACpC,uEAAuE,GACvE,qEACL,EAAE;UAAA2B,QAAA,gBAEH/B,OAAA;YAAA+B,QAAA,EACG5B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;UAAM;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACPnC,OAAA;YAAK8B,SAAS,EAAC,SAAS;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,EAC7D5B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCJ,OAAA;cAAMkE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAErKnC,OAAA;cAAMkE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACrK;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAlUIX,YAAY;AAAA2F,EAAA,GAAZ3F,YAAY;AAoUlB,eAAeA,YAAY;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}