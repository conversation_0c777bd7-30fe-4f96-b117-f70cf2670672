{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\"\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Render MCQ Question with modern, clean design\n  const renderMCQ = () => {\n    if (!question.options) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-8 bg-red-50 rounded-xl border border-red-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-600 text-lg font-medium\",\n          children: \"No options available for this question.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this);\n    }\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: Object.entries(question.options).map(([key, value], index) => {\n        const optionKey = String(key).trim();\n        const optionValue = String(value).trim();\n        const label = optionLabels[index] || optionKey;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"group\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleAnswerSelect(optionKey),\n            className: `w-full p-4 rounded-xl text-left transition-all duration-300 flex items-center space-x-4 border-2 ${currentAnswer === optionKey ? 'bg-blue-600 text-blue-100 border-blue-600 shadow-lg transform scale-[1.02]' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg transition-all duration-300 ${currentAnswer === optionKey ? 'bg-blue-100 text-blue-700' : 'bg-blue-100 text-blue-700 group-hover:bg-blue-200'}`,\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `flex-1 text-lg leading-relaxed ${currentAnswer === optionKey ? 'text-blue-100 font-medium' : 'text-gray-800'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render Fill-in-the-blank Question with modern design\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-blue-800 font-semibold mb-4 text-lg\",\n        children: \"Your Answer:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentAnswer,\n        onChange: e => handleAnswerSelect(e.target.value),\n        placeholder: \"Type your answer here...\",\n        className: \"w-full p-4 border-2 border-blue-300 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 bg-white text-lg transition-all duration-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), currentAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-700 font-medium text-lg\",\n          children: [\"Answer recorded: \\\"\", currentAnswer, \"\\\"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n\n  // Render Image-based Question with enhanced design\n  const renderImageQuestion = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: question.imageUrl,\n          alt: \"Question diagram\",\n          className: \"max-w-full max-h-96 rounded-lg mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this), question.options ? renderMCQ() : renderFillBlank()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/80 backdrop-blur-sm shadow-sm border-b border-blue-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-6 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-100 font-bold text-lg\",\n                children: questionIndex + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg font-semibold text-gray-800\",\n                children: [questionIndex + 1, \" of \", totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-4 py-2 rounded-lg font-mono font-bold text-lg shadow-md ${timeLeft <= 60 ? 'bg-red-500 text-red-100' : timeLeft <= 300 ? 'bg-orange-500 text-orange-100' : 'bg-green-500 text-green-100'}`,\n            children: formatTime(timeLeft)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2 shadow-inner\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-700 shadow-sm\",\n              style: {\n                width: `${progressPercentage}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-1 text-xs text-gray-600\",\n            children: [Math.round(progressPercentage), \"% Complete\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-6 py-6 pb-28\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-100 p-6 md:p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 leading-relaxed mb-4\",\n            children: question.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block p-3 bg-white rounded-xl shadow-lg border border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: question.image,\n                alt: \"Question\",\n                className: \"max-w-full max-h-80 rounded-lg mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [question.answerType === \"Options\" && renderMCQ(), (question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank(), question.imageUrl && renderImageQuestion()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-blue-100 p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${questionIndex === 0 ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-blue-500 text-blue-100 hover:bg-blue-600 shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${!isAnswered ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400' : questionIndex === totalQuestions - 1 ? 'bg-green-500 hover:bg-green-600 text-green-100 shadow-md' : 'bg-blue-500 hover:bg-blue-600 text-blue-100 shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "username", "onNext", "onPrevious", "examTitle", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "handleAnswerSelect", "answer", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "progressPercentage", "renderMCQ", "options", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "optionLabels", "Object", "entries", "map", "key", "value", "index", "optionKey", "String", "trim", "optionValue", "label", "onClick", "renderFillBlank", "type", "onChange", "e", "target", "placeholder", "fill", "viewBox", "fillRule", "d", "clipRule", "renderImageQuestion", "imageUrl", "src", "alt", "style", "width", "round", "name", "image", "answerType", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Render MCQ Question with modern, clean design\n  const renderMCQ = () => {\n    if (!question.options) {\n      return (\n        <div className=\"text-center p-8 bg-red-50 rounded-xl border border-red-200\">\n          <div className=\"text-red-600 text-lg font-medium\">No options available for this question.</div>\n        </div>\n      );\n    }\n\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n\n    return (\n      <div className=\"space-y-4\">\n        {Object.entries(question.options).map(([key, value], index) => {\n          const optionKey = String(key).trim();\n          const optionValue = String(value).trim();\n          const label = optionLabels[index] || optionKey;\n\n          return (\n            <div key={optionKey} className=\"group\">\n              <button\n                onClick={() => handleAnswerSelect(optionKey)}\n                className={`w-full p-4 rounded-xl text-left transition-all duration-300 flex items-center space-x-4 border-2 ${\n                  currentAnswer === optionKey\n                    ? 'bg-blue-600 text-blue-100 border-blue-600 shadow-lg transform scale-[1.02]'\n                    : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 hover:shadow-md'\n                }`}\n              >\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg transition-all duration-300 ${\n                  currentAnswer === optionKey\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'bg-blue-100 text-blue-700 group-hover:bg-blue-200'\n                }`}>\n                  {label}\n                </div>\n                <span className={`flex-1 text-lg leading-relaxed ${\n                  currentAnswer === optionKey ? 'text-blue-100 font-medium' : 'text-gray-800'\n                }`}>\n                  {optionValue}\n                </span>\n              </button>\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render Fill-in-the-blank Question with modern design\n  const renderFillBlank = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200\">\n        <label className=\"block text-blue-800 font-semibold mb-4 text-lg\">\n          Your Answer:\n        </label>\n        <input\n          type=\"text\"\n          value={currentAnswer}\n          onChange={(e) => handleAnswerSelect(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full p-4 border-2 border-blue-300 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 bg-white text-lg transition-all duration-200\"\n        />\n      </div>\n      {currentAnswer && (\n        <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\">\n              <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <p className=\"text-green-700 font-medium text-lg\">\n              Answer recorded: \"{currentAnswer}\"\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  // Render Image-based Question with enhanced design\n  const renderImageQuestion = () => (\n    <div className=\"space-y-8\">\n      {question.imageUrl && (\n        <div className=\"text-center\">\n          <div className=\"inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200\">\n            <img\n              src={question.imageUrl}\n              alt=\"Question diagram\"\n              className=\"max-w-full max-h-96 rounded-lg mx-auto\"\n            />\n          </div>\n        </div>\n      )}\n\n      {question.options ? renderMCQ() : renderFillBlank()}\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\n      {/* Modern Header */}\n      <div className=\"bg-white/80 backdrop-blur-sm shadow-sm border-b border-blue-100\">\n        <div className=\"max-w-4xl mx-auto px-6 py-4\">\n          {/* Header Content */}\n          <div className=\"flex items-center justify-between mb-4\">\n            {/* Question counter */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\">\n                <span className=\"text-blue-100 font-bold text-lg\">{questionIndex + 1}</span>\n              </div>\n              <div>\n                <div className=\"text-sm text-gray-600\">Question</div>\n                <div className=\"text-lg font-semibold text-gray-800\">{questionIndex + 1} of {totalQuestions}</div>\n              </div>\n            </div>\n\n            {/* Timer */}\n            <div className={`px-4 py-2 rounded-lg font-mono font-bold text-lg shadow-md ${\n              timeLeft <= 60 ? 'bg-red-500 text-red-100' :\n              timeLeft <= 300 ? 'bg-orange-500 text-orange-100' :\n              'bg-green-500 text-green-100'\n            }`}>\n              {formatTime(timeLeft)}\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mb-2\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2 shadow-inner\">\n              <div\n                className=\"h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-700 shadow-sm\"\n                style={{ width: `${progressPercentage}%` }}\n              />\n            </div>\n            <div className=\"text-center mt-1 text-xs text-gray-600\">\n              {Math.round(progressPercentage)}% Complete\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content - Centered Layout */}\n      <div className=\"max-w-4xl mx-auto px-6 py-6 pb-28\">\n        {/* Question Card */}\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-100 p-6 md:p-8\">\n          {/* Question Header */}\n          <div className=\"mb-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900 leading-relaxed mb-4\">\n              {question.name}\n            </h2>\n            {question.image && (\n              <div className=\"mb-6\">\n                <div className=\"inline-block p-3 bg-white rounded-xl shadow-lg border border-gray-200\">\n                  <img\n                    src={question.image}\n                    alt=\"Question\"\n                    className=\"max-w-full max-h-80 rounded-lg mx-auto\"\n                  />\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Question Options */}\n          <div className=\"mb-6\">\n            {question.answerType === \"Options\" && renderMCQ()}\n            {(question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank()}\n            {question.imageUrl && renderImageQuestion()}\n          </div>\n        </div>\n      </div>\n\n      {/* Modern Bottom Navigation */}\n      <div className=\"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50\">\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-blue-100 p-3\">\n          <div className=\"flex items-center justify-center gap-3\">\n            {/* Previous Button */}\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${\n                questionIndex === 0\n                  ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400'\n                  : 'bg-blue-500 text-blue-100 hover:bg-blue-600 shadow-md'\n              }`}\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n              <span>Previous</span>\n            </button>\n\n            {/* Next/Submit Button */}\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${\n                !isAnswered\n                  ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400'\n                  : questionIndex === totalQuestions - 1\n                  ? 'bg-green-500 hover:bg-green-600 text-green-100 shadow-md'\n                  : 'bg-blue-500 hover:bg-blue-600 text-blue-100 shadow-md'\n              }`}\n            >\n              <span>\n                {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'}\n              </span>\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                {questionIndex === totalQuestions - 1 ? (\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                ) : (\n                  <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,QAAQ,GAAG,SAAS;EACpBC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAACQ,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdgB,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrCJ,gBAAgB,CAACI,MAAM,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACY,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACxB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,MAAMwB,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAAC1B,QAAQ,CAAC2B,OAAO,EAAE;MACrB,oBACE7B,OAAA;QAAK8B,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzE/B,OAAA;UAAK8B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC;IAEV;IAEA,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEnD,oBACEpC,OAAA;MAAK8B,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBM,MAAM,CAACC,OAAO,CAACpC,QAAQ,CAAC2B,OAAO,CAAC,CAACU,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;QAC7D,MAAMC,SAAS,GAAGC,MAAM,CAACJ,GAAG,CAAC,CAACK,IAAI,CAAC,CAAC;QACpC,MAAMC,WAAW,GAAGF,MAAM,CAACH,KAAK,CAAC,CAACI,IAAI,CAAC,CAAC;QACxC,MAAME,KAAK,GAAGX,YAAY,CAACM,KAAK,CAAC,IAAIC,SAAS;QAE9C,oBACE3C,OAAA;UAAqB8B,SAAS,EAAC,OAAO;UAAAC,QAAA,eACpC/B,OAAA;YACEgD,OAAO,EAAEA,CAAA,KAAM/B,kBAAkB,CAAC0B,SAAS,CAAE;YAC7Cb,SAAS,EAAG,oGACVjB,aAAa,KAAK8B,SAAS,GACvB,4EAA4E,GAC5E,iFACL,EAAE;YAAAZ,QAAA,gBAEH/B,OAAA;cAAK8B,SAAS,EAAG,yGACfjB,aAAa,KAAK8B,SAAS,GACvB,2BAA2B,GAC3B,mDACL,EAAE;cAAAZ,QAAA,EACAgB;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnC,OAAA;cAAM8B,SAAS,EAAG,kCAChBjB,aAAa,KAAK8B,SAAS,GAAG,2BAA2B,GAAG,eAC7D,EAAE;cAAAZ,QAAA,EACAe;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GArBDQ,SAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBd,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMc,eAAe,GAAGA,CAAA,kBACtBjD,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/B,OAAA;MAAK8B,SAAS,EAAC,mFAAmF;MAAAC,QAAA,gBAChG/B,OAAA;QAAO8B,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAElE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRnC,OAAA;QACEkD,IAAI,EAAC,MAAM;QACXT,KAAK,EAAE5B,aAAc;QACrBsC,QAAQ,EAAGC,CAAC,IAAKnC,kBAAkB,CAACmC,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;QACpDa,WAAW,EAAC,0BAA0B;QACtCxB,SAAS,EAAC;MAAuK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACLtB,aAAa,iBACZb,OAAA;MAAK8B,SAAS,EAAC,qFAAqF;MAAAC,QAAA,eAClG/B,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C/B,OAAA;UAAK8B,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjF/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAACyB,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAzB,QAAA,eACzE/B,OAAA;cAAMyD,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnC,OAAA;UAAG8B,SAAS,EAAC,oCAAoC;UAAAC,QAAA,GAAC,qBAC9B,EAAClB,aAAa,EAAC,IACnC;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAMyB,mBAAmB,GAAGA,CAAA,kBAC1B5D,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,GACvB7B,QAAQ,CAAC2D,QAAQ,iBAChB7D,OAAA;MAAK8B,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B/B,OAAA;QAAK8B,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpF/B,OAAA;UACE8D,GAAG,EAAE5D,QAAQ,CAAC2D,QAAS;UACvBE,GAAG,EAAC,kBAAkB;UACtBjC,SAAS,EAAC;QAAwC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAjC,QAAQ,CAAC2B,OAAO,GAAGD,SAAS,CAAC,CAAC,GAAGqB,eAAe,CAAC,CAAC;EAAA;IAAAjB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CACN;EAED,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBAEjF/B,OAAA;MAAK8B,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9E/B,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1C/B,OAAA;UAAK8B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD/B,OAAA;YAAK8B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C/B,OAAA;cAAK8B,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClF/B,OAAA;gBAAM8B,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAE5B,aAAa,GAAG;cAAC;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACNnC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAK8B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrDnC,OAAA;gBAAK8B,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAE5B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YAAK8B,SAAS,EAAG,8DACfvB,QAAQ,IAAI,EAAE,GAAG,yBAAyB,GAC1CA,QAAQ,IAAI,GAAG,GAAG,+BAA+B,GACjD,6BACD,EAAE;YAAAwB,QAAA,EACAZ,UAAU,CAACZ,QAAQ;UAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/B,OAAA;YAAK8B,SAAS,EAAC,kDAAkD;YAAAC,QAAA,eAC/D/B,OAAA;cACE8B,SAAS,EAAC,sGAAsG;cAChHkC,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAEtC,kBAAmB;cAAG;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GACpDT,IAAI,CAAC4C,KAAK,CAACvC,kBAAkB,CAAC,EAAC,YAClC;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAEhD/B,OAAA;QAAK8B,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBAEnG/B,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/B,OAAA;YAAI8B,SAAS,EAAC,uDAAuD;YAAAC,QAAA,EAClE7B,QAAQ,CAACiE;UAAI;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,EACJjC,QAAQ,CAACkE,KAAK,iBACbpE,OAAA;YAAK8B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB/B,OAAA;cAAK8B,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpF/B,OAAA;gBACE8D,GAAG,EAAE5D,QAAQ,CAACkE,KAAM;gBACpBL,GAAG,EAAC,UAAU;gBACdjC,SAAS,EAAC;cAAwC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,GAClB7B,QAAQ,CAACmE,UAAU,KAAK,SAAS,IAAIzC,SAAS,CAAC,CAAC,EAChD,CAAC1B,QAAQ,CAACmE,UAAU,KAAK,WAAW,IAAInE,QAAQ,CAACmE,UAAU,KAAK,mBAAmB,KAAKpB,eAAe,CAAC,CAAC,EACzG/C,QAAQ,CAAC2D,QAAQ,IAAID,mBAAmB,CAAC,CAAC;QAAA;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE/B,OAAA;QAAK8B,SAAS,EAAC,8EAA8E;QAAAC,QAAA,eAC3F/B,OAAA;UAAK8B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD/B,OAAA;YACEgD,OAAO,EAAEtC,UAAW;YACpB4D,QAAQ,EAAEnE,aAAa,KAAK,CAAE;YAC9B2B,SAAS,EAAG,8FACV3B,aAAa,KAAK,CAAC,GACf,yDAAyD,GACzD,uDACL,EAAE;YAAA4B,QAAA,gBAEH/B,OAAA;cAAK8B,SAAS,EAAC,SAAS;cAACyB,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAzB,QAAA,eAC9D/B,OAAA;gBAAMyD,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,mHAAmH;gBAACC,QAAQ,EAAC;cAAS;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjK,CAAC,eACNnC,OAAA;cAAA+B,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGTnC,OAAA;YACEgD,OAAO,EAAEvC,MAAO;YAChB6D,QAAQ,EAAE,CAACvD,UAAW;YACtBe,SAAS,EAAG,8FACV,CAACf,UAAU,GACP,yDAAyD,GACzDZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GACpC,0DAA0D,GAC1D,uDACL,EAAE;YAAA2B,QAAA,gBAEH/B,OAAA;cAAA+B,QAAA,EACG5B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;YAAM;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACPnC,OAAA;cAAK8B,SAAS,EAAC,SAAS;cAACyB,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAzB,QAAA,EAC7D5B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCJ,OAAA;gBAAMyD,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErKnC,OAAA;gBAAMyD,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACrK;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAhQIX,YAAY;AAAAsE,EAAA,GAAZtE,YAAY;AAkQlB,eAAeA,YAAY;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}