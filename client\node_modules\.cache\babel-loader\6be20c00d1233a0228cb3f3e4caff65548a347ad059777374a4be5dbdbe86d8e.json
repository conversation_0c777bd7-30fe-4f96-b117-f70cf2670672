{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Exams\\\\AddEditQuestion.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Modal } from \"antd\";\nimport React, { useState } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { addQuestionToExam, editQuestionById } from \"../../../apicalls/exams\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AddEditQuestion({\n  showAddEditQuestionModal,\n  setShowAddEditQuestionModal,\n  refreshData,\n  examId,\n  selectedQuestion,\n  setSelectedQuestion\n}) {\n  _s();\n  var _selectedQuestion$opt, _selectedQuestion$opt2, _selectedQuestion$opt3, _selectedQuestion$opt4, _selectedQuestion$opt5, _selectedQuestion$opt6, _selectedQuestion$opt7, _selectedQuestion$opt8;\n  const dispatch = useDispatch();\n  const [questionType, setQuestionType] = useState(() => {\n    if (selectedQuestion !== null && selectedQuestion !== void 0 && selectedQuestion.type) {\n      return selectedQuestion.type;\n    }\n    if ((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.answerType) === \"Options\") {\n      return selectedQuestion !== null && selectedQuestion !== void 0 && selectedQuestion.image || selectedQuestion !== null && selectedQuestion !== void 0 && selectedQuestion.imageUrl ? \"image\" : \"mcq\";\n    }\n    if ((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.answerType) === \"Fill in the Blank\" || (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.answerType) === \"Free Text\") {\n      return \"fill\";\n    }\n    // Default for AI-generated questions\n    if (selectedQuestion !== null && selectedQuestion !== void 0 && selectedQuestion.isAIGenerated) {\n      if ((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.questionType) === \"picture_based\") return \"image\";\n      if ((selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.questionType) === \"fill_blank\") return \"fill\";\n      return \"mcq\";\n    }\n    return \"mcq\";\n  });\n  const [imageFile, setImageFile] = useState(null);\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n\n      // Prepare form data for file upload\n      const formData = new FormData();\n\n      // Append question details\n      formData.append('name', values.name);\n      formData.append('type', questionType);\n      formData.append('exam', examId);\n      formData.append('topic', values.topic || 'General');\n      formData.append('classLevel', values.classLevel || 'General');\n\n      // Set legacy answerType for backward compatibility\n      if (questionType === \"mcq\") {\n        formData.append('answerType', 'Options');\n      } else if (questionType === \"fill\") {\n        formData.append('answerType', 'Fill in the Blank');\n      } else if (questionType === \"image\") {\n        formData.append('answerType', 'Options'); // Image questions can have MCQ options\n      }\n\n      // Append correct answer - unified for all types\n      formData.append('correctAnswer', values.correctAnswer);\n\n      // Append options for MCQ and image questions\n      if (questionType === \"mcq\" || questionType === \"image\") {\n        formData.append('options[A]', values.A);\n        formData.append('options[B]', values.B);\n        formData.append('options[C]', values.C);\n        formData.append('options[D]', values.D);\n        // Legacy field for backward compatibility\n        formData.append('correctOption', values.correctAnswer);\n      }\n\n      // Append image if selected\n      if (imageFile) {\n        formData.append(\"image\", imageFile);\n      } else if (selectedQuestion !== null && selectedQuestion !== void 0 && selectedQuestion.image) {\n        formData.append(\"image\", selectedQuestion.image); // Retain existing image if editing\n      }\n\n      let response;\n      if (selectedQuestion) {\n        // For editing, include question ID\n        formData.append('questionId', selectedQuestion._id);\n        response = await editQuestionById(formData);\n      } else {\n        response = await addQuestionToExam(formData);\n      }\n      if (response.success) {\n        message.success(response.message);\n        refreshData();\n        setShowAddEditQuestionModal(false);\n        setImageFile(null);\n      } else {\n        message.error(response.message);\n      }\n      setSelectedQuestion(null);\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const handleImageChange = e => {\n    const file = e.target.files[0];\n    setImageFile(file || null);\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: selectedQuestion ? \"Edit Question\" : \"Add Question\",\n    open: showAddEditQuestionModal,\n    footer: false,\n    onCancel: () => {\n      setShowAddEditQuestionModal(false);\n      setSelectedQuestion(null);\n      setImageFile(null);\n    },\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      onFinish: onFinish,\n      layout: \"vertical\",\n      initialValues: {\n        name: selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.name,\n        correctAnswer: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.correctAnswer) || (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.correctOption),\n        topic: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.topic) || 'General',\n        classLevel: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.classLevel) || 'General',\n        A: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : (_selectedQuestion$opt = selectedQuestion.options) === null || _selectedQuestion$opt === void 0 ? void 0 : _selectedQuestion$opt.A) || (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : (_selectedQuestion$opt2 = selectedQuestion.options) === null || _selectedQuestion$opt2 === void 0 ? void 0 : _selectedQuestion$opt2.a) || '',\n        B: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : (_selectedQuestion$opt3 = selectedQuestion.options) === null || _selectedQuestion$opt3 === void 0 ? void 0 : _selectedQuestion$opt3.B) || (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : (_selectedQuestion$opt4 = selectedQuestion.options) === null || _selectedQuestion$opt4 === void 0 ? void 0 : _selectedQuestion$opt4.b) || '',\n        C: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : (_selectedQuestion$opt5 = selectedQuestion.options) === null || _selectedQuestion$opt5 === void 0 ? void 0 : _selectedQuestion$opt5.C) || (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : (_selectedQuestion$opt6 = selectedQuestion.options) === null || _selectedQuestion$opt6 === void 0 ? void 0 : _selectedQuestion$opt6.c) || '',\n        D: (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : (_selectedQuestion$opt7 = selectedQuestion.options) === null || _selectedQuestion$opt7 === void 0 ? void 0 : _selectedQuestion$opt7.D) || (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : (_selectedQuestion$opt8 = selectedQuestion.options) === null || _selectedQuestion$opt8 === void 0 ? void 0 : _selectedQuestion$opt8.d) || ''\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"name\",\n        label: \"Question\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"questionType\",\n        label: \"Question Type\",\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          value: questionType,\n          onChange: e => setQuestionType(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"mcq\",\n            children: \"Multiple Choice (MCQ)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"fill\",\n            children: \"Fill in the Blank\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"image\",\n            children: \"Image-based Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"topic\",\n          label: \"Topic\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"e.g., Mathematics, Science\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"classLevel\",\n          label: \"Class Level\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"e.g., Primary 1, Secondary 2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), questionType === \"image\" && /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"image\",\n        label: \"Question Image\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: e => setImageFile(e.target.files[0])\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          children: \"Upload an image for this question\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"correctAnswer\",\n        label: \"Correct Answer\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: questionType === \"mcq\" || questionType === \"image\" ? \"Enter the correct option (A, B, C, or D)\" : \"Enter the correct answer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), (questionType === \"mcq\" || questionType === \"image\") && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"A\",\n            label: \"Option A\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"B\",\n            label: \"Option B\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"C\",\n            label: \"Option C\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"D\",\n            label: \"Option D\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"image\",\n        label: \"Question Image (Optional)\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), imageFile && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-sm text-gray-600\",\n          children: [\"Selected file: \", imageFile.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), (selectedQuestion === null || selectedQuestion === void 0 ? void 0 : selectedQuestion.image) && !imageFile && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: selectedQuestion.image,\n            alt: \"Current question\",\n            className: \"max-w-[200px] max-h-[200px] object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end mt-2 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"primary-outlined-btn\",\n          type: \"button\",\n          onClick: () => {\n            setShowAddEditQuestionModal(false);\n            setSelectedQuestion(null);\n            setImageFile(null);\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"primary-contained-btn\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n}\n_s(AddEditQuestion, \"Zj5GUv/J2LYxou+2OqqnKAovBu4=\", false, function () {\n  return [useDispatch];\n});\n_c = AddEditQuestion;\nexport default AddEditQuestion;\nvar _c;\n$RefreshReg$(_c, \"AddEditQuestion\");", "map": {"version": 3, "names": ["Form", "message", "Modal", "React", "useState", "useDispatch", "addQuestionToExam", "editQuestionById", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddEditQuestion", "showAddEditQuestionModal", "setShowAddEditQuestionModal", "refreshData", "examId", "selectedQuestion", "setSelectedQuestion", "_s", "_selectedQuestion$opt", "_selectedQuestion$opt2", "_selectedQuestion$opt3", "_selectedQuestion$opt4", "_selectedQuestion$opt5", "_selectedQuestion$opt6", "_selectedQuestion$opt7", "_selectedQuestion$opt8", "dispatch", "questionType", "setQuestionType", "type", "answerType", "image", "imageUrl", "isAIGenerated", "imageFile", "setImageFile", "onFinish", "values", "formData", "FormData", "append", "name", "topic", "classLevel", "<PERSON><PERSON><PERSON><PERSON>", "A", "B", "C", "D", "response", "_id", "success", "error", "handleImageChange", "e", "file", "target", "files", "title", "open", "footer", "onCancel", "children", "layout", "initialValues", "correctOption", "options", "a", "b", "c", "d", "<PERSON><PERSON>", "label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "className", "placeholder", "accept", "src", "alt", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Exams/AddEditQuestion.js"], "sourcesContent": ["import { Form, message, Modal } from \"antd\";\r\nimport React, { useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { addQuestionToExam, editQuestionById } from \"../../../apicalls/exams\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction AddEditQuestion({\r\n  showAddEditQuestionModal,\r\n  setShowAddEditQuestionModal,\r\n  refreshData,\r\n  examId,\r\n  selectedQuestion,\r\n  setSelectedQuestion,\r\n}) {\r\n  const dispatch = useDispatch();\r\n  const [questionType, setQuestionType] = useState(() => {\r\n    if (selectedQuestion?.type) {\r\n      return selectedQuestion.type;\r\n    }\r\n    if (selectedQuestion?.answerType === \"Options\") {\r\n      return selectedQuestion?.image || selectedQuestion?.imageUrl ? \"image\" : \"mcq\";\r\n    }\r\n    if (selectedQuestion?.answerType === \"Fill in the Blank\" || selectedQuestion?.answerType === \"Free Text\") {\r\n      return \"fill\";\r\n    }\r\n    // Default for AI-generated questions\r\n    if (selectedQuestion?.isAIGenerated) {\r\n      if (selectedQuestion?.questionType === \"picture_based\") return \"image\";\r\n      if (selectedQuestion?.questionType === \"fill_blank\") return \"fill\";\r\n      return \"mcq\";\r\n    }\r\n    return \"mcq\";\r\n  });\r\n  const [imageFile, setImageFile] = useState(null);\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Prepare form data for file upload\r\n      const formData = new FormData();\r\n\r\n      // Append question details\r\n      formData.append('name', values.name);\r\n      formData.append('type', questionType);\r\n      formData.append('exam', examId);\r\n      formData.append('topic', values.topic || 'General');\r\n      formData.append('classLevel', values.classLevel || 'General');\r\n\r\n      // Set legacy answerType for backward compatibility\r\n      if (questionType === \"mcq\") {\r\n        formData.append('answerType', 'Options');\r\n      } else if (questionType === \"fill\") {\r\n        formData.append('answerType', 'Fill in the Blank');\r\n      } else if (questionType === \"image\") {\r\n        formData.append('answerType', 'Options'); // Image questions can have MCQ options\r\n      }\r\n\r\n      // Append correct answer - unified for all types\r\n      formData.append('correctAnswer', values.correctAnswer);\r\n\r\n      // Append options for MCQ and image questions\r\n      if (questionType === \"mcq\" || questionType === \"image\") {\r\n        formData.append('options[A]', values.A);\r\n        formData.append('options[B]', values.B);\r\n        formData.append('options[C]', values.C);\r\n        formData.append('options[D]', values.D);\r\n        // Legacy field for backward compatibility\r\n        formData.append('correctOption', values.correctAnswer);\r\n      }\r\n\r\n      // Append image if selected\r\n      if (imageFile) {\r\n        formData.append(\"image\", imageFile);\r\n      } else if (selectedQuestion?.image) {\r\n        formData.append(\"image\", selectedQuestion.image); // Retain existing image if editing\r\n      }\r\n\r\n      let response;\r\n      if (selectedQuestion) {\r\n        // For editing, include question ID\r\n        formData.append('questionId', selectedQuestion._id);\r\n        response = await editQuestionById(formData);\r\n      } else {\r\n        response = await addQuestionToExam(formData);\r\n      }\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        refreshData();\r\n        setShowAddEditQuestionModal(false);\r\n        setImageFile(null);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n\r\n      setSelectedQuestion(null);\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setImageFile(file || null);\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      title={selectedQuestion ? \"Edit Question\" : \"Add Question\"}\r\n      open={showAddEditQuestionModal}\r\n      footer={false}\r\n      onCancel={() => {\r\n        setShowAddEditQuestionModal(false);\r\n        setSelectedQuestion(null);\r\n        setImageFile(null);\r\n      }}\r\n    >\r\n      <Form\r\n        onFinish={onFinish}\r\n        layout=\"vertical\"\r\n        initialValues={{\r\n          name: selectedQuestion?.name,\r\n          correctAnswer: selectedQuestion?.correctAnswer || selectedQuestion?.correctOption,\r\n          topic: selectedQuestion?.topic || 'General',\r\n          classLevel: selectedQuestion?.classLevel || 'General',\r\n          A: selectedQuestion?.options?.A || selectedQuestion?.options?.a || '',\r\n          B: selectedQuestion?.options?.B || selectedQuestion?.options?.b || '',\r\n          C: selectedQuestion?.options?.C || selectedQuestion?.options?.c || '',\r\n          D: selectedQuestion?.options?.D || selectedQuestion?.options?.d || '',\r\n        }}\r\n      >\r\n        <Form.Item name=\"name\" label=\"Question\">\r\n          <input type=\"text\" />\r\n        </Form.Item>\r\n\r\n        {/* Question Type Selection */}\r\n        <Form.Item name=\"questionType\" label=\"Question Type\">\r\n          <select\r\n            value={questionType}\r\n            onChange={(e) => setQuestionType(e.target.value)}\r\n          >\r\n            <option value=\"mcq\">Multiple Choice (MCQ)</option>\r\n            <option value=\"fill\">Fill in the Blank</option>\r\n            <option value=\"image\">Image-based Question</option>\r\n          </select>\r\n        </Form.Item>\r\n\r\n        {/* Additional Fields */}\r\n        <div className=\"flex gap-3\">\r\n          <Form.Item name=\"topic\" label=\"Topic\">\r\n            <input type=\"text\" placeholder=\"e.g., Mathematics, Science\" />\r\n          </Form.Item>\r\n          <Form.Item name=\"classLevel\" label=\"Class Level\">\r\n            <input type=\"text\" placeholder=\"e.g., Primary 1, Secondary 2\" />\r\n          </Form.Item>\r\n        </div>\r\n\r\n        {/* Image Upload for Image-based Questions */}\r\n        {questionType === \"image\" && (\r\n          <Form.Item name=\"image\" label=\"Question Image\">\r\n            <input\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={(e) => setImageFile(e.target.files[0])}\r\n            />\r\n            <small>Upload an image for this question</small>\r\n          </Form.Item>\r\n        )}\r\n\r\n        {/* Correct Answer - Universal Field */}\r\n        <Form.Item name=\"correctAnswer\" label=\"Correct Answer\">\r\n          <input\r\n            type=\"text\"\r\n            placeholder={\r\n              questionType === \"mcq\" || questionType === \"image\"\r\n                ? \"Enter the correct option (A, B, C, or D)\"\r\n                : \"Enter the correct answer\"\r\n            }\r\n          />\r\n        </Form.Item>\r\n\r\n        {/* Options for MCQ and Image Questions */}\r\n        {(questionType === \"mcq\" || questionType === \"image\") && (\r\n          <>\r\n            <div className=\"flex gap-3\">\r\n              <Form.Item name=\"A\" label=\"Option A\">\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n              <Form.Item name=\"B\" label=\"Option B\">\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n            </div>\r\n            <div className=\"flex gap-3\">\r\n              <Form.Item name=\"C\" label=\"Option C\">\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n              <Form.Item name=\"D\" label=\"Option D\">\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n            </div>\r\n          </>\r\n        )}\r\n\r\n        {/* Image Upload */}\r\n        <Form.Item name=\"image\" label=\"Question Image (Optional)\">\r\n          <input \r\n            type=\"file\" \r\n            accept=\"image/*\" \r\n            onChange={handleImageChange}\r\n          />\r\n          {imageFile && (\r\n            <div className=\"mt-2 text-sm text-gray-600\">\r\n              Selected file: {imageFile.name}\r\n            </div>\r\n          )}\r\n          {selectedQuestion?.image && !imageFile && (\r\n            <div className=\"mt-2\">\r\n              <img \r\n                src={selectedQuestion.image} \r\n                alt=\"Current question\" \r\n                className=\"max-w-[200px] max-h-[200px] object-cover\"\r\n              />\r\n            </div>\r\n          )}\r\n        </Form.Item>\r\n\r\n        {/* Buttons */}\r\n        <div className=\"flex justify-end mt-2 gap-3\">\r\n          <button\r\n            className=\"primary-outlined-btn\"\r\n            type=\"button\"\r\n            onClick={() => {\r\n              setShowAddEditQuestionModal(false);\r\n              setSelectedQuestion(null);\r\n              setImageFile(null);\r\n            }}\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button className=\"primary-contained-btn\">Save</button>\r\n        </div>\r\n      </Form>\r\n    </Modal>\r\n  );\r\n}\r\n\r\nexport default AddEditQuestion;"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AAC3C,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,yBAAyB;AAC7E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtE,SAASC,eAAeA,CAAC;EACvBC,wBAAwB;EACxBC,2BAA2B;EAC3BC,WAAW;EACXC,MAAM;EACNC,gBAAgB;EAChBC;AACF,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACD,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,MAAM;IACrD,IAAIe,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEc,IAAI,EAAE;MAC1B,OAAOd,gBAAgB,CAACc,IAAI;IAC9B;IACA,IAAI,CAAAd,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEe,UAAU,MAAK,SAAS,EAAE;MAC9C,OAAOf,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEgB,KAAK,IAAIhB,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEiB,QAAQ,GAAG,OAAO,GAAG,KAAK;IAChF;IACA,IAAI,CAAAjB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEe,UAAU,MAAK,mBAAmB,IAAI,CAAAf,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEe,UAAU,MAAK,WAAW,EAAE;MACxG,OAAO,MAAM;IACf;IACA;IACA,IAAIf,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEkB,aAAa,EAAE;MACnC,IAAI,CAAAlB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEY,YAAY,MAAK,eAAe,EAAE,OAAO,OAAO;MACtE,IAAI,CAAAZ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEY,YAAY,MAAK,YAAY,EAAE,OAAO,MAAM;MAClE,OAAO,KAAK;IACd;IACA,OAAO,KAAK;EACd,CAAC,CAAC;EACF,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMoC,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACFX,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAMiC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,MAAM,CAACI,IAAI,CAAC;MACpCH,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEb,YAAY,CAAC;MACrCW,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE1B,MAAM,CAAC;MAC/BwB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,MAAM,CAACK,KAAK,IAAI,SAAS,CAAC;MACnDJ,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEH,MAAM,CAACM,UAAU,IAAI,SAAS,CAAC;;MAE7D;MACA,IAAIhB,YAAY,KAAK,KAAK,EAAE;QAC1BW,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC;MAC1C,CAAC,MAAM,IAAIb,YAAY,KAAK,MAAM,EAAE;QAClCW,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE,mBAAmB,CAAC;MACpD,CAAC,MAAM,IAAIb,YAAY,KAAK,OAAO,EAAE;QACnCW,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;MAC5C;;MAEA;MACAF,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,MAAM,CAACO,aAAa,CAAC;;MAEtD;MACA,IAAIjB,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,OAAO,EAAE;QACtDW,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEH,MAAM,CAACQ,CAAC,CAAC;QACvCP,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEH,MAAM,CAACS,CAAC,CAAC;QACvCR,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEH,MAAM,CAACU,CAAC,CAAC;QACvCT,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEH,MAAM,CAACW,CAAC,CAAC;QACvC;QACAV,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEH,MAAM,CAACO,aAAa,CAAC;MACxD;;MAEA;MACA,IAAIV,SAAS,EAAE;QACbI,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEN,SAAS,CAAC;MACrC,CAAC,MAAM,IAAInB,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEgB,KAAK,EAAE;QAClCO,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEzB,gBAAgB,CAACgB,KAAK,CAAC,CAAC,CAAC;MACpD;;MAEA,IAAIkB,QAAQ;MACZ,IAAIlC,gBAAgB,EAAE;QACpB;QACAuB,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEzB,gBAAgB,CAACmC,GAAG,CAAC;QACnDD,QAAQ,GAAG,MAAM9C,gBAAgB,CAACmC,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACLW,QAAQ,GAAG,MAAM/C,iBAAiB,CAACoC,QAAQ,CAAC;MAC9C;MAEA,IAAIW,QAAQ,CAACE,OAAO,EAAE;QACpBtD,OAAO,CAACsD,OAAO,CAACF,QAAQ,CAACpD,OAAO,CAAC;QACjCgB,WAAW,CAAC,CAAC;QACbD,2BAA2B,CAAC,KAAK,CAAC;QAClCuB,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,MAAM;QACLtC,OAAO,CAACuD,KAAK,CAACH,QAAQ,CAACpD,OAAO,CAAC;MACjC;MAEAmB,mBAAmB,CAAC,IAAI,CAAC;MACzBU,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd1B,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;MACvBP,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMwD,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BtB,YAAY,CAACoB,IAAI,IAAI,IAAI,CAAC;EAC5B,CAAC;EAED,oBACEhD,OAAA,CAACT,KAAK;IACJ4D,KAAK,EAAE3C,gBAAgB,GAAG,eAAe,GAAG,cAAe;IAC3D4C,IAAI,EAAEhD,wBAAyB;IAC/BiD,MAAM,EAAE,KAAM;IACdC,QAAQ,EAAEA,CAAA,KAAM;MACdjD,2BAA2B,CAAC,KAAK,CAAC;MAClCI,mBAAmB,CAAC,IAAI,CAAC;MACzBmB,YAAY,CAAC,IAAI,CAAC;IACpB,CAAE;IAAA2B,QAAA,eAEFvD,OAAA,CAACX,IAAI;MACHwC,QAAQ,EAAEA,QAAS;MACnB2B,MAAM,EAAC,UAAU;MACjBC,aAAa,EAAE;QACbvB,IAAI,EAAE1B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE0B,IAAI;QAC5BG,aAAa,EAAE,CAAA7B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6B,aAAa,MAAI7B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkD,aAAa;QACjFvB,KAAK,EAAE,CAAA3B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2B,KAAK,KAAI,SAAS;QAC3CC,UAAU,EAAE,CAAA5B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE4B,UAAU,KAAI,SAAS;QACrDE,CAAC,EAAE,CAAA9B,gBAAgB,aAAhBA,gBAAgB,wBAAAG,qBAAA,GAAhBH,gBAAgB,CAAEmD,OAAO,cAAAhD,qBAAA,uBAAzBA,qBAAA,CAA2B2B,CAAC,MAAI9B,gBAAgB,aAAhBA,gBAAgB,wBAAAI,sBAAA,GAAhBJ,gBAAgB,CAAEmD,OAAO,cAAA/C,sBAAA,uBAAzBA,sBAAA,CAA2BgD,CAAC,KAAI,EAAE;QACrErB,CAAC,EAAE,CAAA/B,gBAAgB,aAAhBA,gBAAgB,wBAAAK,sBAAA,GAAhBL,gBAAgB,CAAEmD,OAAO,cAAA9C,sBAAA,uBAAzBA,sBAAA,CAA2B0B,CAAC,MAAI/B,gBAAgB,aAAhBA,gBAAgB,wBAAAM,sBAAA,GAAhBN,gBAAgB,CAAEmD,OAAO,cAAA7C,sBAAA,uBAAzBA,sBAAA,CAA2B+C,CAAC,KAAI,EAAE;QACrErB,CAAC,EAAE,CAAAhC,gBAAgB,aAAhBA,gBAAgB,wBAAAO,sBAAA,GAAhBP,gBAAgB,CAAEmD,OAAO,cAAA5C,sBAAA,uBAAzBA,sBAAA,CAA2ByB,CAAC,MAAIhC,gBAAgB,aAAhBA,gBAAgB,wBAAAQ,sBAAA,GAAhBR,gBAAgB,CAAEmD,OAAO,cAAA3C,sBAAA,uBAAzBA,sBAAA,CAA2B8C,CAAC,KAAI,EAAE;QACrErB,CAAC,EAAE,CAAAjC,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAEmD,OAAO,cAAA1C,sBAAA,uBAAzBA,sBAAA,CAA2BwB,CAAC,MAAIjC,gBAAgB,aAAhBA,gBAAgB,wBAAAU,sBAAA,GAAhBV,gBAAgB,CAAEmD,OAAO,cAAAzC,sBAAA,uBAAzBA,sBAAA,CAA2B6C,CAAC,KAAI;MACrE,CAAE;MAAAR,QAAA,gBAEFvD,OAAA,CAACX,IAAI,CAAC2E,IAAI;QAAC9B,IAAI,EAAC,MAAM;QAAC+B,KAAK,EAAC,UAAU;QAAAV,QAAA,eACrCvD,OAAA;UAAOsB,IAAI,EAAC;QAAM;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAGZrE,OAAA,CAACX,IAAI,CAAC2E,IAAI;QAAC9B,IAAI,EAAC,cAAc;QAAC+B,KAAK,EAAC,eAAe;QAAAV,QAAA,eAClDvD,OAAA;UACEsE,KAAK,EAAElD,YAAa;UACpBmD,QAAQ,EAAGxB,CAAC,IAAK1B,eAAe,CAAC0B,CAAC,CAACE,MAAM,CAACqB,KAAK,CAAE;UAAAf,QAAA,gBAEjDvD,OAAA;YAAQsE,KAAK,EAAC,KAAK;YAAAf,QAAA,EAAC;UAAqB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClDrE,OAAA;YAAQsE,KAAK,EAAC,MAAM;YAAAf,QAAA,EAAC;UAAiB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/CrE,OAAA;YAAQsE,KAAK,EAAC,OAAO;YAAAf,QAAA,EAAC;UAAoB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGZrE,OAAA;QAAKwE,SAAS,EAAC,YAAY;QAAAjB,QAAA,gBACzBvD,OAAA,CAACX,IAAI,CAAC2E,IAAI;UAAC9B,IAAI,EAAC,OAAO;UAAC+B,KAAK,EAAC,OAAO;UAAAV,QAAA,eACnCvD,OAAA;YAAOsB,IAAI,EAAC,MAAM;YAACmD,WAAW,EAAC;UAA4B;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACZrE,OAAA,CAACX,IAAI,CAAC2E,IAAI;UAAC9B,IAAI,EAAC,YAAY;UAAC+B,KAAK,EAAC,aAAa;UAAAV,QAAA,eAC9CvD,OAAA;YAAOsB,IAAI,EAAC,MAAM;YAACmD,WAAW,EAAC;UAA8B;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,EAGLjD,YAAY,KAAK,OAAO,iBACvBpB,OAAA,CAACX,IAAI,CAAC2E,IAAI;QAAC9B,IAAI,EAAC,OAAO;QAAC+B,KAAK,EAAC,gBAAgB;QAAAV,QAAA,gBAC5CvD,OAAA;UACEsB,IAAI,EAAC,MAAM;UACXoD,MAAM,EAAC,SAAS;UAChBH,QAAQ,EAAGxB,CAAC,IAAKnB,YAAY,CAACmB,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QAAE;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACFrE,OAAA;UAAAuD,QAAA,EAAO;QAAiC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACZ,eAGDrE,OAAA,CAACX,IAAI,CAAC2E,IAAI;QAAC9B,IAAI,EAAC,eAAe;QAAC+B,KAAK,EAAC,gBAAgB;QAAAV,QAAA,eACpDvD,OAAA;UACEsB,IAAI,EAAC,MAAM;UACXmD,WAAW,EACTrD,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,OAAO,GAC9C,0CAA0C,GAC1C;QACL;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,EAGX,CAACjD,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,OAAO,kBAClDpB,OAAA,CAAAE,SAAA;QAAAqD,QAAA,gBACEvD,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAjB,QAAA,gBACzBvD,OAAA,CAACX,IAAI,CAAC2E,IAAI;YAAC9B,IAAI,EAAC,GAAG;YAAC+B,KAAK,EAAC,UAAU;YAAAV,QAAA,eAClCvD,OAAA;cAAOsB,IAAI,EAAC;YAAM;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACZrE,OAAA,CAACX,IAAI,CAAC2E,IAAI;YAAC9B,IAAI,EAAC,GAAG;YAAC+B,KAAK,EAAC,UAAU;YAAAV,QAAA,eAClCvD,OAAA;cAAOsB,IAAI,EAAC;YAAM;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNrE,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAjB,QAAA,gBACzBvD,OAAA,CAACX,IAAI,CAAC2E,IAAI;YAAC9B,IAAI,EAAC,GAAG;YAAC+B,KAAK,EAAC,UAAU;YAAAV,QAAA,eAClCvD,OAAA;cAAOsB,IAAI,EAAC;YAAM;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACZrE,OAAA,CAACX,IAAI,CAAC2E,IAAI;YAAC9B,IAAI,EAAC,GAAG;YAAC+B,KAAK,EAAC,UAAU;YAAAV,QAAA,eAClCvD,OAAA;cAAOsB,IAAI,EAAC;YAAM;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA,eACN,CACH,eAGDrE,OAAA,CAACX,IAAI,CAAC2E,IAAI;QAAC9B,IAAI,EAAC,OAAO;QAAC+B,KAAK,EAAC,2BAA2B;QAAAV,QAAA,gBACvDvD,OAAA;UACEsB,IAAI,EAAC,MAAM;UACXoD,MAAM,EAAC,SAAS;UAChBH,QAAQ,EAAEzB;QAAkB;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACD1C,SAAS,iBACR3B,OAAA;UAAKwE,SAAS,EAAC,4BAA4B;UAAAjB,QAAA,GAAC,iBAC3B,EAAC5B,SAAS,CAACO,IAAI;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CACN,EACA,CAAA7D,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgB,KAAK,KAAI,CAACG,SAAS,iBACpC3B,OAAA;UAAKwE,SAAS,EAAC,MAAM;UAAAjB,QAAA,eACnBvD,OAAA;YACE2E,GAAG,EAAEnE,gBAAgB,CAACgB,KAAM;YAC5BoD,GAAG,EAAC,kBAAkB;YACtBJ,SAAS,EAAC;UAA0C;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGZrE,OAAA;QAAKwE,SAAS,EAAC,6BAA6B;QAAAjB,QAAA,gBAC1CvD,OAAA;UACEwE,SAAS,EAAC,sBAAsB;UAChClD,IAAI,EAAC,QAAQ;UACbuD,OAAO,EAAEA,CAAA,KAAM;YACbxE,2BAA2B,CAAC,KAAK,CAAC;YAClCI,mBAAmB,CAAC,IAAI,CAAC;YACzBmB,YAAY,CAAC,IAAI,CAAC;UACpB,CAAE;UAAA2B,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA;UAAQwE,SAAS,EAAC,uBAAuB;UAAAjB,QAAA,EAAC;QAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ;AAAC3D,EAAA,CAjPQP,eAAe;EAAA,QAQLT,WAAW;AAAA;AAAAoF,EAAA,GARrB3E,eAAe;AAmPxB,eAAeA,eAAe;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}