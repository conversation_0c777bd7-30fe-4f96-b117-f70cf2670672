[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Announcement\\AnnouncementModal.jsx": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Announcement\\Announcement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\announcements.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\index.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionGenerationForm.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionPreview.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\AutoGenerateExamModal.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizStart.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizRenderer.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "69"}, {"size": 395, "mtime": 1696247250000, "results": "70", "hashOfConfig": "71"}, {"size": 7079, "mtime": 1751136930251, "results": "72", "hashOfConfig": "71"}, {"size": 362, "mtime": 1696247250000, "results": "73", "hashOfConfig": "71"}, {"size": 430, "mtime": 1736735017645, "results": "74", "hashOfConfig": "71"}, {"size": 180, "mtime": 1696247250000, "results": "75", "hashOfConfig": "71"}, {"size": 11056, "mtime": 1751136950282, "results": "76", "hashOfConfig": "71"}, {"size": 334, "mtime": 1696247250000, "results": "77", "hashOfConfig": "71"}, {"size": 416, "mtime": 1696247250000, "results": "78", "hashOfConfig": "71"}, {"size": 404, "mtime": 1736731932223, "results": "79", "hashOfConfig": "71"}, {"size": 449, "mtime": 1736732007232, "results": "80", "hashOfConfig": "71"}, {"size": 2226, "mtime": 1749936905425, "results": "81", "hashOfConfig": "71"}, {"size": 4816, "mtime": 1748816644854, "results": "82", "hashOfConfig": "71"}, {"size": 12926, "mtime": 1751134094183, "results": "83", "hashOfConfig": "71"}, {"size": 2884, "mtime": 1748816644857, "results": "84", "hashOfConfig": "71"}, {"size": 7653, "mtime": 1750329453221, "results": "85", "hashOfConfig": "71"}, {"size": 32225, "mtime": 1751138224717, "results": "86", "hashOfConfig": "71"}, {"size": 41077, "mtime": 1751101906169, "results": "87", "hashOfConfig": "71"}, {"size": 2243, "mtime": 1735567977494, "results": "88", "hashOfConfig": "71"}, {"size": 12817, "mtime": 1751138131413, "results": "89", "hashOfConfig": "71"}, {"size": 6465, "mtime": 1748816644863, "results": "90", "hashOfConfig": "71"}, {"size": 1327, "mtime": 1709427669270, "results": "91", "hashOfConfig": "71"}, {"size": 8089, "mtime": 1740446459586, "results": "92", "hashOfConfig": "71"}, {"size": 4234, "mtime": 1734985908270, "results": "93", "hashOfConfig": "71"}, {"size": 3394, "mtime": 1735945343695, "results": "94", "hashOfConfig": "71"}, {"size": 7105, "mtime": 1751100513350, "results": "95", "hashOfConfig": "71"}, {"size": 2315, "mtime": 1735485818817, "results": "96", "hashOfConfig": "71"}, {"size": 15635, "mtime": 1737822439601, "results": "97", "hashOfConfig": "71"}, {"size": 15613, "mtime": 1751103257226, "results": "98", "hashOfConfig": "71"}, {"size": 9935, "mtime": 1740524224304, "results": "99", "hashOfConfig": "71"}, {"size": 4579, "mtime": 1749938582942, "results": "100", "hashOfConfig": "71"}, {"size": 522, "mtime": 1736735708590, "results": "101", "hashOfConfig": "71"}, {"size": 2578, "mtime": 1740446459580, "results": "102", "hashOfConfig": "71"}, {"size": 2204, "mtime": 1696247250000, "results": "103", "hashOfConfig": "71"}, {"size": 388, "mtime": 1703845955779, "results": "104", "hashOfConfig": "71"}, {"size": 1095, "mtime": 1748816644845, "results": "105", "hashOfConfig": "71"}, {"size": 279, "mtime": 1736719733927, "results": "106", "hashOfConfig": "71"}, {"size": 1104, "mtime": 1749936905424, "results": "107", "hashOfConfig": "71"}, {"size": 1179, "mtime": 1703618041193, "results": "108", "hashOfConfig": "71"}, {"size": 4783, "mtime": 1751133001491, "results": "109", "hashOfConfig": "71"}, {"size": 944, "mtime": 1750970590507, "results": "110", "hashOfConfig": "71"}, {"size": 6669, "mtime": 1750999504134, "results": "111", "hashOfConfig": "71"}, {"size": 12864, "mtime": 1751134045332, "results": "112", "hashOfConfig": "71"}, {"size": 4091, "mtime": 1751125746303, "results": "113", "hashOfConfig": "71"}, {"size": 8101, "mtime": 1750963515173, "results": "114", "hashOfConfig": "71"}, {"size": 578, "mtime": 1705434185826, "results": "115", "hashOfConfig": "71"}, {"size": 1787, "mtime": 1734985908268, "results": "116", "hashOfConfig": "71"}, {"size": 2748, "mtime": 1736737718411, "results": "117", "hashOfConfig": "71"}, {"size": 2421, "mtime": 1737107445778, "results": "118", "hashOfConfig": "71"}, {"size": 3692, "mtime": 1751088963669, "results": "119", "hashOfConfig": "71"}, {"size": 8145, "mtime": 1751000372079, "results": "120", "hashOfConfig": "71"}, {"size": 29072, "mtime": 1750992761364, "results": "121", "hashOfConfig": "71"}, {"size": 9494, "mtime": 1750995979612, "results": "122", "hashOfConfig": "71"}, {"size": 1524, "mtime": 1750994293078, "results": "123", "hashOfConfig": "71"}, {"size": 17375, "mtime": 1751000106093, "results": "124", "hashOfConfig": "71"}, {"size": 11161, "mtime": 1750999560542, "results": "125", "hashOfConfig": "71"}, {"size": 8252, "mtime": 1751004143541, "results": "126", "hashOfConfig": "71"}, {"size": 3047, "mtime": 1751086581664, "results": "127", "hashOfConfig": "71"}, {"size": 25462, "mtime": 1751089065189, "results": "128", "hashOfConfig": "71"}, {"size": 10774, "mtime": 1751085763434, "results": "129", "hashOfConfig": "71"}, {"size": 11689, "mtime": 1751100954560, "results": "130", "hashOfConfig": "71"}, {"size": 3850, "mtime": 1751088011224, "results": "131", "hashOfConfig": "71"}, {"size": 5991, "mtime": 1751088070022, "results": "132", "hashOfConfig": "71"}, {"size": 5741, "mtime": 1751088101803, "results": "133", "hashOfConfig": "71"}, {"size": 3690, "mtime": 1751088038266, "results": "134", "hashOfConfig": "71"}, {"size": 13867, "mtime": 1751133136111, "results": "135", "hashOfConfig": "71"}, {"size": 6950, "mtime": 1751112039610, "results": "136", "hashOfConfig": "71"}, {"size": 6675, "mtime": 1751111148871, "results": "137", "hashOfConfig": "71"}, {"size": 11650, "mtime": 1751130367786, "results": "138", "hashOfConfig": "71"}, {"size": 7561, "mtime": 1751136984378, "results": "139", "hashOfConfig": "71"}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, "1ymk59w", {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "177", "usedDeprecatedRules": "143"}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "184", "usedDeprecatedRules": "143"}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "188", "usedDeprecatedRules": "143"}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "195", "usedDeprecatedRules": "143"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "199", "usedDeprecatedRules": "143"}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "206", "usedDeprecatedRules": "143"}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "210", "usedDeprecatedRules": "143"}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "214", "usedDeprecatedRules": "143"}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "218", "usedDeprecatedRules": "143"}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "222", "usedDeprecatedRules": "143"}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "226", "usedDeprecatedRules": "143"}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "230", "usedDeprecatedRules": "143"}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "234", "usedDeprecatedRules": "143"}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "238", "usedDeprecatedRules": "143"}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "242", "usedDeprecatedRules": "143"}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "246", "usedDeprecatedRules": "143"}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "298", "usedDeprecatedRules": "143"}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "323", "usedDeprecatedRules": "143"}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "327", "usedDeprecatedRules": "143"}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "334", "usedDeprecatedRules": "143"}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "347", "usedDeprecatedRules": "143"}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "351", "usedDeprecatedRules": "143"}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["370", "371", "372", "373", "374"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Announcement\\AnnouncementModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Announcement\\Announcement.jsx", ["375"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { message, Modal, Input } from \"antd\";\r\nimport {\r\n  PlusOutlined,\r\n  EditOutlined,\r\n  DeleteOutlined,\r\n} from \"@ant-design/icons\";\r\nimport {\r\n  addAnnouncement,\r\n  deleteAnnouncement,\r\n  getAnnouncements,\r\n  updateAnnouncement,\r\n} from \"../../../apicalls/announcements\";\r\nimport \"./announcement.css\"; // your custom styles\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\n\r\nexport default function Announcement() {\r\n  const [list, setList] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [modalOpen, setModalOpen] = useState(false);\r\n  const [form, setForm] = useState({ heading: \"\", description: \"\" });\r\n  const [editingId, setEditingId] = useState(null);\r\n\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchAll = async () => {\r\n    dispatch(ShowLoading());\r\n    const res = await getAnnouncements();\r\n    dispatch(HideLoading());\r\n    if (res.success !== false) setList(res);\r\n    else message.error(res.error || \"Failed to load announcements\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAll();\r\n  }, []);\r\n\r\n  const openAddModal = () => {\r\n    setForm({ heading: \"\", description: \"\" });\r\n    setEditingId(null);\r\n    setModalOpen(true);\r\n  };\r\n\r\n  const openEditModal = (announcement) => {\r\n    setForm({\r\n      heading: announcement.heading,\r\n      description: announcement.description,\r\n    });\r\n    setEditingId(announcement._id);\r\n    setModalOpen(true);\r\n  };\r\n\r\n  const handleModalSubmit = async () => {\r\n    if (!form.heading || !form.description) {\r\n      return message.warning(\"Heading and Description are required\");\r\n    }\r\n    dispatch(ShowLoading());\r\n\r\n    setLoading(true);\r\n    const res = editingId\r\n      ? await updateAnnouncement(editingId, form)\r\n      : await addAnnouncement(form);\r\n    setLoading(false);\r\n\r\n    if (res.success === false)\r\n      return message.error(res.error || \"Operation failed\");\r\n\r\n    message.success(editingId ? \"Announcement updated\" : \"Announcement added\");\r\n    setModalOpen(false);\r\n    setForm({ heading: \"\", description: \"\" });\r\n    setEditingId(null);\r\n    fetchAll();\r\n    dispatch(HideLoading());\r\n\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    if (!window.confirm(\"Delete this announcement?\")) return;\r\n    dispatch(ShowLoading());\r\n    const res = await deleteAnnouncement(id);\r\n    dispatch(HideLoading());\r\n\r\n    if (res.success === false)\r\n      return message.error(res.error || \"Delete failed\");\r\n    message.success(\"Announcement deleted\");\r\n    fetchAll();\r\n  };\r\n\r\n  return (\r\n    <div className=\"announcement-admin\">\r\n      <div className=\"admin-header\">\r\n        <h2>Manage Announcements</h2>\r\n        <button onClick={openAddModal} className=\"add-btn\">\r\n          <PlusOutlined />\r\n          <span>Add Announcement</span>\r\n        </button>\r\n      </div>\r\n\r\n      {list.length === 0 ? (\r\n        <p className=\"no-announcements\">No announcements yet.</p>\r\n      ) : (\r\n        <div className=\"announcement-list\">\r\n          {list.map((item) => (\r\n            <div className=\"announcement-card\" key={item._id}>\r\n              <div className=\"announcement-content\">\r\n                <h3>{item.heading}</h3>\r\n                <p>{item.description}</p>\r\n              </div>\r\n              <div className=\"card-actions\">\r\n                <button\r\n                  onClick={() => openEditModal(item)}\r\n                  className=\"edit-btn\"\r\n                  title=\"Edit\"\r\n                >\r\n                  <EditOutlined />\r\n                </button>\r\n                <button\r\n                  onClick={() => handleDelete(item._id)}\r\n                  className=\"delete-btn\"\r\n                  title=\"Delete\"\r\n                >\r\n                  <DeleteOutlined />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Modal for Add/Edit */}\r\n      <Modal\r\n        title={editingId ? \"Edit Announcement\" : \"Add Announcement\"}\r\n        open={modalOpen}\r\n        onCancel={() => setModalOpen(false)}\r\n        onOk={handleModalSubmit}\r\n        okText={editingId ? \"Update\" : \"Add\"}\r\n        confirmLoading={loading}\r\n      >\r\n        <div className=\"modal-form\">\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Heading\"\r\n            style={{ borderRadius: \"5px\" }}\r\n            value={form.heading}\r\n            onChange={(e) => setForm({ ...form, heading: e.target.value })}\r\n          />\r\n          <Input.TextArea\r\n            placeholder=\"Description\"\r\n            rows={4}\r\n            value={form.description}\r\n            onChange={(e) =>\r\n              setForm({ ...form, description: e.target.value })\r\n            }\r\n          />\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["376", "377"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["378"], [], "import { message, Table } from \"antd\";\r\nimport React, { useEffect } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { deleteExamById, getAllExams } from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Exams() {\r\n  const navigate = useNavigate();\r\n  const [exams, setExams] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  const getExamsData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExams(response.data.reverse());\r\n        console.log(response, \"exam\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteExam = async (examId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteExamById({\r\n        examId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamsData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Duration\",\r\n      dataIndex: \"duration\",\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Category\",\r\n      dataIndex: \"category\",\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalMarks\",\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"passingMarks\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2\">\r\n          <i\r\n            className=\"ri-pencil-line\"\r\n            onClick={() => navigate(`/admin/exams/edit/${record._id}`)}\r\n          ></i>\r\n          <i\r\n            className=\"ri-delete-bin-line\"\r\n            onClick={() => deleteExam(record._id)}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getExamsData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <PageTitle title=\"Exams\" />\r\n\r\n        <button\r\n          className=\"primary-outlined-btn flex items-center\"\r\n          onClick={() => navigate(\"/admin/exams/add\")}\r\n        >\r\n          <i className=\"ri-add-line\"></i>\r\n          Add Exam\r\n        </button>\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table columns={columns} dataSource={exams} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Exams;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx", ["379"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { getPlans } from \"../../../apicalls/plans\";\r\nimport \"./Plans.css\";\r\nimport ConfirmModal from \"./components/ConfirmModal\";\r\nimport WaitingModal from \"./components/WaitingModal\";\r\nimport { addPayment } from \"../../../apicalls/payment\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nconst Plans = () => {\r\n    const [plans, setPlans] = useState([]);\r\n    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\r\n    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\r\n    const [paymentInProgress, setPaymentInProgress] = useState(false);\r\n    const [selectedPlan, setSelectedPlan] = useState(null);\r\n    const { user } = useSelector((state) => state.user);\r\n    const { subscriptionData } = useSelector((state) => state.subscription);\r\n    const dispatch = useDispatch()\r\n\r\n    useEffect(() => {\r\n        const fetchPlans = async () => {\r\n            try {\r\n                const response = await getPlans();\r\n                setPlans(response);\r\n            } catch (error) {\r\n                console.error(\"Error fetching plans:\", error);\r\n            }\r\n        };\r\n\r\n        fetchPlans();\r\n    }, []);\r\n\r\n    const transactionDetails = {\r\n        amount: selectedPlan?.discountedPrice || 'N/A',\r\n        currency: \"TZS\",\r\n        destination: \"brainwave.zone\",\r\n    };\r\n\r\n\r\n    const handlePaymentStart = async (plan) => {\r\n        setSelectedPlan(plan);\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await addPayment({ plan });\r\n            localStorage.setItem(\"order_id\", response.order_id);\r\n            setWaitingModalOpen(true);\r\n            setPaymentInProgress(true);\r\n            dispatch(setPaymentVerificationNeeded(true));\r\n        } catch (error) {\r\n            console.error(\"Error processing payment:\", error);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        console.log(\"subscription Data in Plans\", subscriptionData)\r\n        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === \"paid\" && paymentInProgress) {\r\n            setWaitingModalOpen(false);\r\n            setConfirmModalOpen(true);\r\n            setPaymentInProgress(false);\r\n        }\r\n    }, [user, subscriptionData]);\r\n\r\n    return (\r\n        <div>\r\n            {!user ?\r\n                <>\r\n                </>\r\n                :\r\n                !user.paymentRequired ?\r\n                    <div className=\"no-plan-required\">\r\n                        <div className=\"no-plan-content\">\r\n                            <h2>No Plan Required</h2>\r\n                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>\r\n                        </div>\r\n                    </div>\r\n                    :\r\n                    subscriptionData?.paymentStatus !== \"paid\" ?\r\n                        <div className=\"plans-container\">\r\n                            {plans.map((plan) => (\r\n                                <div\r\n                                    key={plan._id}\r\n                                    className={`plan-card ${plan.title === \"Standard Membership\" ? \"basic\" : \"\"}`}\r\n                                >\r\n                                    {plan.title === \"Standard Membership\" && (\r\n                                        <div className=\"most-popular-label\">MOST POPULAR</div>\r\n                                    )}\r\n\r\n                                    <h2 className=\"plan-title\">{plan.title}</h2>\r\n                                    <p className=\"plan-actual-price\">\r\n                                        {plan.actualPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <p className=\"plan-discounted-price\">\r\n                                        {plan.discountedPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <span className=\"plan-discount-tag\">\r\n                                        {plan.discountPercentage}% OFF\r\n                                    </span>\r\n                                    <p className=\"plan-renewal-info\">\r\n                                        For {plan?.features[0]}\r\n                                    </p>\r\n                                    <button className=\"plan-button\"\r\n                                        // onClick={() => setConfirmModalOpen(true)}\r\n                                        onClick={() => handlePaymentStart(plan)}\r\n                                    >Choose Plan</button>\r\n                                    <ul className=\"plan-features\">\r\n                                        {plan.features.map((feature, index) => (\r\n                                            <li key={index} className=\"plan-feature\">\r\n                                                <span className=\"plan-feature-icon\">✔</span>\r\n                                                {feature}\r\n                                            </li>\r\n                                        ))}\r\n                                    </ul>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        :\r\n                        <div className=\"subscription-details\">\r\n                            <h1 className=\"plan-title\">{subscriptionData.plan.title}</h1>\r\n\r\n                            <svg\r\n                                width=\"64px\"\r\n                                height=\"64px\"\r\n                                viewBox=\"-3.2 -3.2 38.40 38.40\"\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"#10B981\"\r\n                                stroke=\"#253864\"\r\n                                transform=\"matrix(1, 0, 0, 1, 0, 0)\"\r\n                            >\r\n                                <g id=\"SVGRepo_bgCarrier\" strokeWidth=\"0\"></g>\r\n                                <g id=\"SVGRepo_tracerCarrier\" strokeLinecap=\"round\" strokeLinejoin=\"round\" stroke=\"#CCCCCC\" strokeWidth=\"0.064\"></g>\r\n                                <g id=\"SVGRepo_iconCarrier\">\r\n                                    <path\r\n                                        d=\"m16 0c8.836556 0 16 7.163444 16 16s-7.163444 16-16 16-16-7.163444-16-16 7.163444-16 16-16zm5.7279221 11-7.0710679 7.0710678-4.2426406-4.2426407-1.4142136 1.4142136 5.6568542 5.6568542 8.4852814-8.4852813z\"\r\n                                        fill=\"#202327\"\r\n                                        fillRule=\"evenodd\"\r\n                                    ></path>\r\n                                </g>\r\n                            </svg>\r\n\r\n                            <p className=\"plan-description\">{subscriptionData?.plan?.subscriptionData}</p>\r\n                            <p className=\"plan-dates\">Start Date: {subscriptionData.startDate}</p>\r\n                            <p className=\"plan-dates\">End Date: {subscriptionData.endDate}</p>\r\n                        </div>\r\n            }\r\n\r\n            <WaitingModal\r\n                isOpen={isWaitingModalOpen}\r\n                onClose={() => setWaitingModalOpen(false)}\r\n            />\r\n\r\n            <ConfirmModal\r\n                isOpen={isConfirmModalOpen}\r\n                onClose={() => setConfirmModalOpen(false)}\r\n                transaction={transactionDetails}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Plans;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["380"], [], "import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n\nimport PDFModal from \"./PDFModal\";\nimport {\n  FaPlayCircle,\n  FaBook,\n  FaVideo,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n\n} from \"react-icons/fa\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary'\n    ? primarySubjects\n    : userLevelLower === 'secondary'\n      ? secondarySubjects\n      : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary'\n    ? ['1', '2', '3', '4', '5', '6', '7']\n    : userLevelLower === 'secondary'\n      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n      : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"videos\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n  const [selectedSubtitle, setSelectedSubtitle] = useState('off');\n  const [videoRef, setVideoRef] = useState(null);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n        // For videos (no year field), sort by creation date or reverse order\n        else if (activeTab === \"videos\") {\n          // Since videos are fetched in reverse order from server, maintain that for \"newest\"\n          return 0; // Keep original order (newest first from server)\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n        // For videos, reverse the order\n        else if (activeTab === \"videos\") {\n          return 0; // Will be reversed after sort\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n    // For videos with \"oldest\" sort, reverse the array\n    if (activeTab === \"videos\" && sortBy === \"oldest\") {\n      filtered.reverse();\n    }\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    fetch(documentUrl)\n      .then((response) => response.blob())\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedMaterials[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    setSelectedSubtitle('off');\n    setVideoRef(null);\n  };\n\n  // Handle subtitle selection\n  const handleSubtitleChange = (language) => {\n    setSelectedSubtitle(language);\n\n    if (videoRef) {\n      const tracks = videoRef.textTracks;\n\n      // Disable all tracks first\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'disabled';\n      }\n\n      // Enable selected track\n      if (language !== 'off') {\n        for (let i = 0; i < tracks.length; i++) {\n          if (tracks[i].language === language) {\n            tracks[i].mode = 'showing';\n            break;\n          }\n        }\n      }\n    }\n  };\n\n  const handleExpandVideo = () => {\n    setIsVideoExpanded(true);\n  };\n\n  const handleCollapseVideo = () => {\n    setIsVideoExpanded(false);\n  };\n\n\n\n\n\n\n\n\n\n  // Note: Auto-refresh removed since videos are now uploaded synchronously\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include'\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.warn('⚠️ Failed to get signed URL, using original:', error.message);\n        return videoUrl;\n      }\n    }\n\n    // For other URLs or if signed URL fails, return as-is\n    return videoUrl;\n  };\n\n\n\n\n\n  // Get appropriate thumbnail URL for video\n  const getThumbnailUrl = (material) => {\n    // If we have a custom thumbnail, use it\n    if (material.thumbnail && material.thumbnail !== \"\" && material.thumbnail !== \"processing\") {\n      return material.thumbnail;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n      // Extract YouTube video ID if it's a full URL\n      let videoId = material.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n\n    // For uploaded videos without thumbnails, use a default placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n  };\n\n  // Keyboard support for video modal\n  useEffect(() => {\n    const handleKeyPress = (event) => {\n      if (showVideoIndices.length > 0) {\n        switch (event.key) {\n          case 'Escape':\n            handleHideVideo();\n            break;\n          case 'f':\n          case 'F':\n            if (!isVideoExpanded) {\n              handleExpandVideo();\n            } else {\n              handleCollapseVideo();\n            }\n            break;\n          default:\n            break;\n        }\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => {\n      document.removeEventListener('keydown', handleKeyPress);\n    };\n  }, [showVideoIndices, isVideoExpanded]);\n\n  return (\n    <div className=\"study-material-modern\">\n      {/* Modern Header */}\n      <div className=\"modern-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <FaGraduationCap className=\"header-icon\" />\n            <div className=\"header-text\">\n              <h1>Study Materials</h1>\n              <p>Access comprehensive learning resources for {userLevel} education</p>\n            </div>\n          </div>\n          <div className=\"level-badge\">\n            {userLevel?.toUpperCase()} LEVEL\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"nav-tabs\">\n        <div className=\"tabs-container\">\n          <button\n            className={`nav-tab ${activeTab === 'videos' ? 'active' : ''}`}\n            onClick={() => handleTabChange('videos')}\n          >\n            <FaVideo className=\"tab-icon\" />\n            <span>Videos</span>\n          </button>\n          <button\n            className={`nav-tab ${activeTab === 'study-notes' ? 'active' : ''}`}\n            onClick={() => handleTabChange('study-notes')}\n          >\n            <FaFileAlt className=\"tab-icon\" />\n            <span>Notes</span>\n          </button>\n          <button\n            className={`nav-tab ${activeTab === 'past-papers' ? 'active' : ''}`}\n            onClick={() => handleTabChange('past-papers')}\n          >\n            <FaFileAlt className=\"tab-icon\" />\n            <span>Past Papers</span>\n          </button>\n          <button\n            className={`nav-tab ${activeTab === 'books' ? 'active' : ''}`}\n            onClick={() => handleTabChange('books')}\n          >\n            <FaBook className=\"tab-icon\" />\n            <span>Books</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Filters Section */}\n      <div className=\"filters-section\">\n        <div className=\"filters-container\">\n          {/* Class Selection */}\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">\n              Class\n              {userCurrentClass && (\n                <span className=\"current-class-display\">\n                  {userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`}\n                </span>\n              )}\n            </label>\n            <div className=\"class-selector\">\n              <button\n                className=\"class-display-btn\"\n                onClick={toggleClassSelector}\n              >\n                <span>\n                  {selectedClass === 'all' ? 'All Classes' :\n                    userLevelLower === 'primary'\n                      ? `Class ${selectedClass}`\n                      : `Form ${selectedClass}`\n                  }\n                  {selectedClass === userCurrentClass && (\n                    <span className=\"current-badge\">Current</span>\n                  )}\n                </span>\n                <FaChevronDown className={`chevron ${showClassSelector ? 'open' : ''}`} />\n              </button>\n\n              {showClassSelector && (\n                <div className=\"class-dropdown\">\n                  <button\n                    className={`class-option ${selectedClass === 'all' ? 'active' : ''}`}\n                    onClick={() => handleClassChange('all')}\n                  >\n                    All Classes\n                  </button>\n                  {availableClasses.map((className, index) => (\n                    <button\n                      key={index}\n                      className={`class-option ${selectedClass === className ? 'active' : ''} ${className === userCurrentClass ? 'user-current' : ''}`}\n                      onClick={() => handleClassChange(className)}\n                    >\n                      {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                      {className === userCurrentClass && (\n                        <span className=\"current-badge\">Your Class</span>\n                      )}\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Subject Filter */}\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">Subject</label>\n            <div className=\"subject-pills\">\n              <button\n                className={`subject-pill ${selectedSubject === 'all' ? 'active' : ''}`}\n                onClick={() => handleSubjectChange('all')}\n              >\n                All Subjects\n              </button>\n              {subjectsList.map((subject, index) => (\n                <button\n                  key={index}\n                  className={`subject-pill ${selectedSubject === subject ? 'active' : ''}`}\n                  onClick={() => handleSubjectChange(subject)}\n                >\n                  {subject}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Search and Sort Section */}\n          <div className=\"search-sort-container\">\n            <div className=\"search-section\">\n              <label className=\"filter-label\">Search Materials</label>\n              <div className=\"search-box\">\n                <FaSearch className=\"search-icon\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search by title, subject, or year...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"search-input\"\n                />\n              </div>\n            </div>\n\n            <div className=\"sort-section\">\n              <label className=\"filter-label\">Sort By</label>\n              <div className=\"sort-selector\">\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"sort-select\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className=\"materials-grid\">\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className=\"material-card\">\n                <div className=\"card-header\">\n                  <div className=\"material-type\">\n                    {activeTab === 'videos' && <FaVideo className=\"type-icon\" />}\n                    {activeTab === 'study-notes' && <FaFileAlt className=\"type-icon\" />}\n                    {activeTab === 'past-papers' && <FaFileAlt className=\"type-icon\" />}\n                    {activeTab === 'books' && <FaBook className=\"type-icon\" />}\n                    <span className=\"type-label\">\n                      {activeTab === 'study-notes' ? 'Note' :\n                       activeTab === 'past-papers' ? 'Past Paper' :\n                       activeTab === 'videos' ? 'Video' : 'Book'}\n                    </span>\n                  </div>\n                  <div className=\"header-right\">\n                    {/* Class tags for videos */}\n                    {activeTab === 'videos' && material.coreClass && (\n                      <div className=\"class-tags\">\n                        {material.isCore ? (\n                          <span className=\"class-tag core-class\">\n                            Core Class {userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`}\n                          </span>\n                        ) : material.sharedFromClass && (\n                          <span className=\"class-tag shared-class\">\n                            Shared from {userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`}\n                          </span>\n                        )}\n                      </div>\n                    )}\n                    {material.year && (\n                      <span className=\"material-year\">{material.year}</span>\n                    )}\n                  </div>\n                </div>\n\n                {/* Video Thumbnail for videos */}\n                {activeTab === 'videos' && (material.videoUrl || material.videoID) && (\n                  <div className=\"video-thumbnail-container\" onClick={() => handleShowVideo(index)}>\n                    <img\n                      src={getThumbnailUrl(material)}\n                      alt={material.title}\n                      className=\"video-thumbnail\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = material.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          if (!e.target.src.includes('youtube.com')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n                          } else if (e.target.src.includes('maxresdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;\n                          } else if (e.target.src.includes('mqdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n                          } else {\n                            // Final fallback to default placeholder\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                          }\n                        } else {\n                          // For uploaded videos without thumbnails, use default placeholder\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                      <span className=\"play-text\">Watch Now</span>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"card-content\">\n                  <h3 className=\"material-title\">{material.title}</h3>\n                  <div className=\"material-meta\">\n                    <span className=\"material-subject\">{material.subject}</span>\n                    {material.className && (\n                      <span className=\"material-class\">\n                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"card-actions\">\n                  {activeTab === 'videos' && (material.videoUrl || material.videoID) ? (\n                    <div className=\"video-info-text\">\n                      <span className=\"video-duration\">Click thumbnail to play</span>\n                    </div>\n                  ) : material.documentUrl ? (\n                    <>\n                      <button\n                        className=\"action-btn secondary\"\n                        onClick={() => handleDocumentPreview(material.documentUrl)}\n                      >\n                        <FaEye /> View\n                      </button>\n                      <button\n                        className=\"action-btn primary\"\n                        onClick={() => handleDocumentDownload(material.documentUrl)}\n                      >\n                        <FaDownload /> Download\n                      </button>\n                    </>\n                  ) : (\n                    <span className=\"unavailable\">Not available</span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedMaterials[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <>\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3>{video.title || 'Untitled Video'}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject || 'Unknown Subject'}</span>\n                        <span className=\"video-class\">Class {video.className || 'N/A'}</span>\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      {!isVideoExpanded ? (\n                        <button\n                          className=\"control-btn expand-btn\"\n                          onClick={handleExpandVideo}\n                          title=\"Expand to fullscreen\"\n                        >\n                          <FaExpand />\n                        </button>\n                      ) : (\n                        <button\n                          className=\"control-btn collapse-btn\"\n                          onClick={handleCollapseVideo}\n                          title=\"Exit fullscreen\"\n                        >\n                          <FaCompress />\n                        </button>\n                      )}\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadedMetadata={() => {\n                              // Auto-enable first subtitle if available and none selected\n                              if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {\n                                const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];\n                                handleSubtitleChange(defaultSubtitle.language);\n                              }\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            <p style={{color: 'white', textAlign: 'center', padding: '20px'}}>\n                              Your browser does not support the video tag.\n                              <br />\n                              <a href={video.signedVideoUrl || video.videoUrl} target=\"_blank\" rel=\"noopener noreferrer\" style={{color: '#4fc3f7'}}>\n                                Click here to open video in new tab\n                              </a>\n                            </p>\n                          </video>\n\n                          {/* Custom Subtitle Controls */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div style={{\n                              padding: '12px 15px',\n                              background: 'rgba(0,0,0,0.8)',\n                              borderRadius: '0 0 8px 8px',\n                              borderTop: '1px solid #333'\n                            }}>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '12px',\n                                flexWrap: 'wrap'\n                              }}>\n                                <span style={{\n                                  color: '#fff',\n                                  fontSize: '14px',\n                                  fontWeight: '500',\n                                  minWidth: 'fit-content'\n                                }}>\n                                  📝 Choose Language:\n                                </span>\n\n                                <div style={{\n                                  display: 'flex',\n                                  gap: '8px',\n                                  flexWrap: 'wrap',\n                                  flex: 1\n                                }}>\n                                  {/* Off Button */}\n                                  <button\n                                    onClick={() => handleSubtitleChange('off')}\n                                    style={{\n                                      padding: '6px 12px',\n                                      borderRadius: '20px',\n                                      border: 'none',\n                                      fontSize: '12px',\n                                      fontWeight: '500',\n                                      cursor: 'pointer',\n                                      transition: 'all 0.2s ease',\n                                      backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',\n                                      color: '#fff'\n                                    }}\n                                  >\n                                    OFF\n                                  </button>\n\n                                  {/* Language Buttons */}\n                                  {video.subtitles.map((subtitle) => (\n                                    <button\n                                      key={subtitle.language}\n                                      onClick={() => handleSubtitleChange(subtitle.language)}\n                                      style={{\n                                        padding: '6px 12px',\n                                        borderRadius: '20px',\n                                        border: 'none',\n                                        fontSize: '12px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s ease',\n                                        backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',\n                                        color: '#fff',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '4px'\n                                      }}\n                                    >\n                                      <span>{subtitle.languageName}</span>\n                                      {subtitle.isAutoGenerated && (\n                                        <span style={{\n                                          fontSize: '10px',\n                                          opacity: 0.8,\n                                          backgroundColor: 'rgba(255,255,255,0.2)',\n                                          padding: '1px 4px',\n                                          borderRadius: '8px'\n                                        }}>\n                                          AI\n                                        </span>\n                                      )}\n                                    </button>\n                                  ))}\n                                </div>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  {!isVideoExpanded && (\n                    <div className=\"video-footer\">\n                      <div className=\"video-description\">\n                        <p>Watch this educational video to learn more about {video.subject}.</p>\n                        {video.subtitleGenerationStatus === 'processing' && (\n                          <div className=\"subtitle-status\" style={{\n                            marginTop: '10px',\n                            fontSize: '0.9em',\n                            color: '#2196F3',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                          }}>\n                            <div style={{\n                              width: '16px',\n                              height: '16px',\n                              border: '2px solid #2196F3',\n                              borderTop: '2px solid transparent',\n                              borderRadius: '50%',\n                              animation: 'spin 1s linear infinite'\n                            }}></div>\n                            🤖 Generating subtitles...\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["381", "382"], [], "import React from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Modal, Table } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => <>{record.exam?.name}</>,\r\n    },\r\n    {\r\n      title: \"Date\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <>{moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")}</>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalQuestions\",\r\n      render: (text, record) => <>{record.exam?.totalMarks}</>,\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.exam?.passingMarks}</>,\r\n    },\r\n    {\r\n      title: \"Obtained Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.result?.correctAnswers.length}</>,\r\n    },\r\n    {\r\n      title: \"Verdict\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => <>{record.result?.verdict}</>,\r\n    },\r\n  ];\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"reports-container\">\r\n      <PageTitle title=\"Reports\" />\r\n      <div className=\"divider\"></div>\r\n      <Table \r\n      columns={columns} \r\n      dataSource={reportsData} \r\n      rowKey={(record) => record._id} \r\n      scroll={{ x: true }} \r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["383"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["384", "385", "386", "387", "388"], [], "import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport image from '../../../assets/person.png';\r\nimport { IoPersonCircleOutline } from \"react-icons/io5\";\r\nimport { FaTrophy } from \"react-icons/fa6\";\r\n\r\nconst Ranking = () => {\r\n    const [rankingData, setRankingData] = useState('');\r\n    const [userRanking, setUserRanking] = useState('');\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [isMobile, setIsMobile] = useState(false);\r\n    const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\r\n\r\n\r\n    const dispatch = useDispatch();\r\n\r\n    const fetchReports = async () => {\r\n        try {\r\n            const response = await getAllReportsForRanking();\r\n            if (response.success) {\r\n                setRankingData(response.data);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchReports();\r\n                    dispatch(HideLoading());\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (window.innerWidth < 700) {\r\n            setIsMobile(true);\r\n        }\r\n        else {\r\n            setIsMobile(false);\r\n        }\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const getUserStats = () => {\r\n        const Ranking = rankingData\r\n            .map((user, index) => ({\r\n                user,\r\n                ranking: index + 1,\r\n            }))\r\n            .filter((item) => item.user.userId.includes(userData._id));\r\n        setUserRanking(Ranking);\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (rankingData) {\r\n            getUserStats();\r\n        }\r\n    }, [rankingData]);\r\n\r\n    // Helper function to format user ID for mobile devices\r\n    const formatMobileUserId = (userId) => {\r\n        const prefix = userId.slice(0, 4);\r\n        const suffix = userId.slice(-4);\r\n        return `${prefix}.....${suffix}`;\r\n    };\r\n\r\n    return (\r\n        <div className=\"Ranking\">\r\n            {!isAdmin &&\r\n                <>\r\n\r\n                    <PageTitle title=\"Ranking\" />\r\n                    <div className=\"divider\"></div>\r\n                    <div className=\"tabs\">\r\n                        <button\r\n                            className={activeTab === \"overall\" ? \"Active_bg\" : \"Expired_bg\"}\r\n                            onClick={() => setActiveTab(\"overall\")}\r\n                        >\r\n                            Overall Ranking\r\n                        </button>\r\n                        <button\r\n                            className={activeTab === \"class\" ? \"Active_bg\" : \"Expired_bg\"}\r\n                            onClick={() => setActiveTab(\"class\")}\r\n                        >\r\n                            Class Ranking\r\n                        </button>\r\n                    </div>\r\n\r\n                    {rankingData.length > 0 ? (\r\n                        <fieldset className=\"leaderboard\">\r\n                            <legend className=\"legend\"><FaTrophy className=\"trophy\" />LEADERBOARD</legend>\r\n                            <div className=\"data\">\r\n                                {(activeTab === \"overall\"\r\n                                    ? rankingData\r\n                                    : rankingData.filter(user => user.userClass === userData?.class)\r\n                                ).map((user, index) => (\r\n                                    <div key={user.userId} className=\"row\">\r\n                                        <div className={`position ${(index === 0 || index === 1 || index === 2) ? 'medal' : 'number'}`}>\r\n                                            {index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : index + 1}\r\n                                        </div>\r\n                                        <div>\r\n                                            {user.userPhoto ? (\r\n                                                <img\r\n                                                    className=\"profile\"\r\n                                                    src={user.userPhoto}\r\n                                                    alt=\"profile\"\r\n                                                    onError={(e) => { e.target.src = image }}\r\n                                                />\r\n                                            ) : (\r\n                                                <IoPersonCircleOutline className=\"profile-icon\" />\r\n                                            )}\r\n                                        </div>\r\n                                        <div className={`flex ${user.subscriptionStatus === \"active\" ? 'Active_bg' : 'Expired_bg'}`}>\r\n                                            <div className=\"name\">{user.userName}</div>\r\n                                            <div className=\"school\">{user.userSchool || 'Not Enrolled'}</div>\r\n                                            <div className=\"class\">{user.userClass || 'Not Enrolled'}</div>\r\n                                            <div className=\"score\">{user.score}</div>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        </fieldset>\r\n                    ) : (\r\n                        <div>No Ranking yet.</div>\r\n                    )}\r\n\r\n                </>\r\n            }\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Ranking;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["389", "390"], [], "import React, { useEffect, useState, Suspense } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\n\r\nimport { message } from \"antd\";\r\nconst Test = () => {\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n\r\n\r\n    useEffect(() => {\r\n        const getUserData = async () => {\r\n            try {\r\n                const response = await getUserInfo();\r\n                if (response.success) {\r\n                    if (response.data.isAdmin) {\r\n                        setIsAdmin(true);\r\n                    } else {\r\n                        setIsAdmin(false);\r\n                        setUserData(response.data);\r\n                    }\r\n                } else {\r\n                    message.error(response.message);\r\n                }\r\n            } catch (error) {\r\n                message.error(error.message);\r\n            }\r\n        };\r\n        if (localStorage.getItem(\"token\")) {\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        // <Suspense fallback={<div>Loading...</div>}>\r\n        <div className=\"\">\r\n            <div>{userData.name}</div>\r\n            <div>{userData.school}</div>\r\n            <div>{userData.class}</div>\r\n        </div>\r\n        // </Suspense>\r\n    );\r\n}\r\n\r\nexport default Test;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["391"], [], "import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Rate } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addReview, getAllReviews } from \"../../../apicalls/reviews\";\r\nimport image from '../../../assets/person.png';\r\n\r\nconst AboutUs = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [userRating, setUserRating] = useState('');\r\n    const [userText, setUserText] = useState('');\r\n    const [reviews, setReviews] = useState('');\r\n    const [userOldReview, setUserOldReview] = useState(null);\r\n    const dispatch = useDispatch();\r\n\r\n    const getReviews = async () => {\r\n        try {\r\n            const response = await getAllReviews();\r\n            if (response.success) {\r\n                setReviews(response.data.reverse());\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await getReviews();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const handleRatingChange = (value) => {\r\n        setUserRating(value);\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        if (userRating === '' || userRating === 0 || userText === '') {\r\n            return;\r\n        }\r\n        try {\r\n            const data = {\r\n                rating: userRating,\r\n                text: userText\r\n            }\r\n            const response = await addReview(data);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                getReviews();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n            dispatch(HideLoading());\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (reviews) {\r\n            const userInReview = reviews.find(review => review.user._id === userData._id);\r\n            setUserOldReview(userInReview);\r\n        }\r\n    }, [reviews, userData]);\r\n\r\n    return (\r\n        <div className=\"AboutUs\">\r\n            {!isAdmin &&\r\n                <>\r\n                    <PageTitle title=\"About Us\" />\r\n                    <div className=\"divider\"></div>\r\n                    <p className=\"info-para\">\r\n                        Welcome to our web application! Lorem ipsum dolor sit amet, consectetur adipiscing elit.\r\n                        Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,\r\n                        quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\n                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                    </p>\r\n                    {!userOldReview ?\r\n                        <>\r\n                            <h1>Feedback</h1>\r\n                            <p>\r\n                                We strive to provide an exceptional user experience and value your feedback.<br />\r\n                                Please take a moment to rate our web app:\r\n                            </p>\r\n                            <div><b>Rate Your Experience:</b></div>\r\n                            <div className=\"rating\">\r\n                                <div>\r\n                                    <Rate defaultValue={0} onChange={handleRatingChange} />\r\n                                    <br />\r\n                                    <textarea\r\n                                        className=\"rating-text\"\r\n                                        placeholder=\"Share your thoughts...\"\r\n                                        rows={4}\r\n                                        value={userText}\r\n                                        onChange={(e) => setUserText(e.target.value)}\r\n                                    />\r\n                                </div>\r\n                                <button onClick={handleSubmit}>Submit</button>\r\n                            </div>\r\n                        </>\r\n                        :\r\n                        <>\r\n                            <h2>Your Feedback</h2>\r\n                            <div className=\"p-rating-div\">\r\n                                <div className=\"profile-row\">\r\n                                    <img className=\"profile\" src={userOldReview.user.profileImage ? userOldReview.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                    <p>{userOldReview.user.name}</p>\r\n                                </div>\r\n                                <Rate defaultValue={userOldReview.rating} className=\"rate\" disabled={true} />\r\n                                <br />\r\n                                <div className=\"text\">{userOldReview.text}</div>\r\n                            </div>\r\n                        </>\r\n                    }\r\n                    <h2>Previous Reviews</h2>\r\n                    {reviews ?\r\n                        <div className=\"p-ratings\">\r\n                            {reviews.map((review, index) => (\r\n                                <div key={index}>\r\n                                    {userOldReview?.user._id !== review.user?._id && review.user?._id &&\r\n                                        <div className=\"p-rating-div\">\r\n                                            <div className=\"profile-row\">\r\n                                                <img className=\"profile\" src={review.user.profileImage ? review.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                                <p>{review.user.name}</p>\r\n                                            </div>\r\n                                            <Rate defaultValue={review.rating} className=\"rate\" disabled={true} />\r\n                                            <br />\r\n                                            <div className=\"text\">{review.text}</div>\r\n                                        </div>\r\n                                    }\r\n                                </div>\r\n                            ))\r\n                            }\r\n                        </div>\r\n                        :\r\n                        <div>\r\n                            No reviews yet.    \r\n                        </div>\r\n                    }\r\n                </>\r\n            }\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AboutUs;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["392", "393"], [], "import React from \"react\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Table } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReports } from \"../../../apicalls/reports\";\r\nimport { useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nfunction AdminReports() {\r\n  const [reportsData, setReportsData] = React.useState([]);\r\n  const [pagination, setPagination] = React.useState({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0, // total number of records\r\n  });\r\n  const dispatch = useDispatch();\r\n  const [filters, setFilters] = React.useState({\r\n    examName: \"\",\r\n    userName: \"\",\r\n  });\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => <>{record.exam.name}</>,\r\n    },\r\n    {\r\n      title: \"User Name\",\r\n      dataIndex: \"userName\",\r\n      render: (text, record) => <>{record.user.name}</>,\r\n    },\r\n    {\r\n      title: \"Date\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <>{moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")}</>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalQuestions\",\r\n      render: (text, record) => <>{record.exam.totalMarks}</>,\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.exam.passingMarks}</>,\r\n    },\r\n    {\r\n      title: \"Obtained Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.result.correctAnswers.length}</>,\r\n    },\r\n    {\r\n      title: \"Verdict\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => <>{record.result.verdict}</>,\r\n    },\r\n  ];\r\n\r\n  const getData = async (tempFilters, page = 1, limit = 10) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReports({\r\n        ...tempFilters,\r\n        page,\r\n        limit,\r\n      });\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        setPagination({\r\n          ...pagination,\r\n          current: page,\r\n          total: response.pagination.totalReports,\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  }, [filters, pagination.current]);\r\n\r\n  const handleTableChange = (pagination) => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title=\"Reports\" />\r\n      <div className=\"divider\"></div>\r\n      <div className=\"flex gap-2\">\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Exam\"\r\n          value={filters.examName}\r\n          onChange={(e) => setFilters({ ...filters, examName: e.target.value })}\r\n        />\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"User\"\r\n          value={filters.userName}\r\n          onChange={(e) => setFilters({ ...filters, userName: e.target.value })}\r\n        />\r\n        <button\r\n          className=\"primary-outlined-btn\"\r\n          onClick={() => {\r\n            setFilters({\r\n              examName: \"\",\r\n              userName: \"\",\r\n            });\r\n            getData({\r\n              examName: \"\",\r\n              userName: \"\",\r\n            });\r\n          }}\r\n        >\r\n          Clear\r\n        </button>\r\n        <button\r\n          className=\"primary-contained-btn\"\r\n          onClick={() => getData(filters, 1, pagination.pageSize)}\r\n        >\r\n          Search\r\n        </button>\r\n      </div>\r\n      <Table\r\n  columns={columns}\r\n  dataSource={reportsData}\r\n  className=\"mt-2\"\r\n  pagination={{\r\n    current: pagination.current,\r\n    total: pagination.total,\r\n    showSizeChanger: false, // Disables size changer as per your request\r\n    onChange: (page) => {\r\n      setPagination({\r\n        ...pagination,\r\n        current: page,\r\n      });\r\n      getData(filters, page); // Pass the page, no need to pass pageSize\r\n    },\r\n  }}\r\n/>\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdminReports;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["394", "395"], [], "import { message, Table } from \"antd\";\r\nimport React, { useEffect } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport {\r\n  getAllUsers,\r\n  blockUserById,\r\n  deleteUserById,\r\n} from \"../../../apicalls/users\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { MdDelete } from \"react-icons/md\";\r\n\r\nfunction Users() {\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  const getUsersData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllUsers();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setUsers(response.users);\r\n        console.log(\"users\", response);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const blockUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await blockUserById({\r\n        studentId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteUserById({ studentId });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(\"User deleted successfully\");\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"School\",\r\n      dataIndex: \"school\",\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Email\",\r\n      dataIndex: \"email\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center justify-between \">\r\n          <button onClick={() => blockUser(record.studentId)}>\r\n            {record.isBlocked ? \"Unblock\" : \"Block\"}\r\n          </button>\r\n\r\n          <span\r\n            onClick={() => {\r\n              if (\r\n                window.confirm(\"Are you sure you want to delete this user?\")\r\n              ) {\r\n                deleteUser(record.studentId);\r\n              }\r\n            }}\r\n            style={{ color: \"red\", cursor: \"pointer\" }}\r\n            className=\"cursor-pointer\"\r\n          >\r\n            <MdDelete fontSize={20} />\r\n          </span>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <PageTitle title=\"Users\" />\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table\r\n        columns={columns}\r\n        dataSource={users}\r\n        rowKey={(record) => record.studentId}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Users;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", ["396", "397"], [], "import { Form, message, Select } from \"antd\"; // Added Select for dropdown\r\nimport React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\r\n\r\nfunction Register() {\r\n  const [verification, setVerification] = useState(false);\r\n  const [data, setData] = useState(\"\");\r\n  const [otp, setOTP] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [schoolType, setSchoolType] = useState(\"\"); // State to store selected school type\r\n  const navigate = useNavigate();\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      const response = await registerUser(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        navigate(\"/login\");\r\n      } else {\r\n        message.error(response.message);\r\n        setVerification(false);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      setVerification(false);\r\n    }\r\n    console.log(values);\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === otp) {\r\n      onFinish(data);\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  const generateOTP = async (formData) => {\r\n    if (!formData.name || !formData.email || !formData.password) {\r\n      message.error(\"Please fill all fields!\");\r\n      return;\r\n    }\r\n    setLoading(true);\r\n    try {\r\n      const response = await sendOTP(formData);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setData(formData);\r\n        setOTP(response.data);\r\n        setVerification(true);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  const handleSchoolTypeChange = (value) => {\r\n    setSchoolType(value); // Update the state with selected school type\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex justify-center items-center bg-primary main\">\r\n      <div className=\"card p-3 bg-white\">\r\n        {verification ? (\r\n          <div>\r\n            <h1 className=\"text-2xl\">\r\n              - Verification<i className=\"ri-user-add-line\"></i>\r\n            </h1>\r\n            <div className=\"divider\"></div>\r\n            <Form layout=\"vertical\" className=\"mt-2\" onFinish={verifyUser}>\r\n              <Form.Item name=\"otp\" label=\"OTP\" initialValue=\"\">\r\n                <input type=\"number\" />\r\n              </Form.Item>\r\n              <div className=\"flex flex-col gap-2\">\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"primary-contained-btn mt-2 w-100\"\r\n                >\r\n                  Submit\r\n                </button>\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex flex-col\">\r\n            <h1 className=\"text-2xl\">\r\n              - REGISTER<i className=\"ri-user-add-line\"></i>\r\n            </h1>\r\n            <div className=\"divider\"></div>\r\n            <Form layout=\"vertical\" className=\"mt-2\" onFinish={generateOTP}>\r\n              <Form.Item name=\"name\" label=\"Name\" initialValue=\"\" rules={[{ required: true, message: \"Please enter your name!\" }]}>\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n              <Form.Item name=\"school\" label=\"School\" initialValue=\"\" rules={[{ required: true, message: \"Please enter your school!\" }]}>\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"level\" label=\"Level\" initialValue=\"\" rules={[{ required: true, message: \"Please select your level!\" }]}\r\n              >\r\n                <select onChange={(e) => setSchoolType(e.target.value)}>\r\n                  <option value=\"\" disabled selected>\r\n                    Select Level\r\n                  </option>\r\n                  <option value=\"Primary\">Primary</option>\r\n                  <option value=\"Secondary\">Secondary</option>\r\n                  <option value=\"Advance\">Advance</option>\r\n                </select>\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"class\" label=\"Class\" initialValue=\"\" rules={[{ required: true, message: \"Please select your class!\" }]}>\r\n                <select>\r\n                  <option value=\"\" disabled selected>\r\n                    Select Class\r\n                  </option>\r\n                  {schoolType === \"primary\" && (\r\n                    <>\r\n                      <option value=\"1\">1</option>\r\n                      <option value=\"2\">2</option>\r\n                      <option value=\"3\">3</option>\r\n                      <option value=\"4\">4</option>\r\n                      <option value=\"5\">5</option>\r\n                      <option value=\"6\">6</option>\r\n                      <option value=\"7\">7</option>\r\n                    </>\r\n                  )}\r\n                  {schoolType === \"secondary\" && (\r\n                    <>\r\n                      <option value=\"Form-1\">Form-1</option>\r\n                      <option value=\"Form-2\">Form-2</option>\r\n                      <option value=\"Form-3\">Form-3</option>\r\n                      <option value=\"Form-4\">Form-4</option>\r\n                    </>\r\n                  )}\r\n                  {schoolType === \"advance\" && (\r\n                    <>\r\n                      <option value=\"Form-5\">Form-5</option>\r\n                      <option value=\"Form-6\">Form-6</option>\r\n                    </>\r\n                  )}\r\n                </select>\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"email\" label=\"Email\" initialValue=\"\" rules={[{ required: true, message: \"Please enter your email!\" }]}>\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phoneNumber\"\r\n                label=\"Phone Number\"\r\n                initialValue=\"\"\r\n                rules={[\r\n                  {\r\n                    required: true,\r\n                    message: \"Please enter your phone number!\",\r\n                  },\r\n                  {\r\n                    pattern: /^\\d{10}$/,\r\n                    message: \"Phone number must be exactly 10 digits!\",\r\n                  },\r\n                ]}\r\n                extra=\"This phone number will be used for the payment process.\"\r\n              >\r\n                <input type=\"text\" maxLength=\"10\" />\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"password\" label=\"Password\" initialValue=\"\" rules={[{ required: true, message: \"Please enter your password!\" }]}>\r\n                <input type=\"password\" />\r\n              </Form.Item>\r\n\r\n              <div className=\"flex flex-col gap-2\">\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"primary-contained-btn mt-2 w-100\"\r\n                  disabled={loading}\r\n                >\r\n                  Register\r\n                </button>\r\n                <Link to=\"/login\">Already a member? Login</Link>\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Register;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", ["398"], [], "import { Form, message } from \"antd\";\r\nimport React from \"react\";\r\nimport './index.css';\r\nimport Logo from '../../../assets/logo.png';\r\nimport { useDispatch } from \"react-redux\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { loginUser } from \"../../../apicalls/users\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Login() {\r\n  const navigate = useNavigate()\r\n  const dispatch = useDispatch();\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await loginUser(values);\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n\r\n        message.success(response.message);\r\n        localStorage.setItem(\"token\", response.data);\r\n\r\n        if (response.response.isAdmin) {\r\n          window.location.href = \"/admin/users\";\r\n        }\r\n\r\n        if (!response.response.isAdmin) {\r\n          window.location.href = \"/user/quiz\";\r\n\r\n        }\r\n\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex justify-center items-center h-screen w-screen bg-primary\">\r\n      <div className=\"card p-3 bg-white\">\r\n        <div className=\"flex flex-col\">\r\n          <div className=\"flex justify-center\">\r\n            <img src={Logo} alt=\"brainwave-logo\" className=\"login-logo\"/>\r\n          </div>\r\n          <div className=\"divider\"></div>\r\n          <Form layout=\"vertical\" className=\"mt-2\" onFinish={onFinish}>\r\n            <Form.Item name=\"email\" label=\"Email\" initialValue=\"\">\r\n              <input type=\"text\" />\r\n            </Form.Item>\r\n            <Form.Item name=\"password\" label=\"Password\" initialValue=\"\">\r\n              <input type=\"password\" />\r\n            </Form.Item>\r\n\r\n            <div className=\"flex flex-col gap-2\">\r\n              <button\r\n                type=\"submit\"\r\n                className=\"primary-contained-btn mt-2 w-100\"\r\n              >\r\n                Login\r\n              </button>\r\n              <Link to=\"/register\" className=\"underline\">\r\n                Not a member? Register\r\n              </Link>\r\n            </div>\r\n          </Form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["399", "400", "401", "402", "403"], [], "import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport {\r\n  addQuestion,\r\n  addReply,\r\n  getAllQuestions,\r\n  deleteQuestion,\r\n  updateQuestion,\r\n  updateReplyStatus,\r\n} from \"../../../apicalls/forum\";\r\nimport image from \"../../../assets/person.png\";\r\nimport { FaPencilAlt } from \"react-icons/fa\";\r\nimport { MdDelete, MdMessage } from \"react-icons/md\";\r\nimport { FaCheck } from \"react-icons/fa\";\r\n\r\nconst Forum = () => {\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n  const [userData, setUserData] = useState(\"\");\r\n  const [questions, setQuestions] = useState([]);\r\n  const [expandedReplies, setExpandedReplies] = useState({});\r\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n  const [editQuestion, setEditQuestion] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [form2] = Form.useForm();\r\n  const dispatch = useDispatch();\r\n  const [replyRefs, setReplyRefs] = useState({});\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(10);\r\n  const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n  const fetchQuestions = async (page) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllQuestions({ page, limit }); // Pass query params to API call\r\n      if (response.success) {\r\n        console.log(response.data);\r\n        setQuestions(response.data); // No need to reverse as backend will handle order\r\n        // setCurrentPage(page);\r\n        setTotalQuestions(response.totalQuestions);\r\n        setTotalPages(response.totalPages);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchQuestions(currentPage);\r\n  }, [currentPage, limit]);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        if (response.data.isAdmin) {\r\n          setIsAdmin(true);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        } else {\r\n          setIsAdmin(false);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const toggleReplies = (questionId) => {\r\n    setExpandedReplies((prevExpandedReplies) => ({\r\n      ...prevExpandedReplies,\r\n      [questionId]: !prevExpandedReplies[questionId],\r\n    }));\r\n  };\r\n\r\n  const handleAskQuestion = async (values) => {\r\n    try {\r\n      const response = await addQuestion(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setAskQuestionVisible(false);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleReply = (questionId) => {\r\n    setReplyQuestionId(questionId);\r\n  };\r\n\r\n  const handleReplySubmit = async (values) => {\r\n    try {\r\n      const payload = {\r\n        questionId: replyQuestionId,\r\n        text: values.text,\r\n      };\r\n      const response = await addReply(payload);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setReplyQuestionId(null);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n      setReplyRefs((prevRefs) => ({\r\n        ...prevRefs,\r\n        [replyQuestionId]: React.createRef(),\r\n      }));\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  const handleEdit = (question) => {\r\n    setEditQuestion(question);\r\n  };\r\n\r\n  const handleDelete = async (question) => {\r\n    try {\r\n      const confirmDelete = window.confirm(\r\n        \"Are you sure you want to delete this question?\"\r\n      );\r\n      if (!confirmDelete) {\r\n        return;\r\n      }\r\n      const response = await deleteQuestion(question._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleUpdateQuestion = async (values) => {\r\n    try {\r\n      const response = await updateQuestion(values, editQuestion._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEditQuestion(null);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleCancelUpdate = () => {\r\n    setEditQuestion(\"\");\r\n  };\r\n  const handleCancelAdd = () => {\r\n    setAskQuestionVisible(false);\r\n    form.resetFields();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editQuestion) {\r\n      form2.setFieldsValue({\r\n        title: editQuestion.title,\r\n        body: editQuestion.body,\r\n      });\r\n    } else {\r\n      form2.resetFields();\r\n    }\r\n  }, [editQuestion]);\r\n\r\n  const handleUpdateStatus = async (questionId, replyId, status) => {\r\n    try {\r\n      const response = await updateReplyStatus({ replyId, status }, questionId);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"Forum\">\r\n        <PageTitle title=\"Forum\" />\r\n        <div className=\"divider\"></div>\r\n\r\n        <div>\r\n          <p>\r\n            Welcome to the forum! Feel free to ask questions, share your\r\n            thoughts, and engage with the community.\r\n          </p>\r\n          <Button\r\n            onClick={() => setAskQuestionVisible(true)}\r\n            style={{ marginBottom: 20 }}\r\n          >\r\n            Ask a Question\r\n          </Button>\r\n        </div>\r\n\r\n        {askQuestionVisible && (\r\n          <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n            <Form.Item\r\n              name=\"title\"\r\n              label=\"Title\"\r\n              rules={[{ required: true, message: \"Please enter the title\" }]}\r\n            >\r\n              <Input style={{ padding: \"18px 12px\" }} />\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"body\"\r\n              label=\"Body\"\r\n              rules={[{ required: true, message: \"Please enter the body\" }]}\r\n            >\r\n              <Input.TextArea />\r\n            </Form.Item>\r\n            <Form.Item>\r\n              <Button type=\"primary\" htmlType=\"submit\">\r\n                Ask Question\r\n              </Button>\r\n              <Button onClick={handleCancelAdd} style={{ marginLeft: 10 }}>\r\n                Cancel\r\n              </Button>\r\n            </Form.Item>\r\n          </Form>\r\n        )}\r\n\r\n        {questions.length === 0 && <div>Loading...</div>}\r\n\r\n        {questions.map((question) => (\r\n          <div key={question._id} className=\"forum-question-container\">\r\n            <div className=\"question\">\r\n              <div className=\"profile-row\">\r\n                <div className=\"profile-details\">\r\n                  <Avatar\r\n                    src={\r\n                      question.user.profileImage\r\n                        ? question.user.profileImage\r\n                        : image\r\n                    }\r\n                    alt=\"profile\"\r\n                    size={50}\r\n                  />\r\n                  <p>{question.user.name}</p>\r\n                  <p className=\"date\">\r\n                    {new Date(question.createdAt).toLocaleString(undefined, {\r\n                      minute: \"numeric\",\r\n                      hour: \"numeric\",\r\n                      day: \"numeric\",\r\n                      month: \"numeric\",\r\n                      year: \"numeric\",\r\n                    })}\r\n                  </p>\r\n                </div>\r\n                {(userData._id === question.user._id || userData.isAdmin) && (\r\n                  <div className=\"icons\">\r\n                    <FaPencilAlt onClick={() => handleEdit(question)} />\r\n                    <MdDelete\r\n                      size={22}\r\n                      color=\"red\"\r\n                      onClick={() => handleDelete(question)}\r\n                    />\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"title\">{question.title}</div>\r\n              <div className=\"body\">{question.body}</div>\r\n              <Button onClick={() => toggleReplies(question._id)}>\r\n                {expandedReplies[question._id]\r\n                  ? \"Collapse Replies\"\r\n                  : \"Expand Replies\"}\r\n              </Button>\r\n              <Button onClick={() => handleReply(question._id)}>Reply</Button>\r\n              <Button\r\n                className=\"ml-auto w-fit \"\r\n                style={{ float: \"inline-end\" }}\r\n              >\r\n                <div style={{ display: \"flex\" }}>\r\n                  <span style={{ padding: \"6px\", display: \"flex\" }}>\r\n                    <MdMessage />\r\n                  </span>\r\n                  <span>{question.replies.length}</span>\r\n                </div>\r\n              </Button>\r\n            </div>\r\n            {editQuestion && editQuestion._id === question._id && (\r\n              <Form\r\n                form={form2}\r\n                onFinish={handleUpdateQuestion}\r\n                layout=\"vertical\"\r\n                initialValues={{\r\n                  title: editQuestion.title,\r\n                  body: editQuestion.body,\r\n                }}\r\n              >\r\n                <Form.Item\r\n                  name=\"title\"\r\n                  label=\"Title\"\r\n                  rules={[\r\n                    { required: true, message: \"Please enter the title\" },\r\n                  ]}\r\n                >\r\n                  <Input style={{ padding: \"18px 12px\" }} />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"body\"\r\n                  label=\"Body\"\r\n                  rules={[{ required: true, message: \"Please enter the body\" }]}\r\n                >\r\n                  <Input.TextArea />\r\n                </Form.Item>\r\n                <Form.Item>\r\n                  <Button type=\"primary\" htmlType=\"submit\">\r\n                    Update Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelUpdate}\r\n                    style={{ marginLeft: 10 }}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </Form.Item>\r\n              </Form>\r\n            )}\r\n            {expandedReplies[question._id] && (\r\n              <div className=\"replies\">\r\n                {question.replies.map((reply) => (\r\n                  <div\r\n                    key={reply._id}\r\n                    className={`reply ${\r\n                      reply.user.isAdmin\r\n                        ? \"admin-reply\"\r\n                        : reply.isVerified\r\n                        ? \"verified-reply\"\r\n                        : \"\"\r\n                    }`}\r\n                  >\r\n                    {reply.isVerified && <FaCheck color=\"green\" size={30} />}\r\n                    <div>\r\n                      <div className=\"profile-details\">\r\n                        <Avatar\r\n                          src={\r\n                            reply.user.profileImage\r\n                              ? reply.user.profileImage\r\n                              : image\r\n                          }\r\n                          alt=\"profile\"\r\n                          size={50}\r\n                        />\r\n                        <p>{reply.user.name}</p>\r\n                        <p className=\"date\">\r\n                          {new Date(question.createdAt).toLocaleString(\r\n                            undefined,\r\n                            {\r\n                              minute: \"numeric\",\r\n                              hour: \"numeric\",\r\n                              day: \"numeric\",\r\n                              month: \"numeric\",\r\n                              year: \"numeric\",\r\n                            }\r\n                          )}\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"text\">{reply.text}</div>\r\n                      {isAdmin && !reply.user.isAdmin && (\r\n                        <button\r\n                          className=\"verification-btn\"\r\n                          onClick={() =>\r\n                            handleUpdateStatus(\r\n                              question._id,\r\n                              reply._id,\r\n                              !reply.isVerified\r\n                            )\r\n                          }\r\n                        >\r\n                          {!reply.isVerified\r\n                            ? \"Approve Reply\"\r\n                            : \"Disapprove Reply\"}\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <div ref={replyRefs[question._id]}>\r\n              {replyQuestionId === question._id && (\r\n                <Form\r\n                  form={form}\r\n                  onFinish={handleReplySubmit}\r\n                  layout=\"vertical\"\r\n                >\r\n                  <Form.Item\r\n                    name=\"text\"\r\n                    label=\"Your Reply\"\r\n                    rules={[\r\n                      { required: true, message: \"Please enter your reply\" },\r\n                    ]}\r\n                  >\r\n                    <Input.TextArea rows={4} />\r\n                  </Form.Item>\r\n                  <Form.Item>\r\n                    <Button type=\"primary\" htmlType=\"submit\">\r\n                      Submit Reply\r\n                    </Button>\r\n                    <Button\r\n                      onClick={() => setReplyQuestionId(null)}\r\n                      style={{ marginLeft: 10 }}\r\n                    >\r\n                      Cancel\r\n                    </Button>\r\n                  </Form.Item>\r\n                </Form>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n\r\n        <Pagination\r\n          current={currentPage}\r\n          total={totalQuestions}\r\n          pageSize={limit}\r\n          onChange={handlePageChange}\r\n          style={{ marginTop: \"20px\", textAlign: \"center\" }}\r\n          showSizeChanger={false}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Forum;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["404", "405"], [], "import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n  sendOTP,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\", // New field for level\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          ...formData,\r\n          name: response.data.name,\r\n          email: response.data.email,\r\n          school: response.data.school,\r\n          class_: response.data.class,\r\n          level: response.data.level,\r\n          phoneNumber: response.data.phoneNumber,\r\n        });\r\n        fetchReports();\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        dispatch(HideLoading());\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    if (name === \"phoneNumber\" && value.length > 10) return; // Limit to 10 digits\r\n\r\n    // Check if level is changing\r\n    if (name === \"level\" && value !== userDetails.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return; // Don't update formData yet\r\n    }\r\n\r\n    setFormData((prevFormData) => ({\r\n      ...prevFormData,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      ...formData,\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n  const sendOTPRequest = async (email) => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await sendOTP({ email });\r\n      if (response.success) {\r\n        message.success(\"Please verify new email!\");\r\n        setEdit(false);\r\n        setServerGeneratedOTP(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n        discardChanges();\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      discardChanges();\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  const handleUpdate = async ({ skipOTP }) => {\r\n    if (\r\n      formData.name === userDetails.name &&\r\n      formData.email === userDetails.email &&\r\n      formData.school === userDetails.school &&\r\n      formData.class_ === userDetails.class &&\r\n      formData.phoneNumber === userDetails.phoneNumber &&\r\n      formData.level === userDetails.level\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (!formData.name) {\r\n      message.error(\"Please enter your name.\");\r\n      return;\r\n    }\r\n\r\n    if (!formData.class_) {\r\n      message.error(\"Please select a class before updating your profile.\");\r\n      return;\r\n    }\r\n\r\n    // Check if any other fields have been updated\r\n    if (\r\n      formData.name === userDetails.name &&\r\n      formData.email === userDetails.email &&\r\n      formData.school === userDetails.school &&\r\n      formData.class_ === userDetails.class &&\r\n      formData.phoneNumber === userDetails.phoneNumber &&\r\n      formData.level === userDetails.level\r\n    ) {\r\n      message.info(\"No changes detected to update.\");\r\n      return;\r\n    }\r\n\r\n    if (!skipOTP && formData.email !== userDetails.email) {\r\n      sendOTPRequest(formData.email);\r\n      return;\r\n    }\r\n    dispatch(ShowLoading());\r\n\r\n    try {\r\n      const response = await updateUserInfo({\r\n        ...formData,\r\n        userId: userDetails._id\r\n      });\r\n      if (response.success) {\r\n        if (response.levelChanged) {\r\n          message.success(response.message);\r\n          // Refresh the page to ensure all components reflect the new level\r\n          setTimeout(() => {\r\n            window.location.reload();\r\n          }, 2000);\r\n        } else {\r\n          message.success(\"Info updated successfully!\");\r\n        }\r\n        setEdit(false);\r\n        setServerGeneratedOTP(null);\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prevFormData) => ({\r\n      ...prevFormData,\r\n      level: pendingLevelChange,\r\n      class_: \"\", // Reset class when level changes\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setProfileImage(file);\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setImagePreview(reader.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const formData = new FormData();\r\n    formData.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(formData);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === serverGeneratedOTP) {\r\n      handleUpdate({ skipOTP: true });\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"Profile\">\r\n      <PageTitle title=\"Profile\" />\r\n      <div className=\"divider\"></div>\r\n      {serverGeneratedOTP ? (\r\n        <div className=\"card p-3 bg-white\">\r\n          <div>\r\n            <h1 className=\"text-2xl\">\r\n              - Verification<i className=\"ri-user-add-line\"></i>\r\n            </h1>\r\n            <div className=\"divider\"></div>\r\n            <Form layout=\"vertical\" className=\"mt-2\" onFinish={verifyUser}>\r\n              <Form.Item name=\"otp\" label=\"OTP\" initialValue=\"\">\r\n                <input type=\"number\" />\r\n              </Form.Item>\r\n              <div className=\"flex flex-col gap-2\">\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"primary-contained-btn mt-2 w-100\"\r\n                >\r\n                  Submit\r\n                </button>\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <>\r\n          <div className=\"profile-picture-container\">\r\n            <div\r\n              className=\"profile-picture\"\r\n              onClick={() =>\r\n                document.getElementById(\"profileImageInput\").click()\r\n              }\r\n            >\r\n              {imagePreview && <img src={imagePreview} alt=\"Profile Preview\" />}\r\n              {profileImage ? (\r\n                <img src={profileImage} alt=\"Profile\" />\r\n              ) : (\r\n                <>\r\n                  <div className=\"overlay\">Upload Image</div>\r\n                </>\r\n              )}\r\n            </div>\r\n            <input\r\n              id=\"profileImageInput\"\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={handleImageChange}\r\n              style={{ display: \"none\" }}\r\n            />\r\n            {profileImage instanceof File && (\r\n              <button className=\"btn btn-mt\" onClick={handleImageUpload}>\r\n                Save\r\n              </button>\r\n            )}\r\n          </div>\r\n          {userRanking && !userDetails.isAdmin && (\r\n            <div className=\"flex flex-row\">\r\n              <h1 className=\"ranking-data\">\r\n                Position:{\" \"}\r\n                {userRanking[0]?.ranking\r\n                  ? `#${userRanking[0].ranking}`\r\n                  : \"Not Ranked\"}\r\n              </h1>\r\n              <h1 className=\"ranking-data\">\r\n                Score:{\" \"}\r\n                {userRanking[0]?.user.score ? userRanking[0].user.score : \"0\"}\r\n              </h1>\r\n            </div>\r\n          )}\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"name\" className=\"label\">\r\n              User Name\r\n            </label>\r\n            <br />\r\n            <input\r\n              type=\"text\"\r\n              id=\"name\"\r\n              name=\"name\"\r\n              className=\"input\"\r\n              value={formData.name}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            />\r\n          </div>\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"school\" className=\"label\">\r\n              School\r\n            </label>\r\n            <br />\r\n            <input\r\n              type=\"text\"\r\n              id=\"school\"\r\n              name=\"school\"\r\n              className=\"input\"\r\n              value={formData.school ? formData.school : \"\"}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            />\r\n          </div>\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"level\" className=\"label\">\r\n              Level\r\n            </label>\r\n            <br />\r\n            <select\r\n              id=\"level\"\r\n              name=\"level\"\r\n              className=\"input\"\r\n              value={formData.level}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            >\r\n              <option value=\"\">Select Level</option>\r\n              <option value=\"Primary\">Primary</option>\r\n              <option value=\"Secondary\">Secondary</option>\r\n              <option value=\"Advance\">Advance</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"class\" className=\"label\">\r\n              Class\r\n            </label>\r\n            <br />\r\n            <select\r\n              id=\"class\"\r\n              name=\"class_\"\r\n              className=\"input\"\r\n              value={formData.class_}\r\n              onChange={handleChange}\r\n              disabled={!edit || !formData.level}\r\n            >\r\n              <option value=\"\">Select Class</option>\r\n\r\n              {formData.level === \"Primary\" && (\r\n                <>\r\n                  <option value=\"1\">1</option>\r\n                  <option value=\"2\">2</option>\r\n                  <option value=\"3\">3</option>\r\n                  <option value=\"4\">4</option>\r\n                  <option value=\"5\">5</option>\r\n                  <option value=\"6\">6</option>\r\n                  <option value=\"7\">7</option>\r\n                </>\r\n              )}\r\n\r\n              {formData.level === \"Secondary\" && (\r\n                <>\r\n                  <option value=\"Form-1\">Form-1</option>\r\n                  <option value=\"Form-2\">Form-2</option>\r\n                  <option value=\"Form-3\">Form-3</option>\r\n                  <option value=\"Form-4\">Form-4</option>\r\n                </>\r\n              )}\r\n\r\n              {formData.level === \"Advance\" && (\r\n                <>\r\n                  <option value=\"Form-5\">Form-5</option>\r\n                  <option value=\"Form-6\">Form-6</option>\r\n                </>\r\n              )}\r\n            </select>\r\n          </div>\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"email\" className=\"label\">\r\n              Email Address\r\n            </label>\r\n            <br />\r\n            <input\r\n              type=\"text\"\r\n              id=\"email\"\r\n              name=\"email\"\r\n              className=\"input\"\r\n              value={formData.email}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            />\r\n          </div>\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"email\" className=\"label\">\r\n              Phone Number\r\n            </label>\r\n            <br />\r\n            <input\r\n              type=\"number\"\r\n              id=\"phoneNumber\"\r\n              name=\"phoneNumber\"\r\n              className=\"input\"\r\n              value={formData.phoneNumber}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            />\r\n          </div>\r\n          {!edit ? (\r\n            <div className=\"edit-btn-div\">\r\n              <button className=\"btn\" onClick={(e) => setEdit(true)}>\r\n                Edit\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"btns-container\">\r\n              <button className=\"btn\" onClick={discardChanges}>\r\n                Cancel\r\n              </button>\r\n              <button className=\"btn\" onClick={handleUpdate}>\r\n                Update\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {/* Level Change Confirmation Modal */}\r\n      <Modal\r\n        title=\"Confirm Level Change\"\r\n        open={showLevelChangeModal}\r\n        onOk={handleLevelChangeConfirm}\r\n        onCancel={handleLevelChangeCancel}\r\n        okText=\"Yes, Change Level\"\r\n        cancelText=\"Cancel\"\r\n        okButtonProps={{ danger: true }}\r\n      >\r\n        <div>\r\n          <p><strong>Warning:</strong> You are about to change your level from <strong>{userDetails?.level}</strong> to <strong>{pendingLevelChange}</strong>.</p>\r\n          <p>This will:</p>\r\n          <ul>\r\n            <li>Change your access to content for the new level only</li>\r\n            <li>Reset your class selection</li>\r\n            <li>You will no longer see content from your previous level</li>\r\n            <li>This change will take effect immediately</li>\r\n          </ul>\r\n          <p>Are you sure you want to continue?</p>\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", ["406"], [], "import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { TbArrowBigRightLinesFilled } from \"react-icons/tb\";\r\nimport { AiOutlinePlus } from \"react-icons/ai\";\r\nimport { message, Rate } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReviews } from \"../../../apicalls/reviews\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport Image2 from \"../../../assets/collage-2.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const aboutUsSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [reviews, setReviews] = useState([]); // Initialize as an array\r\n  const dispatch = useDispatch();\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    message: \"\",\r\n  });\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n\r\n  const getReviews = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getAllReviews();\r\n      if (response.success) {\r\n        setReviews(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  useEffect(() => {\r\n    getReviews();\r\n  }, []);\r\n\r\n  const scrollToSection = (ref, offset = 30) => {\r\n    if (ref && ref.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({\r\n        top: sectionTop - offset,\r\n        behavior: \"smooth\"\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n\r\n    try {\r\n      // Assume contactUs returns the parsed JSON response\r\n      const data = await contactUs(formData);\r\n\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" }); // Reset form\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home\">\r\n      <nav className=\"nav\">\r\n        <div className=\"nav-body\">\r\n          <Link to=\"/\" className=\"title\">\r\n            <div>\r\n              <span className=\"colored-title\">Brain</span>Wave\r\n            </div>\r\n          </Link>\r\n\r\n          {/* Hamburger Icon */}\r\n          <div className=\"hamburger\" onClick={() => setMenuOpen(!menuOpen)}>\r\n            ☰\r\n          </div>\r\n\r\n          {/* Navigation Links */}\r\n          <div className={`nav-links ${menuOpen ? \"open\" : \"\"}`}>\r\n            <div\r\n              onClick={() => {\r\n                scrollToSection(homeSectionRef);\r\n                setMenuOpen(false);\r\n              }}\r\n              className=\"link\"\r\n            >\r\n              Home\r\n            </div>\r\n            <div\r\n              onClick={() => {\r\n                scrollToSection(aboutUsSectionRef);\r\n                setMenuOpen(false);\r\n              }}\r\n              className=\"link\"\r\n            >\r\n              About Us\r\n            </div>\r\n            <div\r\n              onClick={() => {\r\n                scrollToSection(reviewsSectionRef);\r\n                setMenuOpen(false);\r\n              }}\r\n              className=\"link\"\r\n            >\r\n              Reviews\r\n            </div>\r\n            <div\r\n              onClick={() => {\r\n                scrollToSection(contactUsRef);\r\n                setMenuOpen(false);\r\n              }}\r\n              className=\"link\"\r\n            >\r\n              Contact Us\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </nav>\r\n      <section ref={homeSectionRef} className=\"section-1\">\r\n        <div className=\"content-1\">\r\n          <div className=\"title\">\r\n            Fueling Bright Futures with <br />\r\n            <span className=\"colored-title\">\r\n              <TbArrowBigRightLinesFilled />\r\n              Education.\r\n            </span>\r\n          </div>\r\n          <p className=\"para\">\r\n            Solutions and flexible online study, you can study anywhere through\r\n            this platform.\r\n          </p>\r\n          <div className=\"btns-container\">\r\n            <Link to=\"/login\" className=\"btn btn-1\">\r\n              Login\r\n            </Link>\r\n            <Link to=\"/register\" className=\"btn btn-2\">\r\n              Sign up\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        <div className=\"content-2\">\r\n          <img src={Image1} alt=\"Collage-1\" className=\"collage\" />\r\n        </div>\r\n      </section>\r\n      <section className=\"section-2\">\r\n        <div className=\"flex-col\">\r\n          <div className=\"number\">7K+</div>\r\n          <div className=\"text\">Success Stories</div>\r\n        </div>\r\n        <div className=\"flex-col\">\r\n          <AiOutlinePlus className=\"plus-icon\" />\r\n        </div>\r\n        <div className=\"flex-col\">\r\n          <div className=\"number\">300+</div>\r\n          <div className=\"text\">Expert Mentors</div>\r\n        </div>\r\n        <div className=\"flex-col\">\r\n          <AiOutlinePlus className=\"plus-icon\" />\r\n        </div>\r\n        <div className=\"flex-col\">\r\n          <div className=\"number\">15K+</div>\r\n          <div className=\"text\">Students Joined</div>\r\n        </div>\r\n        <div className=\"flex-col\">\r\n          <AiOutlinePlus className=\"plus-icon\" />\r\n        </div>\r\n        <div className=\"flex-col\">\r\n          <div className=\"number\">250+</div>\r\n          <div className=\"text\">Trendy Courses</div>\r\n        </div>\r\n      </section>\r\n      <section ref={aboutUsSectionRef} className=\"section-3\">\r\n        <div className=\"content-1\">\r\n          <div className=\"title\">Discover knowledge in limitless realms.</div>\r\n          <p className=\"para\">\r\n            Education serves as the cornerstone of personal and societal\r\n            development. It is a dynamic process that empowers individuals with\r\n            the knowledge, skills, and critical thinking abilities essential for\r\n            success.\r\n          </p>\r\n          <div className=\"btn-container\">\r\n            <Link to=\"/user/about-us\" className=\"btn btn-1\">\r\n              Learn More\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        <div className=\"content-2\">\r\n          <img src={Image2} alt=\"Collage-1\" className=\"collage\" />\r\n        </div>\r\n      </section>\r\n      <section ref={reviewsSectionRef} className=\"section-4\">\r\n        <div className=\"content-1\">\r\n          <div className=\"title\">\r\n            Reviews from <br />\r\n            some students\r\n          </div>\r\n        </div>\r\n        <div className=\"content-2\">\r\n          {reviews.length !== 0 ? (\r\n            reviews.map((review, index) => (\r\n              <div key={index} className=\"review-card\">\r\n                <Rate defaultValue={review.rating} className=\"rate\" disabled />\r\n                <div className=\"text\">\"{review.text}\"</div>\r\n                <div className=\"seperator\"></div>\r\n                <div className=\"name\">{review.user?.name}</div>\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div>No reviews yet.</div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      <section ref={contactUsRef} className=\"contact-section section-4\">\r\n        <div className=\"content-1\">\r\n          <div className=\"title\">Contact Us</div>\r\n        </div>\r\n        <div className=\"contact-container\" style={{ marginTop: \"40px\" }}>\r\n          <div className=\"contact-box\">\r\n            <form className=\"contact-form\" onSubmit={handleSubmit}>\r\n              <div className=\"contact-field\">\r\n                <label className=\"contact-label\">Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"name\"\r\n                  placeholder=\"Your Name\"\r\n                  className=\"contact-input\"\r\n                  value={formData.name}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"contact-field\">\r\n                <label className=\"contact-label\">Email</label>\r\n                <input\r\n                  type=\"email\"\r\n                  name=\"email\"\r\n                  placeholder=\"Your Email\"\r\n                  className=\"contact-input\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"contact-field\">\r\n                <label className=\"contact-label\">Message</label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  placeholder=\"Your Message\"\r\n                  className=\"contact-textarea\"\r\n                  style={{ width: \"93.5%\", padding: \"10px\" }}\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  required\r\n                ></textarea>\r\n              </div>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"contact-submit\"\r\n                disabled={loading}\r\n              >\r\n                {loading ? \"Sending...\" : \"Send Message\"}\r\n              </button>\r\n              {responseMessage && (\r\n                <p className=\"response-message\">{responseMessage}</p>\r\n              )}\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx", ["407", "408"], [], "import React, { useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport \"./index.css\"; // Import the custom CSS\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n      setImageFile(null);\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      alert(\"An error occurred while processing your request. Please try again.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (event) => {\r\n    if (event.key === \"Enter\") {\r\n      handleChat(); // Trigger the handleChat function on Enter key\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"chat-container\">\r\n      {/* Chat messages */}\r\n      <div className=\"chat-messages\">\r\n        {messages.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`message ${msg.role === \"user\" ? \"user-message\" : \"assistant-message\"\r\n              }`}\r\n          >\r\n            <>\r\n              {msg.role === \"assistant\" ? (\r\n                <>\r\n                  {msg?.content ? (\r\n                    <ContentRenderer text={msg.content} />\r\n                  ) : (\r\n                    <p>Unable to get a response from AI</p>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {typeof msg.content === \"string\"\r\n                    ? msg.content\r\n                    : msg.content.map((item, idx) =>\r\n                      item.type === \"text\" ? (\r\n                        <p key={idx}>{item.text}</p>\r\n                      ) : (\r\n                        <img\r\n                          key={idx}\r\n                          src={item.image_url.url}\r\n                          alt=\"User content\"\r\n                          style={{ height: \"100px\" }}\r\n                        />\r\n                      )\r\n                    )}\r\n                </>\r\n              )}\r\n            </>\r\n          </div>\r\n        ))}\r\n        {isLoading && <div className=\"loading-indicator\">Loading...</div>}\r\n      </div>\r\n\r\n      {/* Input and upload */}\r\n      <div className=\"chat-input-container\">\r\n        <textarea\r\n          className=\"chat-input\"\r\n          placeholder=\"Type your message here...\"\r\n          value={prompt}\r\n          onChange={(e) => setPrompt(e.target.value)}\r\n        ></textarea>\r\n        <input\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={(e) => setImageFile(e.target.files[0])}\r\n          style={{ width: \"200px\", borderRadius: \"5px\", marginRight: \"10px\" }}\r\n        />\r\n        <button\r\n          disabled={isLoading}\r\n          className=\"send-button\"\r\n          onClick={handleChat}\r\n        >\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\announcements.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["409", "410", "411", "412", "413"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", [], ["414"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx", ["415", "416"], [], "import React, { useEffect, useState } from \"react\";\r\nimport Mo<PERSON> from \"react-modal\";\r\nimport \"./WaitingModal.css\";\r\n\r\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\r\n\r\nconst WaitingModal = ({ isOpen, onClose }) => {\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={onClose}\r\n            className=\"waiting-modal-content\"\r\n            overlayClassName=\"waiting-modal-overlay\"\r\n        >\r\n            <div className=\"waiting-modal-header\">\r\n                <h2>Please confirm the payment</h2>\r\n            </div>\r\n            <div className=\"waiting-modal-timer\">\r\n                <svg\r\n                    fill=\"#253864\"\r\n                    version=\"1.1\"\r\n                    id=\"Layer_1\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    viewBox=\"0 0 512 512\"\r\n                    width=\"64px\"\r\n                    height=\"64px\"\r\n                    stroke=\"#253864\"\r\n                >\r\n                    <g>\r\n                        <path d=\"M437.019,74.981C388.668,26.629,324.38,0,256,0S123.332,26.629,74.981,74.981C26.629,123.332,0,187.62,0,256 s26.629,132.668,74.981,181.019C123.332,485.371,187.62,512,256,512c64.518,0,126.15-24.077,173.541-67.796l-10.312-11.178 c-44.574,41.12-102.544,63.766-163.229,63.766c-64.317,0-124.786-25.046-170.266-70.527 C40.254,380.786,15.208,320.317,15.208,256S40.254,131.214,85.734,85.735C131.214,40.254,191.683,15.208,256,15.208 s124.786,25.046,170.266,70.527c45.48,45.479,70.526,105.948,70.526,170.265c0,60.594-22.587,118.498-63.599,163.045 l11.188,10.301C487.986,381.983,512,320.421,512,256C512,187.62,485.371,123.332,437.019,74.981z\"></path>\r\n                        <path d=\"M282.819,263.604h63.415v-15.208h-63.415c-1.619-5.701-5.007-10.662-9.536-14.25l35.913-86.701l-14.049-5.82 l-35.908,86.688c-1.064-0.124-2.142-0.194-3.238-0.194c-15.374,0-27.881,12.508-27.881,27.881s12.507,27.881,27.881,27.881 C268.737,283.881,279.499,275.292,282.819,263.604z M243.327,256c0-6.989,5.685-12.673,12.673-12.673 c6.989,0,12.673,5.685,12.673,12.673c0,6.989-5.685,12.673-12.673,12.673C249.011,268.673,243.327,262.989,243.327,256z\"></path>\r\n                        <path d=\"M451.168,256c0-107.616-87.552-195.168-195.168-195.168S60.832,148.384,60.832,256S148.384,451.168,256,451.168 S451.168,363.616,451.168,256z M76.04,256c0-99.231,80.73-179.96,179.96-179.96S435.96,156.769,435.96,256 S355.231,435.96,256,435.96S76.04,355.231,76.04,256z\"></path>\r\n                    </g>\r\n                </svg>\r\n            </div>\r\n\r\n            <p className=\"waiting-modal-footer\">\r\n                Ensure that your payment is confirmed before the timer runs out.\r\n            </p>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nexport default WaitingModal;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["417", "418", "419", "420"], [], "import React, { useState, useEffect } from \"react\";\nimport { Table, Button, Space, Select, Input, message, Modal, Tag, Tooltip } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { \n  getAllStudyMaterials, \n  deleteVideo, \n  deleteNote, \n  deletePastPaper, \n  deleteBook \n} from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaEdit,\n  FaTrash,\n  FaEye,\n  FaFilter,\n  FaSearch\n} from \"react-icons/fa\";\nimport \"./StudyMaterialManager.css\";\n\nconst { Option } = Select;\nconst { Search } = Input;\n\nfunction StudyMaterialManager({ onEdit }) {\n  const dispatch = useDispatch();\n  const [materials, setMaterials] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    materialType: \"\",\n    level: \"\",\n    className: \"\",\n    subject: \"\"\n  });\n  const [searchText, setSearchText] = useState(\"\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Fetch materials\n  const fetchMaterials = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      const response = await getAllStudyMaterials(filters);\n      \n      if (response.status === 200 && response.data.success) {\n        setMaterials(response.data.data || []);\n      } else {\n        message.error(\"Failed to fetch study materials\");\n        setMaterials([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      message.error(\"Failed to fetch study materials\");\n      setMaterials([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  useEffect(() => {\n    fetchMaterials();\n  }, [filters]);\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => {\n      const newFilters = { ...prev, [key]: value };\n      \n      // Reset dependent filters\n      if (key === \"level\") {\n        newFilters.className = \"\";\n        newFilters.subject = \"\";\n      }\n      \n      return newFilters;\n    });\n  };\n\n  // Handle delete\n  const handleDelete = async (material) => {\n    Modal.confirm({\n      title: `Delete ${material.type.replace(\"-\", \" \")}`,\n      content: `Are you sure you want to delete \"${material.title}\"? This action cannot be undone.`,\n      okText: \"Delete\",\n      okType: \"danger\",\n      cancelText: \"Cancel\",\n      onOk: async () => {\n        try {\n          dispatch(ShowLoading());\n          \n          let response;\n          switch (material.type) {\n            case \"videos\":\n              response = await deleteVideo(material._id);\n              break;\n            case \"study-notes\":\n              response = await deleteNote(material._id);\n              break;\n            case \"past-papers\":\n              response = await deletePastPaper(material._id);\n              break;\n            case \"books\":\n              response = await deleteBook(material._id);\n              break;\n            default:\n              throw new Error(\"Invalid material type\");\n          }\n\n          if (response.status === 200 && response.data.success) {\n            message.success(response.data.message);\n            fetchMaterials(); // Refresh the list\n          } else {\n            message.error(response.data?.message || \"Failed to delete material\");\n          }\n        } catch (error) {\n          console.error(\"Error deleting material:\", error);\n          message.error(\"Failed to delete material\");\n        } finally {\n          dispatch(HideLoading());\n        }\n      }\n    });\n  };\n\n  // Get material type icon\n  const getMaterialIcon = (type) => {\n    switch (type) {\n      case \"videos\":\n        return <FaVideo className=\"material-icon video\" />;\n      case \"study-notes\":\n        return <FaFileAlt className=\"material-icon note\" />;\n      case \"past-papers\":\n        return <FaGraduationCap className=\"material-icon paper\" />;\n      case \"books\":\n        return <FaBook className=\"material-icon book\" />;\n      default:\n        return <FaFileAlt className=\"material-icon\" />;\n    }\n  };\n\n  // Get material type label\n  const getMaterialTypeLabel = (type) => {\n    switch (type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return type;\n    }\n  };\n\n  // Filter materials based on search text\n  const filteredMaterials = materials.filter(material =>\n    material.title.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.subject.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.className.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // Table columns\n  const columns = [\n    {\n      title: \"Material\",\n      key: \"material\",\n      width: \"30%\",\n      render: (_, record) => (\n        <div className=\"material-info\">\n          <div className=\"material-header\">\n            {getMaterialIcon(record.type)}\n            <div className=\"material-details\">\n              <div className=\"material-title\">{record.title}</div>\n              <div className=\"material-meta\">\n                <Tag color=\"blue\">{getMaterialTypeLabel(record.type)}</Tag>\n                <span className=\"meta-text\">{record.subject} • Class {record.className}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: \"Level\",\n      dataIndex: \"level\",\n      key: \"level\",\n      width: \"10%\",\n      render: (level) => (\n        <Tag color={level === \"primary\" ? \"green\" : level === \"secondary\" ? \"orange\" : \"purple\"}>\n          {level.charAt(0).toUpperCase() + level.slice(1)}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Class\",\n      dataIndex: \"className\",\n      key: \"className\",\n      width: \"10%\",\n      render: (className) => <span className=\"class-badge\">Class {className}</span>,\n    },\n    {\n      title: \"Subject\",\n      dataIndex: \"subject\",\n      key: \"subject\",\n      width: \"15%\",\n    },\n    {\n      title: \"Year\",\n      dataIndex: \"year\",\n      key: \"year\",\n      width: \"10%\",\n      render: (year) => year || \"-\",\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      width: \"25%\",\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"Edit material\">\n            <Button\n              type=\"primary\"\n              icon={<FaEdit />}\n              size=\"small\"\n              onClick={() => onEdit(record)}\n            >\n              Edit\n            </Button>\n          </Tooltip>\n          \n          <Tooltip title=\"Delete material\">\n            <Button\n              danger\n              icon={<FaTrash />}\n              size=\"small\"\n              onClick={() => handleDelete(record)}\n            >\n              Delete\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"study-material-manager\">\n      <div className=\"manager-header\">\n        <h2>Study Materials Management</h2>\n        <p>Manage all uploaded study materials - edit, delete, and organize content</p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Material Type:</label>\n            <Select\n              placeholder=\"All Types\"\n              value={filters.materialType || undefined}\n              onChange={(value) => handleFilterChange(\"materialType\", value)}\n              allowClear\n              style={{ width: 150 }}\n            >\n              <Option value=\"videos\">Videos</Option>\n              <Option value=\"study-notes\">Study Notes</Option>\n              <Option value=\"past-papers\">Past Papers</Option>\n              <Option value=\"books\">Books</Option>\n            </Select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Level:</label>\n            <Select\n              placeholder=\"All Levels\"\n              value={filters.level || undefined}\n              onChange={(value) => handleFilterChange(\"level\", value)}\n              allowClear\n              style={{ width: 120 }}\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </div>\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Class:</label>\n              <Select\n                placeholder=\"All Classes\"\n                value={filters.className || undefined}\n                onChange={(value) => handleFilterChange(\"className\", value)}\n                allowClear\n                style={{ width: 120 }}\n              >\n                {getClassesForLevel(filters.level).map(cls => (\n                  <Option key={cls} value={cls}>Class {cls}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Subject:</label>\n              <Select\n                placeholder=\"All Subjects\"\n                value={filters.subject || undefined}\n                onChange={(value) => handleFilterChange(\"subject\", value)}\n                allowClear\n                style={{ width: 150 }}\n              >\n                {getSubjectsForLevel(filters.level).map(subject => (\n                  <Option key={subject} value={subject}>{subject}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          <div className=\"filter-group\">\n            <label>Search:</label>\n            <Search\n              placeholder=\"Search materials...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: 200 }}\n              allowClear\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Table */}\n      <div className=\"materials-table\">\n        <Table\n          columns={columns}\n          dataSource={filteredMaterials}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} materials`,\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterialManager;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\index.js", ["421", "422", "423"], [], "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport { Card, Button, Row, Col, Statistic, Table, Tag, Space, message } from \"antd\";\nimport { \n  FaRobot, \n  FaQuestionCircle, \n  FaHistory, \n  FaCog, \n  FaPlus,\n  FaEye,\n  FaCheck,\n  FaTimes\n} from \"react-icons/fa\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getGenerationHistory } from \"../../../apicalls/aiQuestions\";\nimport QuestionGenerationForm from \"./QuestionGenerationForm\";\nimport QuestionPreview from \"./QuestionPreview\";\nimport \"./AIQuestionGeneration.css\";\n\nfunction AIQuestionGeneration() {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [activeView, setActiveView] = useState(\"dashboard\");\n  const [generationHistory, setGenerationHistory] = useState([]);\n  const [selectedGeneration, setSelectedGeneration] = useState(null);\n  const [stats, setStats] = useState({\n    totalGenerations: 0,\n    totalQuestions: 0,\n    approvedQuestions: 0,\n    pendingReview: 0,\n  });\n\n  useEffect(() => {\n    fetchGenerationHistory();\n  }, []);\n\n  const fetchGenerationHistory = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getGenerationHistory({ limit: 20 });\n      if (response.success) {\n        setGenerationHistory(response.data.generations);\n        calculateStats(response.data.generations);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to fetch generation history\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const calculateStats = (generations) => {\n    const stats = generations.reduce((acc, gen) => {\n      acc.totalGenerations += 1;\n      acc.totalQuestions += gen.generatedQuestions.length;\n      acc.approvedQuestions += gen.generatedQuestions.filter(q => q.approved).length;\n      acc.pendingReview += gen.generationStatus === \"completed\" ? 1 : 0;\n      return acc;\n    }, {\n      totalGenerations: 0,\n      totalQuestions: 0,\n      approvedQuestions: 0,\n      pendingReview: 0,\n    });\n    setStats(stats);\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      pending: \"orange\",\n      in_progress: \"blue\",\n      completed: \"green\",\n      failed: \"red\",\n      cancelled: \"gray\",\n    };\n    return colors[status] || \"default\";\n  };\n\n  const historyColumns = [\n    {\n      title: \"Generation ID\",\n      dataIndex: \"_id\",\n      key: \"_id\",\n      render: (id) => id.slice(-8),\n    },\n    {\n      title: \"Exam\",\n      dataIndex: [\"examId\", \"name\"],\n      key: \"examName\",\n    },\n    {\n      title: \"Questions\",\n      dataIndex: \"generatedQuestions\",\n      key: \"questionCount\",\n      render: (questions) => questions.length,\n    },\n    {\n      title: \"Status\",\n      dataIndex: \"generationStatus\",\n      key: \"status\",\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {status.toUpperCase()}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Created\",\n      dataIndex: \"createdAt\",\n      key: \"createdAt\",\n      render: (date) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            icon={<FaEye />}\n            onClick={() => {\n              setSelectedGeneration(record);\n              setActiveView(\"preview\");\n            }}\n          >\n            Preview\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  const renderDashboard = () => (\n    <div className=\"ai-question-dashboard\">\n      <Row gutter={[16, 16]} className=\"mb-4\">\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Total Generations\"\n              value={stats.totalGenerations}\n              prefix={<FaRobot />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Questions Generated\"\n              value={stats.totalQuestions}\n              prefix={<FaQuestionCircle />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Approved Questions\"\n              value={stats.approvedQuestions}\n              prefix={<FaCheck />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Pending Review\"\n              value={stats.pendingReview}\n              prefix={<FaTimes />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        <Col xs={24} md={12}>\n          <Card\n            title=\"Generate New Questions\"\n            className=\"action-card\"\n            actions={[\n              <Button\n                type=\"primary\"\n                icon={<FaPlus />}\n                onClick={() => setActiveView(\"generate\")}\n              >\n                Start Generation\n              </Button>\n            ]}\n          >\n            <p>Create AI-generated questions for your exams using advanced language models.</p>\n            <ul>\n              <li>Multiple choice questions</li>\n              <li>Fill in the blank questions</li>\n              <li>Picture-based questions</li>\n              <li>Tanzania syllabus compliant</li>\n            </ul>\n          </Card>\n        </Col>\n        <Col xs={24} md={12}>\n          <Card\n            title=\"Generation History\"\n            className=\"action-card\"\n            actions={[\n              <Button\n                icon={<FaHistory />}\n                onClick={() => setActiveView(\"history\")}\n              >\n                View History\n              </Button>\n            ]}\n          >\n            <p>Review and manage your previous question generations.</p>\n            <ul>\n              <li>Track generation status</li>\n              <li>Preview generated questions</li>\n              <li>Approve or reject questions</li>\n              <li>Add approved questions to exams</li>\n            </ul>\n          </Card>\n        </Col>\n      </Row>\n\n      <Card title=\"Recent Generations\" className=\"mt-4\">\n        <Table\n          dataSource={generationHistory.slice(0, 5)}\n          columns={historyColumns}\n          pagination={false}\n          rowKey=\"_id\"\n        />\n        {generationHistory.length > 5 && (\n          <div className=\"text-center mt-3\">\n            <Button onClick={() => setActiveView(\"history\")}>\n              View All Generations\n            </Button>\n          </div>\n        )}\n      </Card>\n    </div>\n  );\n\n  const renderHistory = () => (\n    <Card title=\"Generation History\">\n      <Table\n        dataSource={generationHistory}\n        columns={historyColumns}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n        }}\n        rowKey=\"_id\"\n      />\n    </Card>\n  );\n\n  const renderContent = () => {\n    switch (activeView) {\n      case \"generate\":\n        return (\n          <QuestionGenerationForm\n            onBack={() => setActiveView(\"dashboard\")}\n            onSuccess={() => {\n              setActiveView(\"dashboard\");\n              fetchGenerationHistory();\n            }}\n          />\n        );\n      case \"preview\":\n        return (\n          <QuestionPreview\n            generation={selectedGeneration}\n            onBack={() => setActiveView(\"dashboard\")}\n            onSuccess={() => {\n              setActiveView(\"dashboard\");\n              fetchGenerationHistory();\n            }}\n          />\n        );\n      case \"history\":\n        return renderHistory();\n      default:\n        return renderDashboard();\n    }\n  };\n\n  return (\n    <div className=\"ai-question-generation\">\n      <PageTitle title=\"AI Question Generation\" />\n      \n      {activeView === \"dashboard\" && (\n        <div className=\"page-header\">\n          <h2>AI Question Generation Dashboard</h2>\n          <p>Generate high-quality questions using artificial intelligence</p>\n        </div>\n      )}\n\n      {renderContent()}\n    </div>\n  );\n}\n\nexport default AIQuestionGeneration;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionGenerationForm.js", ["424"], [], "import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport {\n  Card,\n  Form,\n  Select,\n  InputNumber,\n  Button,\n  Row,\n  Col,\n  Checkbox,\n  message,\n  Divider,\n  Alert,\n  Progress\n} from \"antd\";\nimport { FaArrowLeft, FaRobot } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport {\n  generateQuestions,\n  getSubjectsForLevel,\n  getSyllabusTopics\n} from \"../../../apicalls/aiQuestions\";\nimport AutoGenerateExamModal from \"./AutoGenerateExamModal\";\nimport AILoginModal from \"../../../components/AILoginModal\";\nimport { useAIAuth } from \"../../../hooks/useAIAuth\";\n\nconst { Option } = Select;\n\nfunction QuestionGenerationForm({ onBack, onSuccess }) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  // Enhanced authentication\n  const {\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    loading: authLoading,\n    requiresUpgrade,\n    needsLogin,\n    handleLoginSuccess,\n    requireAIAuth,\n    sessionExpiringSoon,\n    timeUntilExpiry\n  } = useAIAuth();\n\n  const [exams, setExams] = useState([]);\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableTopics, setAvailableTopics] = useState([]);\n  const [selectedLevel, setSelectedLevel] = useState(\"\");\n  const [selectedClass, setSelectedClass] = useState(\"\");\n  const [selectedSubjects, setSelectedSubjects] = useState([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n\n  const fetchExams = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      if (response.success && Array.isArray(response.data)) {\n        // Filter out any null or undefined values\n        const validExams = response.data.filter(exam => exam && exam._id);\n        setExams(validExams);\n      } else {\n        message.error(\"Failed to fetch exams\");\n        setExams([]); // Ensure exams is always an array\n      }\n    } catch (error) {\n      message.error(\"Error fetching exams\");\n      setExams([]); // Ensure exams is always an array\n    } finally {\n      dispatch(HideLoading());\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Only fetch exams if we have authentication, otherwise let the auth hook handle it\n    if (isAuthenticated && hasAIAccess && !authLoading) {\n      fetchExams();\n    }\n  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);\n\n  const handleLevelChange = async (level) => {\n    setSelectedLevel(level);\n    setSelectedClass(\"\");\n    setSelectedSubjects([]);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      class: undefined,\n      subjects: [],\n      syllabusTopics: []\n    });\n\n    try {\n      const response = await getSubjectsForLevel(level);\n      if (response.success) {\n        setAvailableSubjects(response.data);\n      } else {\n        message.error(\"Failed to fetch subjects\");\n      }\n    } catch (error) {\n      message.error(\"Error fetching subjects\");\n    }\n  };\n\n  const handleClassChange = (className) => {\n    setSelectedClass(className);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If subjects are already selected, fetch topics\n    if (selectedSubjects.length > 0) {\n      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);\n    }\n  };\n\n  const handleSubjectsChange = (subjects) => {\n    setSelectedSubjects(subjects);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If class is selected, fetch topics\n    if (selectedClass) {\n      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);\n    }\n\n    // Note: Auto-generate exam functionality moved to modal\n  };\n\n  const fetchTopicsForSubjects = async (level, className, subjects) => {\n    if (!level || !className || subjects.length === 0) return;\n\n    try {\n      const allTopics = [];\n\n      for (const subject of subjects) {\n        const response = await getSyllabusTopics(level, className, subject);\n        if (response.success) {\n          const subjectTopics = response.data.topics.map(topic => ({\n            ...topic,\n            subject: subject,\n            fullName: `${subject}: ${topic.topicName}`,\n          }));\n          allTopics.push(...subjectTopics);\n        }\n      }\n\n      setAvailableTopics(allTopics);\n    } catch (error) {\n      console.error(\"Error fetching topics:\", error);\n      message.error(\"Failed to fetch syllabus topics\");\n    }\n  };\n\n  const handleAutoGenerateExamSuccess = (newExam) => {\n    // Add the new exam to the list and select it\n    if (newExam && newExam._id) {\n      const updatedExams = [...exams, newExam];\n      setExams(updatedExams);\n      form.setFieldsValue({ examId: newExam._id });\n      setShowAutoGenerateModal(false);\n      message.success(`Exam created successfully: ${newExam.name}`);\n    } else {\n      message.error(\"Invalid exam data received\");\n      setShowAutoGenerateModal(false);\n    }\n  };\n\n  const openAutoGenerateModal = () => {\n    setShowAutoGenerateModal(true);\n  };\n\n  const onFinish = async (values) => {\n    console.log(\"🚀 Form submission started\");\n    console.log(\"📝 Form values:\", values);\n\n    try {\n      setIsGenerating(true);\n      setGenerationProgress(10);\n\n      // Validate question distribution\n      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);\n      console.log(\"📊 Total distribution:\", totalDistribution, \"Total questions:\", values.totalQuestions);\n\n      if (totalDistribution !== values.totalQuestions) {\n        console.error(\"❌ Distribution validation failed\");\n        message.error(\"Question distribution must equal total questions\");\n        setIsGenerating(false);\n        return;\n      }\n\n      console.log(\"✅ Distribution validation passed\");\n\n      setGenerationProgress(30);\n\n      // Check authentication and AI access\n      if (!isAuthenticated || !hasAIAccess) {\n        setIsGenerating(false);\n        setShowLoginModal(true);\n        message.warning(\"Please login to access AI question generation features.\");\n        return;\n      }\n\n      // Double-check with server-side validation\n      const authCheck = await requireAIAuth();\n      if (!authCheck.success) {\n        setIsGenerating(false);\n\n        switch (authCheck.reason) {\n          case 'not_authenticated':\n          case 'refresh_failed':\n            setShowLoginModal(true);\n            message.warning(\"Please login to generate AI questions.\");\n            return;\n          case 'no_ai_access':\n            message.error(\"AI features are not available for your account.\");\n            return;\n          case 'requires_upgrade':\n            message.warning(\"AI question generation requires a premium subscription. Please upgrade your account.\");\n            return;\n          default:\n            setShowLoginModal(true);\n            message.warning(\"Authentication check failed. Please login again.\");\n            return;\n        }\n      }\n\n      const payload = {\n        examId: values.examId,\n        questionTypes: values.questionTypes,\n        subjects: values.subjects,\n        level: values.level,\n        class: values.class,\n        difficultyLevels: values.difficultyLevels,\n        syllabusTopics: values.syllabusTopics || [],\n        totalQuestions: values.totalQuestions,\n        questionDistribution: values.questionDistribution,\n        userId: user._id,\n      };\n\n      console.log(\"📤 Sending payload:\", payload);\n\n      setGenerationProgress(50);\n\n      console.log(\"🌐 Making API call to generate questions...\");\n\n      // Show progress message to user\n      message.info(\"AI is generating your questions... This may take a few minutes.\", 5);\n\n      const response = await generateQuestions(payload);\n      console.log(\"📥 API response received:\", response);\n\n      setGenerationProgress(90);\n\n      if (response.success) {\n        setGenerationProgress(100);\n        message.success(\"Questions generated successfully!\");\n        setTimeout(() => {\n          onSuccess();\n        }, 1000);\n      } else {\n        message.error(response.message || \"Failed to generate questions\");\n      }\n    } catch (error) {\n      console.error(\"Question generation error:\", error);\n\n      // More detailed error handling\n      let errorMessage = \"Error generating questions\";\n\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        // Handle timeout errors specifically\n        errorMessage = \"Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.\";\n      } else if (error.response) {\n        // Server responded with error status\n        console.error(\"Server error response:\", error.response.data);\n        console.error(\"Server error status:\", error.response.status);\n\n        if (error.response.status === 401) {\n          // Handle authentication errors for AI requests\n          const errorData = error.response.data;\n\n          if (errorData?.requiresLogin) {\n            // Show the AI login modal instead of redirecting\n            setShowLoginModal(true);\n            errorMessage = errorData.message || \"Authentication required for AI features.\";\n          } else {\n            errorMessage = errorData?.message || \"Authentication failed. Please login again.\";\n          }\n        } else if (error.response.status === 403) {\n          // Handle permission/subscription errors\n          const errorData = error.response.data;\n          if (errorData?.upgradeRequired) {\n            errorMessage = \"AI question generation requires a premium subscription. Please upgrade your account.\";\n          } else {\n            errorMessage = errorData?.message || \"Access denied for AI features.\";\n          }\n        } else if (error.response.status === 504 || error.response.status === 502) {\n          errorMessage = \"Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.\";\n        } else {\n          errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error(\"Network error:\", error.request);\n        errorMessage = \"Network error - please check your connection. If the problem persists, try generating fewer questions at once.\";\n      } else {\n        // Something else happened\n        console.error(\"Error:\", error.message);\n        errorMessage = error.message || \"Unknown error occurred\";\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress(0);\n    }\n  };\n\n  const questionTypeOptions = [\n    { label: \"Multiple Choice\", value: \"multiple_choice\" },\n    { label: \"Fill in the Blank\", value: \"fill_blank\" },\n    { label: \"Picture-based\", value: \"picture_based\" },\n  ];\n\n  const difficultyOptions = [\n    { label: \"Easy\", value: \"easy\" },\n    { label: \"Medium\", value: \"medium\" },\n    { label: \"Hard\", value: \"hard\" },\n  ];\n\n  const levelOptions = [\n    { label: \"Primary Education (Standards I-VI)\", value: \"primary\" },\n    { label: \"Ordinary Secondary (Forms I-IV)\", value: \"ordinary_secondary\" },\n    { label: \"Advanced Secondary (Forms V-VI)\", value: \"advanced_secondary\" },\n  ];\n\n  const classOptions = {\n    primary: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\"], // Standards I-VI (TIE Primary)\n    ordinary_secondary: [\"I\", \"II\", \"III\", \"IV\"], // Forms I-IV (TIE Ordinary Secondary)\n    advanced_secondary: [\"V\", \"VI\"], // Forms V-VI (TIE Advanced Secondary)\n  };\n\n  return (\n    <div className=\"question-generation-form\">\n      <Card\n        title={\n          <div className=\"form-header\">\n            <Button\n              type=\"text\"\n              icon={<FaArrowLeft />}\n              onClick={onBack}\n              className=\"back-button\"\n            >\n              Back to Dashboard\n            </Button>\n            <div className=\"title-section\">\n              <FaRobot className=\"title-icon\" />\n              <span>Generate AI Questions</span>\n            </div>\n          </div>\n        }\n      >\n        {/* Authentication Status */}\n        {authLoading ? (\n          <Alert\n            message=\"Checking Authentication...\"\n            description=\"Verifying your access to AI features.\"\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !isAuthenticated ? (\n          <Alert\n            message=\"Login Required\"\n            description={\n              <div>\n                <p>Please login to access AI question generation features.</p>\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => setShowLoginModal(true)}\n                  style={{ marginTop: 8 }}\n                >\n                  Login Now\n                </Button>\n              </div>\n            }\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !hasAIAccess ? (\n          <Alert\n            message={requiresUpgrade ? \"Upgrade Required\" : \"AI Access Restricted\"}\n            description={\n              requiresUpgrade\n                ? \"AI question generation requires a premium subscription. Please upgrade your account.\"\n                : \"AI features are not available for your account. Please contact support.\"\n            }\n            type=\"error\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : sessionExpiringSoon ? (\n          <Alert\n            message=\"Session Expiring Soon\"\n            description={`Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`}\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n            action={\n              <Button\n                size=\"small\"\n                onClick={() => setShowLoginModal(true)}\n              >\n                Refresh Login\n              </Button>\n            }\n          />\n        ) : (\n          <Alert\n            message=\"AI Features Ready\"\n            description={`Welcome ${user?.name}! You have full access to AI question generation.`}\n            type=\"success\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        {isGenerating && (\n          <Alert\n            message=\"Generating Questions\"\n            description={\n              <div>\n                <p>AI is generating your questions. This may take a few moments...</p>\n                <Progress percent={generationProgress} status=\"active\" />\n              </div>\n            }\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={onFinish}\n          disabled={isGenerating || !hasAIAccess || authLoading}\n        >\n          <Row gutter={[16, 16]}>\n            <Col xs={24}>\n              <Alert\n                message=\"Exam Selection\"\n                description=\"You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam.\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n            </Col>\n\n            <Col xs={24} md={16}>\n              <Form.Item\n                name=\"examId\"\n                label=\"Target Exam (Optional)\"\n                extra=\"Leave empty to generate standalone questions, or select an existing exam\"\n              >\n                <Select\n                  placeholder=\"Optional: Choose an existing exam\"\n                  allowClear\n                >\n                  {exams && exams.length > 0 && exams.map((exam) => (\n                    exam && exam._id ? (\n                      <Option key={exam._id} value={exam._id}>\n                        {exam.name} - {exam.category}\n                      </Option>\n                    ) : null\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item label=\"Or Create New Exam\">\n                <Button\n                  type=\"dashed\"\n                  icon={<FaRobot />}\n                  onClick={openAutoGenerateModal}\n                  style={{ width: \"100%\" }}\n                  disabled={isGenerating || !hasAIAccess || authLoading}\n                >\n                  Auto-Generate New Exam\n                </Button>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"level\"\n                label=\"Education Level\"\n                rules={[{ required: true, message: \"Please select a level\" }]}\n              >\n                <Select \n                  placeholder=\"Choose education level\"\n                  onChange={handleLevelChange}\n                >\n                  {levelOptions.map((option) => (\n                    <Option key={option.value} value={option.value}>\n                      {option.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"class\"\n                label=\"Class\"\n                rules={[{ required: true, message: \"Please select a class\" }]}\n              >\n                <Select\n                  placeholder=\"Choose class\"\n                  disabled={!selectedLevel}\n                  onChange={handleClassChange}\n                >\n                  {selectedLevel && classOptions[selectedLevel]?.map((cls) => (\n                    <Option key={cls} value={cls}>\n                      Class {cls}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"subjects\"\n                label=\"Subjects\"\n                rules={[{ required: true, message: \"Please select at least one subject\" }]}\n              >\n                <Select\n                  mode=\"multiple\"\n                  placeholder=\"Choose subjects\"\n                  disabled={!selectedLevel}\n                  onChange={handleSubjectsChange}\n                >\n                  {availableSubjects.map((subject) => (\n                    <Option key={subject} value={subject}>\n                      {subject}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"questionTypes\"\n                label=\"Question Types\"\n                rules={[{ required: true, message: \"Please select at least one question type\" }]}\n              >\n                <Checkbox.Group options={questionTypeOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"difficultyLevels\"\n                label=\"Difficulty Levels\"\n                rules={[{ required: true, message: \"Please select at least one difficulty level\" }]}\n              >\n                <Checkbox.Group options={difficultyOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"totalQuestions\"\n                label=\"Total Questions\"\n                rules={[\n                  { required: true, message: \"Please enter total questions\" },\n                  { type: \"number\", min: 1, max: 50, message: \"Must be between 1 and 50\" }\n                ]}\n              >\n                <InputNumber\n                  min={1}\n                  max={50}\n                  placeholder=\"Enter total questions\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Divider>Question Distribution</Divider>\n\n          {selectedLevel && selectedClass && selectedSubjects.length > 0 && (\n            <Alert\n              message=\"Tanzania Syllabus Information\"\n              description={\n                <div>\n                  <p><strong>Level:</strong> {selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</p>\n                  <p><strong>Class:</strong> {selectedClass}</p>\n                  <p><strong>Subjects:</strong> {selectedSubjects.join(\", \")}</p>\n                  <p><strong>Available Topics:</strong> {availableTopics.length} topics from Tanzania National Curriculum</p>\n                  <p><strong>Auto-generate:</strong> Use the button above to create a new exam with proper structure</p>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          )}\n\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"multiple_choice\"]}\n                label=\"Multiple Choice\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"fill_blank\"]}\n                label=\"Fill in the Blank\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"picture_based\"]}\n                label=\"Picture-based\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"syllabusTopics\"\n            label={`Tanzania Syllabus Topics (${availableTopics.length} available)`}\n            extra={availableTopics.length === 0 ? \"Select level, class, and subjects to see available topics\" : \"Select specific topics from Tanzania National Curriculum\"}\n          >\n            <Select\n              mode=\"multiple\"\n              placeholder={availableTopics.length === 0 ? \"No topics available - select level, class, and subjects first\" : \"Choose specific topics from Tanzania syllabus\"}\n              style={{ width: \"100%\" }}\n              disabled={availableTopics.length === 0}\n              optionFilterProp=\"children\"\n              showSearch\n              filterOption={(input, option) =>\n                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0\n              }\n            >\n              {availableTopics.map((topic, index) => (\n                <Option key={`${topic.subject}-${topic.topicName}-${index}`} value={topic.topicName}>\n                  <div>\n                    <strong>{topic.topicName}</strong>\n                    <div style={{ fontSize: \"12px\", color: \"#666\" }}>\n                      {topic.subject} • Difficulty: {topic.difficulty}\n                    </div>\n                    {topic.subtopics && topic.subtopics.length > 0 && (\n                      <div style={{ fontSize: \"11px\", color: \"#999\" }}>\n                        Subtopics: {topic.subtopics.slice(0, 3).join(\", \")}\n                        {topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`}\n                      </div>\n                    )}\n                  </div>\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <div className=\"form-actions\">\n            <Button onClick={onBack} disabled={isGenerating}>\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={isGenerating}\n              disabled={!hasAIAccess || authLoading}\n              icon={<FaRobot />}\n            >\n              {isGenerating ? \"Generating...\" : !hasAIAccess ? \"Login Required\" : \"Generate Questions\"}\n            </Button>\n          </div>\n        </Form>\n      </Card>\n\n      <AutoGenerateExamModal\n        visible={showAutoGenerateModal}\n        onCancel={() => setShowAutoGenerateModal(false)}\n        onSuccess={handleAutoGenerateExamSuccess}\n        prefilledData={{\n          level: selectedLevel,\n          class: selectedClass,\n          subjects: selectedSubjects,\n        }}\n      />\n\n      <AILoginModal\n        visible={showLoginModal}\n        onCancel={() => setShowLoginModal(false)}\n        onSuccess={(userData) => {\n          handleLoginSuccess(userData);\n          setShowLoginModal(false);\n        }}\n        title=\"AI Features Login Required\"\n        description=\"Please login to access AI question generation features. Your session may have expired or you need enhanced permissions.\"\n      />\n    </div>\n  );\n}\n\nexport default QuestionGenerationForm;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionPreview.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\AutoGenerateExamModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js", ["425", "426"], [], "import React, { useState, useEffect } from 'react';\nimport { Modal, Form, Input, Button, Checkbox, Alert, Typography, Space, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, RobotOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { quickLogin, autoRefreshToken } from '../apicalls/auth';\nimport { getTokenExpiryInfo } from '../utils/authUtils';\n\nconst { Title, Text } = Typography;\n\nconst AILoginModal = ({ \n  visible, \n  onCancel, \n  onSuccess, \n  title = \"Login Required for AI Features\",\n  description = \"Please login to access AI question generation features.\"\n}) => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [autoRefreshing, setAutoRefreshing] = useState(false);\n\n  useEffect(() => {\n    if (visible) {\n      // Check current token status when modal opens\n      const info = getTokenExpiryInfo();\n      setTokenInfo(info);\n      \n      // Try auto-refresh if token is expiring soon\n      if (info.needsRefresh && !info.expired) {\n        handleAutoRefresh();\n      }\n    }\n  }, [visible]);\n\n  const handleAutoRefresh = async () => {\n    try {\n      setAutoRefreshing(true);\n      const success = await autoRefreshToken();\n      if (success) {\n        const newInfo = getTokenExpiryInfo();\n        setTokenInfo(newInfo);\n        \n        if (!newInfo.expired) {\n          onSuccess?.();\n          return;\n        }\n      }\n    } catch (error) {\n      console.error('Auto-refresh failed:', error);\n    } finally {\n      setAutoRefreshing(false);\n    }\n  };\n\n  const handleLogin = async (values) => {\n    try {\n      setLoading(true);\n      \n      const response = await quickLogin({\n        email: values.email,\n        password: values.password,\n        rememberMe: values.rememberMe || false\n      });\n\n      if (response.success) {\n        // Check AI access\n        const { aiAccess } = response.data;\n        \n        if (!aiAccess.enabled) {\n          Modal.warning({\n            title: 'AI Features Not Available',\n            content: aiAccess.requiresUpgrade \n              ? 'AI question generation requires a premium subscription. Please upgrade your account.'\n              : 'AI features are not available for your account. Please contact support.',\n          });\n          return;\n        }\n\n        form.resetFields();\n        onSuccess?.(response.data);\n      }\n    } catch (error) {\n      console.error('Login failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderTokenStatus = () => {\n    if (!tokenInfo) return null;\n\n    if (tokenInfo.expired) {\n      return (\n        <Alert\n          type=\"warning\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expired\"\n          description=\"Your session has expired. Please login again to continue using AI features.\"\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    if (tokenInfo.needsRefresh) {\n      return (\n        <Alert\n          type=\"info\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expiring Soon\"\n          description={`Your session will expire in ${tokenInfo.formattedTimeLeft}. Login to extend your session.`}\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Modal\n      title={\n        <Space>\n          <RobotOutlined style={{ color: '#1890ff' }} />\n          <span>{title}</span>\n        </Space>\n      }\n      open={visible}\n      onCancel={onCancel}\n      footer={null}\n      width={450}\n      destroyOnClose\n      maskClosable={false}\n    >\n      <div style={{ padding: '20px 0' }}>\n        <Text type=\"secondary\" style={{ display: 'block', marginBottom: 24, textAlign: 'center' }}>\n          {description}\n        </Text>\n\n        {renderTokenStatus()}\n\n        {autoRefreshing && (\n          <Alert\n            type=\"info\"\n            message=\"Refreshing Session...\"\n            description=\"Attempting to refresh your authentication automatically.\"\n            style={{ marginBottom: 16 }}\n            showIcon\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleLogin}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"email\"\n            label=\"Email\"\n            rules={[\n              { required: true, message: 'Please enter your email' },\n              { type: 'email', message: 'Please enter a valid email' }\n            ]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"Enter your email\"\n              autoComplete=\"email\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            label=\"Password\"\n            rules={[{ required: true, message: 'Please enter your password' }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"Enter your password\"\n              autoComplete=\"current-password\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"rememberMe\" valuePropName=\"checked\">\n            <Checkbox>\n              Keep me logged in for 30 days\n            </Checkbox>\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading || autoRefreshing}\n              block\n              size=\"large\"\n              icon={<RobotOutlined />}\n            >\n              {loading ? 'Logging in...' : autoRefreshing ? 'Refreshing...' : 'Login for AI Features'}\n            </Button>\n          </Form.Item>\n        </Form>\n\n        <Divider />\n\n        <div style={{ textAlign: 'center' }}>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            Secure authentication for AI-powered question generation\n          </Text>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default AILoginModal;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js", ["427"], [], "import { useState, useEffect, useCallback } from 'react';\nimport { message } from 'antd';\nimport { validateSession, autoRefreshToken, checkAIAccess } from '../apicalls/auth';\nimport { getTokenExpiryInfo, isSessionValid } from '../utils/authUtils';\n\n/**\n * Enhanced authentication hook specifically for AI features\n * Provides automatic token refresh, session validation, and AI access checking\n */\nexport const useAIAuth = () => {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [hasAIAccess, setHasAIAccess] = useState(false);\n  const [user, setUser] = useState(null);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [requiresUpgrade, setRequiresUpgrade] = useState(false);\n\n  // Check authentication status\n  const checkAuth = useCallback(async () => {\n    try {\n      setLoading(true);\n      \n      // Quick check if session is valid\n      if (!isSessionValid()) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        setTokenInfo(null);\n        return false;\n      }\n\n      // Get token expiry info\n      const expiry = getTokenExpiryInfo();\n      setTokenInfo(expiry);\n\n      // If token is expired, clear everything\n      if (expiry.expired) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        return false;\n      }\n\n      // Try to auto-refresh if needed\n      if (expiry.needsRefresh) {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n        } catch (error) {\n          console.warn('Auto-refresh failed:', error);\n        }\n      }\n\n      // Validate session and check AI access\n      const accessCheck = await checkAIAccess();\n      \n      if (accessCheck.hasAccess) {\n        setIsAuthenticated(true);\n        setHasAIAccess(true);\n        setUser(accessCheck.user);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return true;\n      } else {\n        setIsAuthenticated(!!accessCheck.user);\n        setHasAIAccess(false);\n        setUser(accessCheck.user || null);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return false;\n      }\n\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setIsAuthenticated(false);\n      setHasAIAccess(false);\n      setUser(null);\n      setTokenInfo(null);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  // Refresh authentication\n  const refreshAuth = useCallback(async () => {\n    return await checkAuth();\n  }, [checkAuth]);\n\n  // Logout\n  const logout = useCallback(() => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setIsAuthenticated(false);\n    setHasAIAccess(false);\n    setUser(null);\n    setTokenInfo(null);\n    message.info('Logged out successfully');\n  }, []);\n\n  // Login success handler\n  const handleLoginSuccess = useCallback((userData) => {\n    setIsAuthenticated(true);\n    setUser(userData.user);\n    \n    // Check AI access from login response\n    const aiEnabled = userData.aiAccess?.enabled !== false;\n    setHasAIAccess(aiEnabled);\n    setRequiresUpgrade(userData.aiAccess?.requiresUpgrade || false);\n    \n    // Update token info\n    const expiry = getTokenExpiryInfo();\n    setTokenInfo(expiry);\n    \n    message.success('Successfully logged in for AI features!');\n  }, []);\n\n  // Require authentication for AI operations\n  const requireAIAuth = useCallback(async () => {\n    if (loading) {\n      return { success: false, reason: 'loading' };\n    }\n\n    if (!isAuthenticated) {\n      return { success: false, reason: 'not_authenticated' };\n    }\n\n    if (!hasAIAccess) {\n      if (requiresUpgrade) {\n        return { success: false, reason: 'requires_upgrade' };\n      }\n      return { success: false, reason: 'no_ai_access' };\n    }\n\n    // Check if token is about to expire\n    if (tokenInfo?.needsRefresh) {\n      try {\n        await autoRefreshToken();\n        const newExpiry = getTokenExpiryInfo();\n        setTokenInfo(newExpiry);\n      } catch (error) {\n        return { success: false, reason: 'refresh_failed' };\n      }\n    }\n\n    return { success: true };\n  }, [isAuthenticated, hasAIAccess, requiresUpgrade, tokenInfo, loading]);\n\n  // Auto-refresh timer\n  useEffect(() => {\n    let refreshTimer;\n\n    if (isAuthenticated && tokenInfo && !tokenInfo.expired) {\n      // Set timer to refresh token 5 minutes before expiry\n      const refreshTime = Math.max(0, (tokenInfo.timeLeft - 300) * 1000);\n      \n      refreshTimer = setTimeout(async () => {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n          console.log('🔄 Token auto-refreshed');\n        } catch (error) {\n          console.warn('Auto-refresh timer failed:', error);\n        }\n      }, refreshTime);\n    }\n\n    return () => {\n      if (refreshTimer) {\n        clearTimeout(refreshTimer);\n      }\n    };\n  }, [isAuthenticated, tokenInfo]);\n\n  // Initial auth check\n  useEffect(() => {\n    checkAuth();\n  }, [checkAuth]);\n\n  return {\n    // State\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    tokenInfo,\n    loading,\n    requiresUpgrade,\n    \n    // Actions\n    checkAuth,\n    refreshAuth,\n    logout,\n    handleLoginSuccess,\n    requireAIAuth,\n    \n    // Computed values\n    needsLogin: !isAuthenticated,\n    needsUpgrade: isAuthenticated && !hasAIAccess && requiresUpgrade,\n    sessionExpiringSoon: tokenInfo?.needsRefresh || false,\n    timeUntilExpiry: tokenInfo?.formattedTimeLeft || 'Unknown'\n  };\n};\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizStart.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizRenderer.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", [], [], {"ruleId": "428", "severity": 1, "message": "429", "line": 9, "column": 10, "nodeType": "430", "messageId": "431", "endLine": 9, "endColumn": 21}, {"ruleId": "428", "severity": 1, "message": "432", "line": 9, "column": 23, "nodeType": "430", "messageId": "431", "endLine": 9, "endColumn": 34}, {"ruleId": "433", "severity": 1, "message": "434", "line": 208, "column": 6, "nodeType": "435", "endLine": 208, "endColumn": 8, "suggestions": "436"}, {"ruleId": "433", "severity": 1, "message": "437", "line": 273, "column": 6, "nodeType": "435", "endLine": 273, "endColumn": 33, "suggestions": "438"}, {"ruleId": "433", "severity": 1, "message": "439", "line": 280, "column": 6, "nodeType": "435", "endLine": 280, "endColumn": 25, "suggestions": "440"}, {"ruleId": "433", "severity": 1, "message": "441", "line": 37, "column": 6, "nodeType": "435", "endLine": 37, "endColumn": 8, "suggestions": "442"}, {"ruleId": "428", "severity": 1, "message": "443", "line": 1, "column": 35, "nodeType": "430", "messageId": "431", "endLine": 1, "endColumn": 41}, {"ruleId": "433", "severity": 1, "message": "444", "line": 81, "column": 6, "nodeType": "435", "endLine": 81, "endColumn": 8, "suggestions": "445"}, {"ruleId": "433", "severity": 1, "message": "446", "line": 93, "column": 6, "nodeType": "435", "endLine": 93, "endColumn": 8, "suggestions": "447"}, {"ruleId": "433", "severity": 1, "message": "448", "line": 65, "column": 8, "nodeType": "435", "endLine": 65, "endColumn": 32, "suggestions": "449"}, {"ruleId": "433", "severity": 1, "message": "450", "line": 47, "column": 9, "nodeType": "451", "endLine": 51, "endColumn": 29}, {"ruleId": "428", "severity": 1, "message": "452", "line": 4, "column": 19, "nodeType": "430", "messageId": "431", "endLine": 4, "endColumn": 24}, {"ruleId": "433", "severity": 1, "message": "453", "line": 67, "column": 6, "nodeType": "435", "endLine": 67, "endColumn": 8, "suggestions": "454"}, {"ruleId": "433", "severity": 1, "message": "455", "line": 196, "column": 6, "nodeType": "435", "endLine": 196, "endColumn": 8, "suggestions": "456"}, {"ruleId": "428", "severity": 1, "message": "457", "line": 15, "column": 12, "nodeType": "430", "messageId": "431", "endLine": 15, "endColumn": 23}, {"ruleId": "428", "severity": 1, "message": "458", "line": 18, "column": 12, "nodeType": "430", "messageId": "431", "endLine": 18, "endColumn": 20}, {"ruleId": "433", "severity": 1, "message": "459", "line": 69, "column": 8, "nodeType": "435", "endLine": 69, "endColumn": 10, "suggestions": "460"}, {"ruleId": "433", "severity": 1, "message": "461", "line": 85, "column": 8, "nodeType": "435", "endLine": 85, "endColumn": 21, "suggestions": "462"}, {"ruleId": "428", "severity": 1, "message": "463", "line": 88, "column": 11, "nodeType": "430", "messageId": "431", "endLine": 88, "endColumn": 29}, {"ruleId": "428", "severity": 1, "message": "464", "line": 1, "column": 38, "nodeType": "430", "messageId": "431", "endLine": 1, "endColumn": 46}, {"ruleId": "428", "severity": 1, "message": "465", "line": 8, "column": 12, "nodeType": "430", "messageId": "431", "endLine": 8, "endColumn": 19}, {"ruleId": "433", "severity": 1, "message": "459", "line": 58, "column": 8, "nodeType": "435", "endLine": 58, "endColumn": 10, "suggestions": "466"}, {"ruleId": "433", "severity": 1, "message": "467", "line": 90, "column": 6, "nodeType": "435", "endLine": 90, "endColumn": 35, "suggestions": "468"}, {"ruleId": "428", "severity": 1, "message": "469", "line": 92, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 92, "endColumn": 26}, {"ruleId": "428", "severity": 1, "message": "470", "line": 15, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 15, "endColumn": 17}, {"ruleId": "433", "severity": 1, "message": "471", "line": 116, "column": 6, "nodeType": "435", "endLine": 116, "endColumn": 8, "suggestions": "472"}, {"ruleId": "428", "severity": 1, "message": "443", "line": 1, "column": 25, "nodeType": "430", "messageId": "431", "endLine": 1, "endColumn": 31}, {"ruleId": "428", "severity": 1, "message": "473", "line": 62, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 62, "endColumn": 31}, {"ruleId": "428", "severity": 1, "message": "470", "line": 11, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 11, "endColumn": 17}, {"ruleId": "428", "severity": 1, "message": "474", "line": 1, "column": 38, "nodeType": "430", "messageId": "431", "endLine": 1, "endColumn": 44}, {"ruleId": "428", "severity": 1, "message": "475", "line": 35, "column": 10, "nodeType": "430", "messageId": "431", "endLine": 35, "endColumn": 20}, {"ruleId": "433", "severity": 1, "message": "476", "line": 61, "column": 6, "nodeType": "435", "endLine": 61, "endColumn": 26, "suggestions": "477"}, {"ruleId": "433", "severity": 1, "message": "478", "line": 93, "column": 6, "nodeType": "435", "endLine": 93, "endColumn": 8, "suggestions": "479"}, {"ruleId": "433", "severity": 1, "message": "480", "line": 213, "column": 6, "nodeType": "435", "endLine": 213, "endColumn": 20, "suggestions": "481"}, {"ruleId": "433", "severity": 1, "message": "461", "line": 63, "column": 6, "nodeType": "435", "endLine": 63, "endColumn": 19, "suggestions": "482"}, {"ruleId": "433", "severity": 1, "message": "478", "line": 97, "column": 6, "nodeType": "435", "endLine": 97, "endColumn": 8, "suggestions": "483"}, {"ruleId": "433", "severity": 1, "message": "484", "line": 48, "column": 6, "nodeType": "435", "endLine": 48, "endColumn": 8, "suggestions": "485"}, {"ruleId": "428", "severity": 1, "message": "486", "line": 2, "column": 8, "nodeType": "430", "messageId": "431", "endLine": 2, "endColumn": 13}, {"ruleId": "428", "severity": 1, "message": "487", "line": 74, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 74, "endColumn": 23}, {"ruleId": "428", "severity": 1, "message": "488", "line": 19, "column": 11, "nodeType": "430", "messageId": "431", "endLine": 19, "endColumn": 24}, {"ruleId": "489", "severity": 1, "message": "490", "line": 51, "column": 48, "nodeType": "491", "messageId": "492", "endLine": 51, "endColumn": 50}, {"ruleId": "489", "severity": 1, "message": "490", "line": 53, "column": 52, "nodeType": "491", "messageId": "492", "endLine": 53, "endColumn": 54}, {"ruleId": "493", "severity": 1, "message": "494", "line": 78, "column": 103, "nodeType": "495", "messageId": "496", "endLine": 78, "endColumn": 104, "suggestions": "497"}, {"ruleId": "493", "severity": 1, "message": "494", "line": 90, "column": 93, "nodeType": "495", "messageId": "496", "endLine": 90, "endColumn": 94, "suggestions": "498"}, {"ruleId": "433", "severity": 1, "message": "499", "line": 126, "column": 6, "nodeType": "435", "endLine": 126, "endColumn": 32, "suggestions": "500", "suppressions": "501"}, {"ruleId": "428", "severity": 1, "message": "502", "line": 1, "column": 17, "nodeType": "430", "messageId": "431", "endLine": 1, "endColumn": 26}, {"ruleId": "428", "severity": 1, "message": "503", "line": 1, "column": 28, "nodeType": "430", "messageId": "431", "endLine": 1, "endColumn": 36}, {"ruleId": "428", "severity": 1, "message": "504", "line": 20, "column": 3, "nodeType": "430", "messageId": "431", "endLine": 20, "endColumn": 8}, {"ruleId": "428", "severity": 1, "message": "505", "line": 21, "column": 3, "nodeType": "430", "messageId": "431", "endLine": 21, "endColumn": 11}, {"ruleId": "428", "severity": 1, "message": "506", "line": 22, "column": 3, "nodeType": "430", "messageId": "431", "endLine": 22, "endColumn": 11}, {"ruleId": "433", "severity": 1, "message": "507", "line": 95, "column": 6, "nodeType": "435", "endLine": 95, "endColumn": 15, "suggestions": "508"}, {"ruleId": "428", "severity": 1, "message": "509", "line": 9, "column": 3, "nodeType": "430", "messageId": "431", "endLine": 9, "endColumn": 8}, {"ruleId": "428", "severity": 1, "message": "470", "line": 23, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 23, "endColumn": 17}, {"ruleId": "433", "severity": 1, "message": "510", "line": 37, "column": 6, "nodeType": "435", "endLine": 37, "endColumn": 8, "suggestions": "511"}, {"ruleId": "428", "severity": 1, "message": "512", "line": 42, "column": 5, "nodeType": "430", "messageId": "431", "endLine": 42, "endColumn": 15}, {"ruleId": "428", "severity": 1, "message": "513", "line": 7, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 7, "endColumn": 14}, {"ruleId": "433", "severity": 1, "message": "514", "line": 32, "column": 6, "nodeType": "435", "endLine": 32, "endColumn": 15, "suggestions": "515"}, {"ruleId": "428", "severity": 1, "message": "516", "line": 3, "column": 10, "nodeType": "430", "messageId": "431", "endLine": 3, "endColumn": 25}, "no-unused-vars", "'HideLoading' is defined but never used.", "Identifier", "unusedVar", "'ShowLoading' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'getUserData' and 'navigate'. Either include them or remove the dependency array.", "ArrayExpression", ["517"], "React Hook useEffect has missing dependencies: 'dispatch', 'user?.isAdmin', 'user?.paymentRequired', and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["518"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["519"], "React Hook useEffect has a missing dependency: 'fetchAll'. Either include it or remove the dependency array.", ["520"], "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["521"], "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["522"], "React Hook useEffect has a missing dependency: 'paymentInProgress'. Either include it or remove the dependency array.", ["523"], "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 103) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "'Modal' is defined but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["524"], "React Hook useEffect has missing dependencies: 'getData' and 'getExams'. Either include them or remove the dependency array.", ["525"], "'userRanking' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["526"], "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["527"], "'formatMobileUserId' is assigned a value but never used.", "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", ["528"], "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["529"], "'handleTableChange' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["530"], "'handleSchoolTypeChange' is assigned a value but never used.", "'useRef' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["531"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["532"], "React Hook useEffect has a missing dependency: 'form2'. Either include it or remove the dependency array.", ["533"], ["534"], ["535"], "React Hook useEffect has a missing dependency: 'getReviews'. Either include it or remove the dependency array.", ["536"], "'axios' is defined but never used.", "'handleKeyPress' is assigned a value but never used.", "'restoredLines' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a return value from arrow function.", "ArrowFunctionExpression", "expectedInside", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["537", "538"], ["539", "540"], "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["541"], ["542"], "'useEffect' is defined but never used.", "'useState' is defined but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "'FaSearch' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["543"], "'FaCog' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGenerationHistory'. Either include it or remove the dependency array.", ["544"], "'needsLogin' is assigned a value but never used.", "'Title' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAutoRefresh'. Either include it or remove the dependency array.", ["545"], "'validateSession' is defined but never used.", {"desc": "546", "fix": "547"}, {"desc": "548", "fix": "549"}, {"desc": "550", "fix": "551"}, {"desc": "552", "fix": "553"}, {"desc": "554", "fix": "555"}, {"desc": "556", "fix": "557"}, {"desc": "558", "fix": "559"}, {"desc": "560", "fix": "561"}, {"desc": "562", "fix": "563"}, {"desc": "564", "fix": "565"}, {"desc": "566", "fix": "567"}, {"desc": "564", "fix": "568"}, {"desc": "569", "fix": "570"}, {"desc": "571", "fix": "572"}, {"desc": "573", "fix": "574"}, {"desc": "575", "fix": "576"}, {"desc": "577", "fix": "578"}, {"desc": "566", "fix": "579"}, {"desc": "575", "fix": "580"}, {"desc": "581", "fix": "582"}, {"messageId": "583", "fix": "584", "desc": "585"}, {"messageId": "586", "fix": "587", "desc": "588"}, {"messageId": "583", "fix": "589", "desc": "585"}, {"messageId": "586", "fix": "590", "desc": "588"}, {"desc": "591", "fix": "592"}, {"kind": "593", "justification": "594"}, {"desc": "595", "fix": "596"}, {"desc": "597", "fix": "598"}, {"desc": "599", "fix": "600"}, "Update the dependencies array to be: [getUserData, navigate]", {"range": "601", "text": "602"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", {"range": "603", "text": "604"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "605", "text": "606"}, "Update the dependencies array to be: [fetchAll]", {"range": "607", "text": "608"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "609", "text": "610"}, "Update the dependencies array to be: [getExamsData]", {"range": "611", "text": "612"}, "Update the dependencies array to be: [user, subscriptionData, paymentInProgress]", {"range": "613", "text": "614"}, "Update the dependencies array to be: [getData]", {"range": "615", "text": "616"}, "Update the dependencies array to be: [getData, getExams]", {"range": "617", "text": "618"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "619", "text": "620"}, "Update the dependencies array to be: [getUserStats, rankingData]", {"range": "621", "text": "622"}, {"range": "623", "text": "620"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "624", "text": "625"}, "Update the dependencies array to be: [getUsersData]", {"range": "626", "text": "627"}, "Update the dependencies array to be: [currentPage, fetchQuestions, limit]", {"range": "628", "text": "629"}, "Update the dependencies array to be: [getUserData]", {"range": "630", "text": "631"}, "Update the dependencies array to be: [editQuestion, form2]", {"range": "632", "text": "633"}, {"range": "634", "text": "622"}, {"range": "635", "text": "631"}, "Update the dependencies array to be: [getReviews]", {"range": "636", "text": "637"}, "removeEscape", {"range": "638", "text": "594"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "639", "text": "640"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "641", "text": "594"}, {"range": "642", "text": "640"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "643", "text": "644"}, "directive", "", "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "645", "text": "646"}, "Update the dependencies array to be: [fetchGenerationHistory]", {"range": "647", "text": "648"}, "Update the dependencies array to be: [handleAutoRefresh, visible]", {"range": "649", "text": "650"}, [6226, 6228], "[get<PERSON><PERSON><PERSON><PERSON>, navigate]", [8037, 8064], "[dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", [8225, 8244], "[user, activeRoute, verifyPaymentStatus]", [1156, 1158], "[fetchAll]", [2375, 2377], "[getExamData, params.id]", [2372, 2374], "[getExamsData]", [2446, 2470], "[user, subscriptionData, paymentInProgress]", [1901, 1903], "[getData]", [6393, 6395], "[getData, getExams]", [2289, 2291], "[dispatch, getUserData]", [2697, 2710], "[getUserStats, rankingData]", [1990, 1992], [2512, 2541], "[filters, getData, pagination]", [3040, 3042], "[getUsersData]", [2250, 2270], "[currentPage, fetchQuestions, limit]", [3018, 3020], "[getUserData]", [6129, 6143], "[editQuestion, form2]", [1961, 1974], [2889, 2891], [1587, 1589], "[getReviews]", [3429, 3430], [3429, 3429], "\\", [4409, 4410], [4409, 4409], [3978, 4004], "[modalIsOpen, documentUrl, renderPDF]", [2411, 2420], "[fetchMaterials, filters]", [1203, 1205], "[fetchGenerationHistory]", [1110, 1119], "[handleAutoRefresh, visible]"]