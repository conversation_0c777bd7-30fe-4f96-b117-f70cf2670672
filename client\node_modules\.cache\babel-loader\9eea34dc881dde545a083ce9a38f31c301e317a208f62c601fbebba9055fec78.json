{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\"\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Render MCQ Question with modern, clean design\n  const renderMCQ = () => {\n    if (!question.options) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-8 bg-red-50 rounded-xl border border-red-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-600 text-lg font-medium\",\n          children: \"No options available for this question.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this);\n    }\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: Object.entries(question.options).map(([key, value], index) => {\n        const optionKey = String(key).trim();\n        const optionValue = String(value).trim();\n        const label = optionLabels[index] || optionKey;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"group\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleAnswerSelect(optionKey),\n            className: `group relative overflow-hidden w-full p-6 rounded-2xl text-left transition-all duration-300 flex items-center space-x-5 border-2 ${currentAnswer === optionKey ? 'bg-blue-500 text-white border-blue-600 shadow-2xl transform scale-[1.02]' : 'bg-white hover:bg-blue-50 border-slate-200 hover:border-blue-300 hover:shadow-xl'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `relative w-12 h-12 rounded-2xl flex items-center justify-center font-black text-xl transition-all duration-300 shadow-lg ${currentAnswer === optionKey ? 'bg-blue-100 text-blue-600' : 'bg-gradient-to-br from-blue-500 to-indigo-500 text-white group-hover:from-blue-600 group-hover:to-indigo-600'}`,\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `relative flex-1 text-xl leading-relaxed font-semibold ${currentAnswer === optionKey ? 'text-blue-50' : 'text-slate-800 group-hover:text-slate-900'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render Fill-in-the-blank Question with modern design\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl p-8 border border-slate-200 shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-slate-700 font-black mb-6 text-xl tracking-tight\",\n        children: \"Your Answer:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentAnswer,\n        onChange: e => handleAnswerSelect(e.target.value),\n        placeholder: \"Type your answer here...\",\n        className: \"w-full p-6 border-2 border-slate-300 rounded-2xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 bg-white text-xl font-semibold transition-all duration-300 shadow-inner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 rounded-2xl bg-gradient-to-t from-black/5 to-transparent pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), currentAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-200 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-3 h-3 bg-white rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-emerald-700 font-bold text-xl tracking-tight\",\n          children: [\"Answer recorded: \\\"\", currentAnswer, \"\\\"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n\n  // Render Image-based Question with enhanced design\n  const renderImageQuestion = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: question.imageUrl,\n          alt: \"Question diagram\",\n          className: \"max-w-full max-h-96 rounded-lg mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this), question.options ? renderMCQ() : renderFillBlank()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/95 backdrop-blur-md shadow-sm border-b border-slate-200/50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-6 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `inline-block px-6 py-3 rounded-2xl font-mono font-black text-xl shadow-lg border ${timeLeft <= 60 ? 'bg-gradient-to-r from-red-500 to-red-600 text-white border-red-300' : timeLeft <= 300 ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white border-amber-300' : 'bg-gradient-to-r from-emerald-500 to-green-500 text-white border-emerald-300'}`,\n            children: formatTime(timeLeft)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-6 py-8 pb-32\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl border border-slate-200/50 p-8 md:p-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-black text-slate-800 leading-tight mb-6 tracking-tight\",\n            children: question.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative inline-block p-4 bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl shadow-xl border border-slate-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: question.image,\n                alt: \"Question\",\n                className: \"max-w-full max-h-80 rounded-xl mx-auto shadow-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 rounded-2xl bg-gradient-to-t from-black/5 to-transparent pointer-events-none\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [question.answerType === \"Options\" && renderMCQ(), (question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank(), question.imageUrl && renderImageQuestion()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-slate-200/50 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `group relative overflow-hidden px-8 py-4 rounded-xl font-bold transition-all duration-300 ${questionIndex === 0 ? 'opacity-40 cursor-not-allowed bg-slate-100 text-slate-400' : 'bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'}`,\n            children: [questionIndex > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `group relative overflow-hidden px-8 py-4 rounded-xl font-bold transition-all duration-300 ${!isAnswered ? 'opacity-40 cursor-not-allowed bg-slate-100 text-slate-400' : questionIndex === totalQuestions - 1 ? 'bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5' : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'}`,\n            children: [isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative\",\n              children: questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-black text-slate-800 tracking-tight\",\n            children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-slate-200 rounded-full h-2 shadow-inner overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-full bg-blue-500 rounded-full transition-all duration-700 shadow-sm relative overflow-hidden\",\n              style: {\n                width: `${progressPercentage}%`\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-400/30 to-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "username", "onNext", "onPrevious", "examTitle", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "handleAnswerSelect", "answer", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "progressPercentage", "renderMCQ", "options", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "optionLabels", "Object", "entries", "map", "key", "value", "index", "optionKey", "String", "trim", "optionValue", "label", "onClick", "renderFillBlank", "type", "onChange", "e", "target", "placeholder", "renderImageQuestion", "imageUrl", "src", "alt", "name", "image", "answerType", "disabled", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Render MCQ Question with modern, clean design\n  const renderMCQ = () => {\n    if (!question.options) {\n      return (\n        <div className=\"text-center p-8 bg-red-50 rounded-xl border border-red-200\">\n          <div className=\"text-red-600 text-lg font-medium\">No options available for this question.</div>\n        </div>\n      );\n    }\n\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n\n    return (\n      <div className=\"space-y-4\">\n        {Object.entries(question.options).map(([key, value], index) => {\n          const optionKey = String(key).trim();\n          const optionValue = String(value).trim();\n          const label = optionLabels[index] || optionKey;\n\n          return (\n            <div key={optionKey} className=\"group\">\n              <button\n                onClick={() => handleAnswerSelect(optionKey)}\n                className={`group relative overflow-hidden w-full p-6 rounded-2xl text-left transition-all duration-300 flex items-center space-x-5 border-2 ${\n                  currentAnswer === optionKey\n                    ? 'bg-blue-500 text-white border-blue-600 shadow-2xl transform scale-[1.02]'\n                    : 'bg-white hover:bg-blue-50 border-slate-200 hover:border-blue-300 hover:shadow-xl'\n                }`}\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className={`relative w-12 h-12 rounded-2xl flex items-center justify-center font-black text-xl transition-all duration-300 shadow-lg ${\n                  currentAnswer === optionKey\n                    ? 'bg-blue-100 text-blue-600'\n                    : 'bg-gradient-to-br from-blue-500 to-indigo-500 text-white group-hover:from-blue-600 group-hover:to-indigo-600'\n                }`}>\n                  {label}\n                </div>\n                <span className={`relative flex-1 text-xl leading-relaxed font-semibold ${\n                  currentAnswer === optionKey ? 'text-blue-50' : 'text-slate-800 group-hover:text-slate-900'\n                }`}>\n                  {optionValue}\n                </span>\n              </button>\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render Fill-in-the-blank Question with modern design\n  const renderFillBlank = () => (\n    <div className=\"space-y-8\">\n      <div className=\"relative bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl p-8 border border-slate-200 shadow-lg\">\n        <label className=\"block text-slate-700 font-black mb-6 text-xl tracking-tight\">\n          Your Answer:\n        </label>\n        <input\n          type=\"text\"\n          value={currentAnswer}\n          onChange={(e) => handleAnswerSelect(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full p-6 border-2 border-slate-300 rounded-2xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 bg-white text-xl font-semibold transition-all duration-300 shadow-inner\"\n        />\n        <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-t from-black/5 to-transparent pointer-events-none\"></div>\n      </div>\n      {currentAnswer && (\n        <div className=\"bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-200 shadow-lg\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center shadow-lg\">\n              <div className=\"w-3 h-3 bg-white rounded-full\"></div>\n            </div>\n            <p className=\"text-emerald-700 font-bold text-xl tracking-tight\">\n              Answer recorded: \"{currentAnswer}\"\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  // Render Image-based Question with enhanced design\n  const renderImageQuestion = () => (\n    <div className=\"space-y-8\">\n      {question.imageUrl && (\n        <div className=\"text-center\">\n          <div className=\"inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200\">\n            <img\n              src={question.imageUrl}\n              alt=\"Question diagram\"\n              className=\"max-w-full max-h-96 rounded-lg mx-auto\"\n            />\n          </div>\n        </div>\n      )}\n\n      {question.options ? renderMCQ() : renderFillBlank()}\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* Timer Only - Below Navigation Bar */}\n      <div className=\"bg-white/95 backdrop-blur-md shadow-sm border-b border-slate-200/50\">\n        <div className=\"max-w-4xl mx-auto px-6 py-4\">\n          {/* Centered Timer */}\n          <div className=\"text-center\">\n            <div className={`inline-block px-6 py-3 rounded-2xl font-mono font-black text-xl shadow-lg border ${\n              timeLeft <= 60 ? 'bg-gradient-to-r from-red-500 to-red-600 text-white border-red-300' :\n              timeLeft <= 300 ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white border-amber-300' :\n              'bg-gradient-to-r from-emerald-500 to-green-500 text-white border-emerald-300'\n            }`}>\n              {formatTime(timeLeft)}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content - Centered Layout */}\n      <div className=\"max-w-4xl mx-auto px-6 py-8 pb-32\">\n        {/* Question Card */}\n        <div className=\"bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl border border-slate-200/50 p-8 md:p-12\">\n          {/* Question Header */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-3xl font-black text-slate-800 leading-tight mb-6 tracking-tight\">\n              {question.name}\n            </h2>\n            {question.image && (\n              <div className=\"mb-8\">\n                <div className=\"relative inline-block p-4 bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl shadow-xl border border-slate-200\">\n                  <img\n                    src={question.image}\n                    alt=\"Question\"\n                    className=\"max-w-full max-h-80 rounded-xl mx-auto shadow-lg\"\n                  />\n                  <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-t from-black/5 to-transparent pointer-events-none\"></div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Question Options */}\n          <div className=\"mb-8\">\n            {question.answerType === \"Options\" && renderMCQ()}\n            {(question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank()}\n            {question.imageUrl && renderImageQuestion()}\n          </div>\n        </div>\n      </div>\n\n      {/* Modern Bottom Navigation with Question Counter and Progress */}\n      <div className=\"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50\">\n        <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-slate-200/50 p-6\">\n          {/* Navigation Buttons */}\n          <div className=\"flex items-center justify-center gap-4 mb-6\">\n            {/* Previous Button */}\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`group relative overflow-hidden px-8 py-4 rounded-xl font-bold transition-all duration-300 ${\n                questionIndex === 0\n                  ? 'opacity-40 cursor-not-allowed bg-slate-100 text-slate-400'\n                  : 'bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'\n              }`}\n            >\n              {questionIndex > 0 && (\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              )}\n              <span className=\"relative\">Previous</span>\n            </button>\n\n            {/* Next/Submit Button */}\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`group relative overflow-hidden px-8 py-4 rounded-xl font-bold transition-all duration-300 ${\n                !isAnswered\n                  ? 'opacity-40 cursor-not-allowed bg-slate-100 text-slate-400'\n                  : questionIndex === totalQuestions - 1\n                  ? 'bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'\n                  : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'\n              }`}\n            >\n              {isAnswered && (\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              )}\n              <span className=\"relative\">\n                {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'}\n              </span>\n            </button>\n          </div>\n\n          {/* Question Counter */}\n          <div className=\"text-center mb-4\">\n            <div className=\"text-lg font-black text-slate-800 tracking-tight\">\n              Question {questionIndex + 1} of {totalQuestions}\n            </div>\n          </div>\n\n          {/* Progress Line */}\n          <div className=\"px-4\">\n            <div className=\"w-full bg-slate-200 rounded-full h-2 shadow-inner overflow-hidden\">\n              <div\n                className=\"h-full bg-blue-500 rounded-full transition-all duration-700 shadow-sm relative overflow-hidden\"\n                style={{ width: `${progressPercentage}%` }}\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400/30 to-transparent\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,QAAQ,GAAG,SAAS;EACpBC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAACQ,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdgB,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrCJ,gBAAgB,CAACI,MAAM,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACY,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACxB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,MAAMwB,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAAC1B,QAAQ,CAAC2B,OAAO,EAAE;MACrB,oBACE7B,OAAA;QAAK8B,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzE/B,OAAA;UAAK8B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC;IAEV;IAEA,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEnD,oBACEpC,OAAA;MAAK8B,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBM,MAAM,CAACC,OAAO,CAACpC,QAAQ,CAAC2B,OAAO,CAAC,CAACU,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;QAC7D,MAAMC,SAAS,GAAGC,MAAM,CAACJ,GAAG,CAAC,CAACK,IAAI,CAAC,CAAC;QACpC,MAAMC,WAAW,GAAGF,MAAM,CAACH,KAAK,CAAC,CAACI,IAAI,CAAC,CAAC;QACxC,MAAME,KAAK,GAAGX,YAAY,CAACM,KAAK,CAAC,IAAIC,SAAS;QAE9C,oBACE3C,OAAA;UAAqB8B,SAAS,EAAC,OAAO;UAAAC,QAAA,eACpC/B,OAAA;YACEgD,OAAO,EAAEA,CAAA,KAAM/B,kBAAkB,CAAC0B,SAAS,CAAE;YAC7Cb,SAAS,EAAG,oIACVjB,aAAa,KAAK8B,SAAS,GACvB,0EAA0E,GAC1E,kFACL,EAAE;YAAAZ,QAAA,gBAEH/B,OAAA;cAAK8B,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxJnC,OAAA;cAAK8B,SAAS,EAAG,4HACfjB,aAAa,KAAK8B,SAAS,GACvB,2BAA2B,GAC3B,8GACL,EAAE;cAAAZ,QAAA,EACAgB;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnC,OAAA;cAAM8B,SAAS,EAAG,yDAChBjB,aAAa,KAAK8B,SAAS,GAAG,cAAc,GAAG,2CAChD,EAAE;cAAAZ,QAAA,EACAe;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GAtBDQ,SAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBd,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMc,eAAe,GAAGA,CAAA,kBACtBjD,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/B,OAAA;MAAK8B,SAAS,EAAC,uGAAuG;MAAAC,QAAA,gBACpH/B,OAAA;QAAO8B,SAAS,EAAC,6DAA6D;QAAAC,QAAA,EAAC;MAE/E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRnC,OAAA;QACEkD,IAAI,EAAC,MAAM;QACXT,KAAK,EAAE5B,aAAc;QACrBsC,QAAQ,EAAGC,CAAC,IAAKnC,kBAAkB,CAACmC,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;QACpDa,WAAW,EAAC,0BAA0B;QACtCxB,SAAS,EAAC;MAAoM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/M,CAAC,eACFnC,OAAA;QAAK8B,SAAS,EAAC;MAA+F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClH,CAAC,EACLtB,aAAa,iBACZb,OAAA;MAAK8B,SAAS,EAAC,kGAAkG;MAAAC,QAAA,eAC/G/B,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C/B,OAAA;UAAK8B,SAAS,EAAC,gHAAgH;UAAAC,QAAA,eAC7H/B,OAAA;YAAK8B,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNnC,OAAA;UAAG8B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAAC,qBAC7C,EAAClB,aAAa,EAAC,IACnC;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAMoB,mBAAmB,GAAGA,CAAA,kBAC1BvD,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,GACvB7B,QAAQ,CAACsD,QAAQ,iBAChBxD,OAAA;MAAK8B,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B/B,OAAA;QAAK8B,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpF/B,OAAA;UACEyD,GAAG,EAAEvD,QAAQ,CAACsD,QAAS;UACvBE,GAAG,EAAC,kBAAkB;UACtB5B,SAAS,EAAC;QAAwC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAjC,QAAQ,CAAC2B,OAAO,GAAGD,SAAS,CAAC,CAAC,GAAGqB,eAAe,CAAC,CAAC;EAAA;IAAAjB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CACN;EAED,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,wEAAwE;IAAAC,QAAA,gBAErF/B,OAAA;MAAK8B,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClF/B,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAE1C/B,OAAA;UAAK8B,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/B,OAAA;YAAK8B,SAAS,EAAG,oFACfvB,QAAQ,IAAI,EAAE,GAAG,oEAAoE,GACrFA,QAAQ,IAAI,GAAG,GAAG,2EAA2E,GAC7F,8EACD,EAAE;YAAAwB,QAAA,EACAZ,UAAU,CAACZ,QAAQ;UAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAEhD/B,OAAA;QAAK8B,SAAS,EAAC,4FAA4F;QAAAC,QAAA,gBAEzG/B,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/B,OAAA;YAAI8B,SAAS,EAAC,sEAAsE;YAAAC,QAAA,EACjF7B,QAAQ,CAACyD;UAAI;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,EACJjC,QAAQ,CAAC0D,KAAK,iBACb5D,OAAA;YAAK8B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB/B,OAAA;cAAK8B,SAAS,EAAC,sHAAsH;cAAAC,QAAA,gBACnI/B,OAAA;gBACEyD,GAAG,EAAEvD,QAAQ,CAAC0D,KAAM;gBACpBF,GAAG,EAAC,UAAU;gBACd5B,SAAS,EAAC;cAAkD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACFnC,OAAA;gBAAK8B,SAAS,EAAC;cAA+F;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,GAClB7B,QAAQ,CAAC2D,UAAU,KAAK,SAAS,IAAIjC,SAAS,CAAC,CAAC,EAChD,CAAC1B,QAAQ,CAAC2D,UAAU,KAAK,WAAW,IAAI3D,QAAQ,CAAC2D,UAAU,KAAK,mBAAmB,KAAKZ,eAAe,CAAC,CAAC,EACzG/C,QAAQ,CAACsD,QAAQ,IAAID,mBAAmB,CAAC,CAAC;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE/B,OAAA;QAAK8B,SAAS,EAAC,oFAAoF;QAAAC,QAAA,gBAEjG/B,OAAA;UAAK8B,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAE1D/B,OAAA;YACEgD,OAAO,EAAEtC,UAAW;YACpBoD,QAAQ,EAAE3D,aAAa,KAAK,CAAE;YAC9B2B,SAAS,EAAG,6FACV3B,aAAa,KAAK,CAAC,GACf,2DAA2D,GAC3D,4JACL,EAAE;YAAA4B,QAAA,GAEF5B,aAAa,GAAG,CAAC,iBAChBH,OAAA;cAAK8B,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACxJ,eACDnC,OAAA;cAAM8B,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAGTnC,OAAA;YACEgD,OAAO,EAAEvC,MAAO;YAChBqD,QAAQ,EAAE,CAAC/C,UAAW;YACtBe,SAAS,EAAG,6FACV,CAACf,UAAU,GACP,2DAA2D,GAC3DZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GACpC,gKAAgK,GAChK,4JACL,EAAE;YAAA2B,QAAA,GAEFhB,UAAU,iBACTf,OAAA;cAAK8B,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACxJ,eACDnC,OAAA;cAAM8B,SAAS,EAAC,UAAU;cAAAC,QAAA,EACvB5B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;YAAM;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B/B,OAAA;YAAK8B,SAAS,EAAC,kDAAkD;YAAAC,QAAA,GAAC,WACvD,EAAC5B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB/B,OAAA;YAAK8B,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF/B,OAAA;cACE8B,SAAS,EAAC,gGAAgG;cAC1GiC,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAErC,kBAAmB;cAAG,CAAE;cAAAI,QAAA,eAE3C/B,OAAA;gBAAK8B,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAxPIX,YAAY;AAAAgE,EAAA,GAAZhE,YAAY;AA0PlB,eAAeA,YAAY;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}