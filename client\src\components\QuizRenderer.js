import React, { useState, useEffect } from 'react';

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  username = "Student",
  onNext,
  onPrevious,
  examTitle = "Quiz",
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Render MCQ Question with modern, clean design
  const renderMCQ = () => {
    if (!question.options) {
      return (
        <div className="text-center p-8 bg-red-50 rounded-xl border border-red-200">
          <div className="text-red-600 text-lg font-medium">No options available for this question.</div>
        </div>
      );
    }

    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];

    return (
      <div className="space-y-4">
        {Object.entries(question.options).map(([key, value], index) => {
          const optionKey = String(key).trim();
          const optionValue = String(value).trim();
          const label = optionLabels[index] || optionKey;

          return (
            <div key={optionKey} className="group">
              <button
                onClick={() => handleAnswerSelect(optionKey)}
                className={`group relative overflow-hidden w-full p-6 rounded-2xl text-left transition-all duration-300 flex items-center space-x-5 border-2 ${
                  currentAnswer === optionKey
                    ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-blue-500 shadow-2xl transform scale-[1.02]'
                    : 'bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border-slate-200 hover:border-blue-300 hover:shadow-xl'
                }`}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className={`relative w-12 h-12 rounded-2xl flex items-center justify-center font-black text-xl transition-all duration-300 shadow-lg ${
                  currentAnswer === optionKey
                    ? 'bg-white text-blue-600'
                    : 'bg-gradient-to-br from-blue-500 to-indigo-500 text-white group-hover:from-blue-600 group-hover:to-indigo-600'
                }`}>
                  {label}
                </div>
                <span className={`relative flex-1 text-xl leading-relaxed font-semibold ${
                  currentAnswer === optionKey ? 'text-white' : 'text-slate-800 group-hover:text-slate-900'
                }`}>
                  {optionValue}
                </span>
              </button>
            </div>
          );
        })}
      </div>
    );
  };

  // Render Fill-in-the-blank Question with modern design
  const renderFillBlank = () => (
    <div className="space-y-8">
      <div className="relative bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl p-8 border border-slate-200 shadow-lg">
        <label className="block text-slate-700 font-black mb-6 text-xl tracking-tight">
          Your Answer:
        </label>
        <input
          type="text"
          value={currentAnswer}
          onChange={(e) => handleAnswerSelect(e.target.value)}
          placeholder="Type your answer here..."
          className="w-full p-6 border-2 border-slate-300 rounded-2xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 bg-white text-xl font-semibold transition-all duration-300 shadow-inner"
        />
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/5 to-transparent pointer-events-none"></div>
      </div>
      {currentAnswer && (
        <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-200 shadow-lg">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center shadow-lg">
              <div className="w-3 h-3 bg-white rounded-full"></div>
            </div>
            <p className="text-emerald-700 font-bold text-xl tracking-tight">
              Answer recorded: "{currentAnswer}"
            </p>
          </div>
        </div>
      )}
    </div>
  );

  // Render Image-based Question with enhanced design
  const renderImageQuestion = () => (
    <div className="space-y-8">
      {question.imageUrl && (
        <div className="text-center">
          <div className="inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200">
            <img
              src={question.imageUrl}
              alt="Question diagram"
              className="max-w-full max-h-96 rounded-lg mx-auto"
            />
          </div>
        </div>
      )}

      {question.options ? renderMCQ() : renderFillBlank()}
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Timer Only - Below Navigation Bar */}
      <div className="bg-white/95 backdrop-blur-md shadow-sm border-b border-slate-200/50">
        <div className="max-w-4xl mx-auto px-6 py-4">
          {/* Centered Timer */}
          <div className="text-center">
            <div className={`inline-block px-6 py-3 rounded-2xl font-mono font-black text-xl shadow-lg border ${
              timeLeft <= 60 ? 'bg-gradient-to-r from-red-500 to-red-600 text-white border-red-300' :
              timeLeft <= 300 ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white border-amber-300' :
              'bg-gradient-to-r from-emerald-500 to-green-500 text-white border-emerald-300'
            }`}>
              {formatTime(timeLeft)}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Centered Layout */}
      <div className="max-w-4xl mx-auto px-6 py-8 pb-32">
        {/* Question Card */}
        <div className="bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl border border-slate-200/50 p-8 md:p-12">
          {/* Question Header */}
          <div className="mb-8">
            <h2 className="text-3xl font-black text-slate-800 leading-tight mb-6 tracking-tight">
              {question.name}
            </h2>
            {question.image && (
              <div className="mb-8">
                <div className="relative inline-block p-4 bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl shadow-xl border border-slate-200">
                  <img
                    src={question.image}
                    alt="Question"
                    className="max-w-full max-h-80 rounded-xl mx-auto shadow-lg"
                  />
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/5 to-transparent pointer-events-none"></div>
                </div>
              </div>
            )}
          </div>

          {/* Question Options */}
          <div className="mb-8">
            {question.answerType === "Options" && renderMCQ()}
            {(question.answerType === "Free Text" || question.answerType === "Fill in the Blank") && renderFillBlank()}
            {question.imageUrl && renderImageQuestion()}
          </div>
        </div>
      </div>

      {/* Modern Bottom Navigation with Question Counter and Progress */}
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-slate-200/50 p-6">
          {/* Question Counter */}
          <div className="text-center mb-4">
            <div className="text-lg font-black text-slate-800 tracking-tight">
              Question {questionIndex + 1} of {totalQuestions}
            </div>
          </div>

          {/* Progress Line */}
          <div className="mb-6 px-4">
            <div className="w-full bg-slate-200 rounded-full h-2 shadow-inner overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 rounded-full transition-all duration-700 shadow-sm relative overflow-hidden"
                style={{ width: `${progressPercentage}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex items-center justify-center gap-4">
            {/* Previous Button */}
            <button
              onClick={onPrevious}
              disabled={questionIndex === 0}
              className={`group relative overflow-hidden px-8 py-4 rounded-xl font-bold transition-all duration-300 ${
                questionIndex === 0
                  ? 'opacity-40 cursor-not-allowed bg-slate-100 text-slate-400'
                  : 'bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
              }`}
            >
              {questionIndex > 0 && (
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              )}
              <span className="relative">Previous</span>
            </button>

            {/* Next/Submit Button */}
            <button
              onClick={onNext}
              disabled={!isAnswered}
              className={`group relative overflow-hidden px-8 py-4 rounded-xl font-bold transition-all duration-300 ${
                !isAnswered
                  ? 'opacity-40 cursor-not-allowed bg-slate-100 text-slate-400'
                  : questionIndex === totalQuestions - 1
                  ? 'bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
                  : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
              }`}
            >
              {isAnswered && (
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              )}
              <span className="relative">
                {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizRenderer;
