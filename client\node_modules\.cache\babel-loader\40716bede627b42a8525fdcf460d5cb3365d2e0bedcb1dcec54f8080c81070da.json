{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\WriteExam\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { getExamById } from \"../../../apicalls/exams\";\nimport { addReport } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport Instructions from \"./Instructions\";\nimport Pass from \"../../../assets/pass.gif\";\nimport Fail from \"../../../assets/fail.gif\";\nimport Confetti from \"react-confetti\";\nimport useWindowSize from \"react-use/lib/useWindowSize\";\nimport PassSound from \"../../../assets/pass.mp3\";\nimport FailSound from \"../../../assets/fail.mp3\";\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\nimport ContentRenderer from \"../../../components/ContentRenderer\";\nimport QuizRenderer from \"../../../components/QuizRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction WriteExam() {\n  _s();\n  var _result$correctAnswer, _result$correctAnswer2, _result$correctAnswer3, _result$correctAnswer4;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [result, setResult] = useState({});\n  const params = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [view, setView] = useState(\"instructions\");\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const [explanations, setExplanations] = useState({});\n  const getExamData = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({\n        examId: params.id\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        var _response$data, _response$data2;\n        setQuestions(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || []);\n        setExamData(response.data);\n        setSecondsLeft(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.duration) || 0);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [params.id, dispatch]);\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      const indexMap = [];\n      questions.forEach((q, idx) => {\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          indexMap.push(idx);\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      const wrongPayload = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: q.correctAnswer || q.correctOption,\n              userAnswer: userAnswerKey\n            });\n          }\n        } else if (q.answerType === \"Options\") {\n          const correctKey = q.correctOption;\n          const correctValue = q.options && q.options[correctKey] || correctKey;\n          const userValue = q.options && q.options[userAnswerKey] || userAnswerKey || \"\";\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: correctValue,\n              userAnswer: userValue\n            });\n          }\n        }\n      });\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict\n      };\n      setResult(tempResult);\n      const response = await addReport({\n        exam: params.id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        setView(\"result\");\n        window.scrollTo(0, 0);\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const startTimer = () => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0;\n    setSecondsLeft(totalSeconds);\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  };\n  useEffect(() => {\n    if (timeUp && view === \"questions\") {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, view, intervalId, calculateResult]);\n  useEffect(() => {\n    if (params.id) {\n      getExamData();\n    }\n  }, [params.id, getExamData]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  // Repair function for fixing orphaned questions\n  const repairExamQuestions = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await fetch('/api/exams/repair-exam-questions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          examId: params.id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        message.success(data.message);\n        // Reload the exam data\n        getExamData();\n      } else {\n        message.error(data.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to repair exam questions\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Check if user is authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-10 h-10 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-8\",\n          children: \"Please log in to access the exam and start your learning journey.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          onClick: () => navigate(\"/login\"),\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this);\n  }\n  return examData ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [view !== \"instructions\" && view !== \"questions\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/80 backdrop-blur-sm border-b border-blue-100 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-6 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-white\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n              children: (examData === null || examData === void 0 ? void 0 : examData.name) || 'Loading...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mt-1\",\n              children: \"Exam Results & Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 9\n    }, this), view === \"instructions\" && /*#__PURE__*/_jsxDEV(Instructions, {\n      examData: examData,\n      setView: setView,\n      startTimer: startTimer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 9\n    }, this), view === \"questions\" && (questions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-amber-800 mb-4\",\n          children: \"No Questions Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-700 mb-8 text-lg leading-relaxed\",\n          children: \"This exam appears to have no questions. Let's fix this issue and get you started!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: repairExamQuestions,\n          className: \"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          children: \"\\uD83D\\uDD27 Repair Questions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 11\n    }, this) : questions[selectedQuestionIndex] && /*#__PURE__*/_jsxDEV(QuizRenderer, {\n      question: questions[selectedQuestionIndex],\n      questionIndex: selectedQuestionIndex,\n      totalQuestions: questions.length,\n      selectedAnswer: selectedOptions[selectedQuestionIndex],\n      onAnswerChange: answer => setSelectedOptions({\n        ...selectedOptions,\n        [selectedQuestionIndex]: answer\n      }),\n      timeLeft: secondsLeft,\n      username: (user === null || user === void 0 ? void 0 : user.name) || \"Student\",\n      examTitle: (examData === null || examData === void 0 ? void 0 : examData.name) || \"Quiz\",\n      onNext: () => {\n        if (selectedQuestionIndex === questions.length - 1) {\n          calculateResult();\n        } else {\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      },\n      onPrevious: () => setSelectedQuestionIndex(selectedQuestionIndex - 1)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 13\n    }, this)), view === \"result\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\",\n      children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 41\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-8 py-10 text-center relative ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\" : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500 to-green-600\" : \"bg-gradient-to-br from-amber-500 to-orange-600\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: result.verdict === \"Pass\" ? Pass : Fail,\n                  alt: result.verdict,\n                  className: \"w-12 h-12 object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-4xl font-black mb-4 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"}`,\n                children: result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\",\n                children: result.verdict === \"Pass\" ? \"You've mastered this exam with flying colors!\" : \"Every challenge makes you stronger. Try again!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-blue-600 mb-2 tracking-tight\",\n                    children: [Math.round((((_result$correctAnswer = result.correctAnswers) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.length) || 0) / questions.length * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-blue-700/80 uppercase tracking-wider\",\n                    children: \"Your Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-emerald-600 mb-2 tracking-tight\",\n                    children: [((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0, \"/\", questions.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\",\n                    children: \"Correct\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\" : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"} to-transparent`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-4xl font-black mb-2 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                    children: result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-sm font-bold uppercase tracking-wider ${result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"}`,\n                    children: result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative bg-slate-100 rounded-2xl p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-bold text-slate-700 mb-1\",\n                    children: \"Performance Overview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-slate-500\",\n                    children: \"Your achievement level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\" : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"}`,\n                      style: {\n                        width: `${(((_result$correctAnswer3 = result.correctAnswers) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.length) || 0) / questions.length * 100}%`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 456,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"0%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-lg font-black tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                      children: [Math.round((((_result$correctAnswer4 = result.correctAnswers) === null || _result$correctAnswer4 === void 0 ? void 0 : _result$correctAnswer4.length) || 0) / questions.length * 100), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"100%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                onClick: () => setView(\"review\"),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative\",\n                  children: \"Review Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                onClick: () => {\n                  // Reset exam state\n                  setSelectedOptions({});\n                  setSelectedQuestionIndex(0);\n                  setSecondsLeft(examData.duration || 0);\n                  setView(\"instructions\");\n                  setResult(null);\n                  setTimeUp(false);\n                  // Clear any existing timer\n                  if (intervalId) {\n                    clearInterval(intervalId);\n                    setIntervalId(null);\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative\",\n                  children: \"Retry Exam\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                onClick: () => navigate(\"/user/dashboard\"),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative\",\n                  children: \"Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 9\n    }, this), view === \"review\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2\",\n              children: \"Answer Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-slate-600\",\n              children: \"Quick overview of your answers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3 mb-6\",\n          children: questions.map((question, index) => {\n            const correctAnswer = question.answerType === \"Options\" ? question.correctOption : question.correctAnswer;\n            const isCorrect = correctAnswer === selectedOptions[index];\n            const userAnswer = selectedOptions[index];\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"backdrop-blur-md rounded-lg shadow-md border-2 p-4\",\n              style: {\n                backgroundColor: isCorrect ? '#bbf7d0' : '#fecaca',\n                borderColor: isCorrect ? '#22c55e' : '#ef4444'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\",\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-slate-800 font-medium leading-relaxed\",\n                      children: question.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-slate-600\",\n                  children: \"Your Answer: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`,\n                  children: question.answerType === \"Options\" ? question.options && userAnswer && question.options[userAnswer] || userAnswer || \"Not answered\" : userAnswer || \"Not answered\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 23\n                }, this), isCorrect ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-green-600 text-2xl font-black\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-red-600 text-2xl font-black\",\n                  children: \"\\u2717\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 21\n              }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-slate-600\",\n                  children: \"Correct Answer: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-green-700\",\n                  children: question.answerType === \"Options\" ? question.options && question.options[question.correctOption] || question.correctOption : question.correctAnswer || question.correctOption\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-green-500 text-2xl font-black\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 23\n              }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\",\n                  onClick: () => {\n                    console.log('Fetching explanation for:', question.name);\n                    fetchExplanation(question.name, question.answerType === \"Options\" ? question.options && question.options[question.correctOption] || question.correctOption : question.correctAnswer || question.correctOption, question.answerType === \"Options\" ? question.options && question.options[userAnswer] || userAnswer || \"Not answered\" : userAnswer || \"Not answered\", question.image);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83D\\uDCA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Get Explanation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 23\n              }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 p-4 bg-white rounded-xl border-l-4 border-l-blue-500 shadow-md border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600 text-lg mr-2\",\n                    children: \"\\uD83D\\uDCA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"font-bold text-gray-800 text-base\",\n                    children: [\"Question \", index + 1, \" - Explanation\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 25\n                }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700 text-sm font-semibold\",\n                      children: \"\\uD83D\\uDCCA Reference Diagram:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: question.image || question.imageUrl,\n                      alt: \"Question diagram\",\n                      className: \"max-w-full max-h-60 object-contain rounded border border-gray-300 shadow-sm\",\n                      style: {\n                        maxWidth: '380px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-800 leading-relaxed bg-gray-50 p-3 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                    text: explanations[question.name]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n            onClick: () => setView(\"result\"),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative\",\n              children: \"\\u2190 Back to Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n            onClick: () => navigate(\"/user/dashboard\"),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 518,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this) : null;\n}\n_s(WriteExam, \"ZNpCVL+CJB3fnQYZ800cmvPHL84=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useWindowSize];\n});\n_c = WriteExam;\nexport default WriteExam;\nvar _c;\n$RefreshReg$(_c, \"WriteExam\");", "map": {"version": 3, "names": ["message", "React", "useCallback", "useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "getExamById", "addReport", "HideLoading", "ShowLoading", "Instructions", "Pass", "Fail", "Confetti", "useWindowSize", "PassSound", "FailSound", "chatWithChatGPTToGetAns", "chatWithChatGPTToExplainAns", "Content<PERSON><PERSON><PERSON>", "Quiz<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "WriteExam", "_s", "_result$correctAnswer", "_result$correctAnswer2", "_result$correctAnswer3", "_result$correctAnswer4", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "result", "setResult", "params", "dispatch", "navigate", "view", "<PERSON><PERSON><PERSON><PERSON>", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "user", "state", "width", "height", "explanations", "setExplanations", "getExamData", "response", "examId", "id", "success", "_response$data", "_response$data2", "data", "duration", "error", "checkFreeTextAnswers", "payload", "length", "calculateResult", "_id", "freeTextPayload", "indexMap", "for<PERSON>ach", "q", "idx", "answerType", "push", "question", "name", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "wrongPayload", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "correctValue", "options", "userValue", "verdict", "passingMarks", "tempResult", "exam", "window", "scrollTo", "Audio", "play", "fetchExplanation", "imageUrl", "prev", "explanation", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "repairExamQuestions", "fetch", "method", "headers", "localStorage", "getItem", "body", "JSON", "stringify", "json", "className", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "answer", "timeLeft", "username", "examTitle", "onNext", "onPrevious", "src", "alt", "Math", "round", "style", "map", "index", "backgroundColor", "borderColor", "console", "log", "image", "max<PERSON><PERSON><PERSON>", "text", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/WriteExam/index.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nimport QuizRenderer from \"../../../components/QuizRenderer\";\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getExamById({ examId: params.id });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setQuestions(response.data?.questions || []);\r\n        setExamData(response.data);\r\n        setSecondsLeft(response.data?.duration || 0);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\r\n      const tempResult = { correctAnswers, wrongAnswers, verdict };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0;\r\n    setSecondsLeft(totalSeconds);\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, [params.id, getExamData]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      {/* Enhanced Header for non-quiz views */}\r\n      {view !== \"instructions\" && view !== \"questions\" && (\r\n        <div className=\"bg-white/80 backdrop-blur-sm border-b border-blue-100 shadow-lg\">\r\n          <div className=\"max-w-6xl mx-auto px-6 py-8\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center\">\r\n                <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\r\n                  {examData?.name || 'Loading...'}\r\n                </h1>\r\n                <p className=\"text-gray-600 mt-1\">Exam Results & Review</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-8 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. Let's fix this issue and get you started!\r\n              </p>\r\n              <button\r\n                onClick={repairExamQuestions}\r\n                className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n              >\r\n                🔧 Repair Questions\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          questions[selectedQuestionIndex] && (\r\n            <QuizRenderer\r\n              question={questions[selectedQuestionIndex]}\r\n              questionIndex={selectedQuestionIndex}\r\n              totalQuestions={questions.length}\r\n              selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n              onAnswerChange={(answer) =>\r\n                setSelectedOptions({\r\n                  ...selectedOptions,\r\n                  [selectedQuestionIndex]: answer,\r\n                })\r\n              }\r\n              timeLeft={secondsLeft}\r\n              username={user?.name || \"Student\"}\r\n              examTitle={examData?.name || \"Quiz\"}\r\n              onNext={() => {\r\n                if (selectedQuestionIndex === questions.length - 1) {\r\n                  calculateResult();\r\n                } else {\r\n                  setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n                }\r\n              }}\r\n              onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}\r\n            />\r\n          )\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {result.correctAnswers?.length || 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => {\r\n                      // Reset exam state\r\n                      setSelectedOptions({});\r\n                      setSelectedQuestionIndex(0);\r\n                      setSecondsLeft(examData.duration || 0);\r\n                      setView(\"instructions\");\r\n                      setResult(null);\r\n                      setTimeUp(false);\r\n                      // Clear any existing timer\r\n                      if (intervalId) {\r\n                        clearInterval(intervalId);\r\n                        setIntervalId(null);\r\n                      }\r\n                    }}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Retry Exam</span>\r\n                  </button>\r\n\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => navigate(\"/user/dashboard\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Dashboard</span>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\">\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            {/* Simple Header */}\r\n            <div className=\"text-center mb-6\">\r\n              <div className=\"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\">\r\n                <h2 className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2\">\r\n                  Answer Review\r\n                </h2>\r\n                <p className=\"text-slate-600\">Quick overview of your answers</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Compact Questions Review */}\r\n            <div className=\"space-y-3 mb-6\">\r\n              {questions.map((question, index) => {\r\n                const correctAnswer = question.answerType === \"Options\"\r\n                  ? question.correctOption\r\n                  : question.correctAnswer;\r\n                const isCorrect = correctAnswer === selectedOptions[index];\r\n                const userAnswer = selectedOptions[index];\r\n\r\n                return (\r\n                  <div\r\n                    key={index}\r\n                    className=\"backdrop-blur-md rounded-lg shadow-md border-2 p-4\"\r\n                    style={{\r\n                      backgroundColor: isCorrect ? '#bbf7d0' : '#fecaca',\r\n                      borderColor: isCorrect ? '#22c55e' : '#ef4444'\r\n                    }}\r\n                  >\r\n                    {/* Question */}\r\n                    <div className=\"mb-3\">\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div className=\"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\">\r\n                          {index + 1}\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                          <p className=\"text-slate-800 font-medium leading-relaxed\">{question.name}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Your Answer with Visual Indicator */}\r\n                    <div className=\"mb-2\">\r\n                      <span className=\"text-sm font-semibold text-slate-600\">Your Answer: </span>\r\n                      <span className={`font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\r\n                        {question.answerType === \"Options\"\r\n                          ? (question.options && userAnswer && question.options[userAnswer]) || userAnswer || \"Not answered\"\r\n                          : userAnswer || \"Not answered\"}\r\n                      </span>\r\n                      {isCorrect ? (\r\n                        <span className=\"ml-3 text-green-600 text-2xl font-black\">✓</span>\r\n                      ) : (\r\n                        <span className=\"ml-3 text-red-600 text-2xl font-black\">✗</span>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Correct Answer (only for wrong answers) */}\r\n                    {!isCorrect && (\r\n                      <div className=\"mb-2\">\r\n                        <span className=\"text-sm font-semibold text-slate-600\">Correct Answer: </span>\r\n                        <span className=\"font-medium text-green-700\">\r\n                          {question.answerType === \"Options\"\r\n                            ? (question.options && question.options[question.correctOption]) || question.correctOption\r\n                            : (question.correctAnswer || question.correctOption)}\r\n                        </span>\r\n                        <span className=\"ml-3 text-green-500 text-2xl font-black\">✓</span>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* See Explanation Button (only for wrong answers) */}\r\n                    {!isCorrect && (\r\n                      <div className=\"mt-2\">\r\n                        <button\r\n                          className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\"\r\n                          onClick={() => {\r\n                            console.log('Fetching explanation for:', question.name);\r\n                            fetchExplanation(\r\n                              question.name,\r\n                              question.answerType === \"Options\"\r\n                                ? (question.options && question.options[question.correctOption]) || question.correctOption\r\n                                : (question.correctAnswer || question.correctOption),\r\n                              question.answerType === \"Options\"\r\n                                ? (question.options && question.options[userAnswer]) || userAnswer || \"Not answered\"\r\n                                : userAnswer || \"Not answered\",\r\n                              question.image\r\n                            );\r\n                          }}\r\n                        >\r\n                          <span>💡</span>\r\n                          <span>Get Explanation</span>\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Explanation */}\r\n                    {explanations[question.name] && (\r\n                      <div className=\"mt-2 p-4 bg-white rounded-xl border-l-4 border-l-blue-500 shadow-md border border-gray-200\">\r\n                        <div className=\"flex items-center mb-3\">\r\n                          <span className=\"text-blue-600 text-lg mr-2\">💡</span>\r\n                          <h6 className=\"font-bold text-gray-800 text-base\">\r\n                            Question {index + 1} - Explanation\r\n                          </h6>\r\n                        </div>\r\n\r\n                        {/* Show diagram/image for image-based questions */}\r\n                        {(question.image || question.imageUrl) && (\r\n                          <div className=\"mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200\">\r\n                            <div className=\"flex items-center mb-2\">\r\n                              <span className=\"text-gray-700 text-sm font-semibold\">📊 Reference Diagram:</span>\r\n                            </div>\r\n                            <div className=\"flex justify-center\">\r\n                              <img\r\n                                src={question.image || question.imageUrl}\r\n                                alt=\"Question diagram\"\r\n                                className=\"max-w-full max-h-60 object-contain rounded border border-gray-300 shadow-sm\"\r\n                                style={{ maxWidth: '380px' }}\r\n                              />\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n\r\n                        <div className=\"text-sm text-gray-800 leading-relaxed bg-gray-50 p-3 rounded-lg\">\r\n                          <ContentRenderer text={explanations[question.name]} />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n\r\n            {/* Modern Navigation */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\r\n              <button\r\n                className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                onClick={() => setView(\"result\")}\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative\">← Back to Results</span>\r\n              </button>\r\n\r\n              <button\r\n                className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                onClick={() => navigate(\"/user/dashboard\")}\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative\">Dashboard</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,uBAAuB,EAAEC,2BAA2B,QAAQ,wBAAwB;AAC7F,OAAOC,eAAe,MAAM,qCAAqC;AAEjE,OAAOC,YAAY,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACoC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMsC,MAAM,GAAGlC,SAAS,CAAC,CAAC;EAC1B,MAAMmC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAG1C,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC+C,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM;IAAEiD;EAAK,CAAC,GAAG/C,WAAW,CAAEgD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM;IAAEE,KAAK;IAAEC;EAAO,CAAC,GAAGvC,aAAa,CAAC,CAAC;EACzC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpD,MAAMuD,WAAW,GAAGzD,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFyC,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgD,QAAQ,GAAG,MAAMnD,WAAW,CAAC;QAAEoD,MAAM,EAAEnB,MAAM,CAACoB;MAAG,CAAC,CAAC;MACzDnB,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIiD,QAAQ,CAACG,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACpB9B,YAAY,CAAC,EAAA6B,cAAA,GAAAJ,QAAQ,CAACM,IAAI,cAAAF,cAAA,uBAAbA,cAAA,CAAe9B,SAAS,KAAI,EAAE,CAAC;QAC5CD,WAAW,CAAC2B,QAAQ,CAACM,IAAI,CAAC;QAC1BlB,cAAc,CAAC,EAAAiB,eAAA,GAAAL,QAAQ,CAACM,IAAI,cAAAD,eAAA,uBAAbA,eAAA,CAAeE,QAAQ,KAAI,CAAC,CAAC;MAC9C,CAAC,MAAM;QACLnE,OAAO,CAACoE,KAAK,CAACR,QAAQ,CAAC5D,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdzB,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC0C,MAAM,CAACoB,EAAE,EAAEnB,QAAQ,CAAC,CAAC;EAEzB,MAAM0B,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAEL;IAAK,CAAC,GAAG,MAAM9C,uBAAuB,CAACkD,OAAO,CAAC;IACvD,OAAOJ,IAAI;EACb,CAAC;EAED,MAAMM,eAAe,GAAGtE,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF;MACA,IAAI,CAACmD,IAAI,IAAI,CAACA,IAAI,CAACoB,GAAG,EAAE;QACtBzE,OAAO,CAACoE,KAAK,CAAC,sCAAsC,CAAC;QACrDxB,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAD,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM8D,eAAe,GAAG,EAAE;MAC1B,MAAMC,QAAQ,GAAG,EAAE;MAEnBzC,SAAS,CAAC0C,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAACE,UAAU,KAAK,WAAW,IAAIF,CAAC,CAACE,UAAU,KAAK,mBAAmB,EAAE;UACxEJ,QAAQ,CAACK,IAAI,CAACF,GAAG,CAAC;UAClBJ,eAAe,CAACM,IAAI,CAAC;YACnBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;YAChBC,cAAc,EAAEN,CAAC,CAACO,aAAa,IAAIP,CAAC,CAACQ,aAAa;YAClDC,UAAU,EAAEhD,eAAe,CAACwC,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMS,UAAU,GAAG,MAAMlB,oBAAoB,CAACK,eAAe,CAAC;MAC9D,MAAMc,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACX,OAAO,CAAEa,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACjD,MAAM,IAAI,OAAOiD,CAAC,CAACjD,MAAM,CAACkD,SAAS,KAAK,SAAS,EAAE;UACvDF,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAGQ,CAAC,CAACjD,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOiD,CAAC,CAACC,SAAS,KAAK,SAAS,EAAE;UAC3CF,MAAM,CAACC,CAAC,CAACR,QAAQ,CAAC,GAAG;YAAES,SAAS,EAAED,CAAC,CAACC,SAAS;YAAEC,MAAM,EAAEF,CAAC,CAACE,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,YAAY,GAAG,EAAE;MAEvB5D,SAAS,CAAC0C,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMiB,aAAa,GAAGzD,eAAe,CAACwC,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAACE,UAAU,KAAK,WAAW,IAAIF,CAAC,CAACE,UAAU,KAAK,mBAAmB,EAAE;UACxE,MAAM;YAAEW,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGH,MAAM,CAACX,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAMc,QAAQ,GAAG;YAAE,GAAGnB,CAAC;YAAES,UAAU,EAAES,aAAa;YAAEJ;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACZ,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACb,IAAI,CAACgB,QAAQ,CAAC;YAC3BF,YAAY,CAACd,IAAI,CAAC;cAChBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;cAChBC,cAAc,EAAEN,CAAC,CAACO,aAAa,IAAIP,CAAC,CAACQ,aAAa;cAClDC,UAAU,EAAES;YACd,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAIlB,CAAC,CAACE,UAAU,KAAK,SAAS,EAAE;UACrC,MAAMkB,UAAU,GAAGpB,CAAC,CAACQ,aAAa;UAClC,MAAMa,YAAY,GAAIrB,CAAC,CAACsB,OAAO,IAAItB,CAAC,CAACsB,OAAO,CAACF,UAAU,CAAC,IAAKA,UAAU;UACvE,MAAMG,SAAS,GAAIvB,CAAC,CAACsB,OAAO,IAAItB,CAAC,CAACsB,OAAO,CAACJ,aAAa,CAAC,IAAKA,aAAa,IAAI,EAAE;UAEhF,MAAML,SAAS,GAAGO,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGnB,CAAC;YAAES,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIL,SAAS,EAAE;YACbE,cAAc,CAACZ,IAAI,CAACgB,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACb,IAAI,CAACgB,QAAQ,CAAC;YAC3BF,YAAY,CAACd,IAAI,CAAC;cAChBC,QAAQ,EAAEJ,CAAC,CAACK,IAAI;cAChBC,cAAc,EAAEe,YAAY;cAC5BZ,UAAU,EAAEc;YACd,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;MAEF,MAAMC,OAAO,GAAGT,cAAc,CAACrB,MAAM,IAAIvC,QAAQ,CAACsE,YAAY,GAAG,MAAM,GAAG,MAAM;MAChF,MAAMC,UAAU,GAAG;QAAEX,cAAc;QAAEC,YAAY;QAAEQ;MAAQ,CAAC;MAE5D5D,SAAS,CAAC8D,UAAU,CAAC;MAErB,MAAM3C,QAAQ,GAAG,MAAMlD,SAAS,CAAC;QAC/B8F,IAAI,EAAE9D,MAAM,CAACoB,EAAE;QACftB,MAAM,EAAE+D,UAAU;QAClBlD,IAAI,EAAEA,IAAI,CAACoB;MACb,CAAC,CAAC;MAEF,IAAIb,QAAQ,CAACG,OAAO,EAAE;QACpBjB,OAAO,CAAC,QAAQ,CAAC;QACjB2D,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,IAAIC,KAAK,CAACN,OAAO,KAAK,MAAM,GAAGnF,SAAS,GAAGC,SAAS,CAAC,CAACyF,IAAI,CAAC,CAAC;MAC9D,CAAC,MAAM;QACL5G,OAAO,CAACoE,KAAK,CAACR,QAAQ,CAAC5D,OAAO,CAAC;MACjC;MACA2C,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACdzB,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACkC,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEU,MAAM,CAACoB,EAAE,EAAET,IAAI,EAAET,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE/E,MAAMkE,gBAAgB,GAAG,MAAAA,CAAO5B,QAAQ,EAAEE,cAAc,EAAEG,UAAU,EAAEwB,QAAQ,KAAK;IACjF,IAAI;MACFnE,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgD,QAAQ,GAAG,MAAMvC,2BAA2B,CAAC;QAAE4D,QAAQ;QAAEE,cAAc;QAAEG,UAAU;QAAEwB;MAAS,CAAC,CAAC;MACtGnE,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIiD,QAAQ,CAACG,OAAO,EAAE;QACpBL,eAAe,CAAEqD,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAAC9B,QAAQ,GAAGrB,QAAQ,CAACoD;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLhH,OAAO,CAACoE,KAAK,CAACR,QAAQ,CAACQ,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdzB,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMiH,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG,CAAAlF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmC,QAAQ,KAAI,CAAC;IAC5CnB,cAAc,CAACkE,YAAY,CAAC;IAE5B,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCpE,cAAc,CAAEqE,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACLnE,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAAC+D,aAAa,CAAC;EAC9B,CAAC;EAEDhH,SAAS,CAAC,MAAM;IACd,IAAI8C,MAAM,IAAIJ,IAAI,KAAK,WAAW,EAAE;MAClCyE,aAAa,CAACnE,UAAU,CAAC;MACzBqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACvB,MAAM,EAAEJ,IAAI,EAAEM,UAAU,EAAEqB,eAAe,CAAC,CAAC;EAE/CrE,SAAS,CAAC,MAAM;IACd,IAAIuC,MAAM,CAACoB,EAAE,EAAE;MACbH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACjB,MAAM,CAACoB,EAAE,EAAEH,WAAW,CAAC,CAAC;EAE5BxD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIgD,UAAU,EAAE;QACdmE,aAAa,CAACnE,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMoE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF5E,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgD,QAAQ,GAAG,MAAM4D,KAAK,CAAC,kCAAkC,EAAE;QAC/DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAElE,MAAM,EAAEnB,MAAM,CAACoB;QAAG,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMI,IAAI,GAAG,MAAMN,QAAQ,CAACoE,IAAI,CAAC,CAAC;MAClC,IAAI9D,IAAI,CAACH,OAAO,EAAE;QAChB/D,OAAO,CAAC+D,OAAO,CAACG,IAAI,CAAClE,OAAO,CAAC;QAC7B;QACA2D,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACL3D,OAAO,CAACoE,KAAK,CAACF,IAAI,CAAClE,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,iCAAiC,CAAC;IAClD,CAAC,SAAS;MACRzB,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,IAAI,CAAC0C,IAAI,EAAE;IACT,oBACE5B,OAAA;MAAKwG,SAAS,EAAC,qGAAqG;MAAAC,QAAA,eAClHzG,OAAA;QAAKwG,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBACxHzG,OAAA;UAAKwG,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/FzG,OAAA;YAAKwG,SAAS,EAAC,sBAAsB;YAACE,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,eAC3EzG,OAAA;cAAM4G,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,8JAA8J;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlH,OAAA;UAAIwG,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFlH,OAAA;UAAGwG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvGlH,OAAA;UACEwG,SAAS,EAAC,mNAAmN;UAC7NW,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,QAAQ,CAAE;UAAAsF,QAAA,EACnC;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAO3G,QAAQ,gBACbP,OAAA;IAAKwG,SAAS,EAAC,oEAAoE;IAAAC,QAAA,GAEhFrF,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,WAAW,iBAC9CpB,OAAA;MAAKwG,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EzG,OAAA;QAAKwG,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CzG,OAAA;UAAKwG,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzG,OAAA;YAAKwG,SAAS,EAAC,oGAAoG;YAAAC,QAAA,eACjHzG,OAAA;cAAKwG,SAAS,EAAC,oBAAoB;cAACE,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAF,QAAA,eACzEzG,OAAA;gBAAM6G,CAAC,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlH,OAAA;YAAAyG,QAAA,gBACEzG,OAAA;cAAIwG,SAAS,EAAC,+FAA+F;cAAAC,QAAA,EAC1G,CAAAlG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkD,IAAI,KAAI;YAAY;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACLlH,OAAA;cAAGwG,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA9F,IAAI,KAAK,cAAc,iBACtBpB,OAAA,CAACZ,YAAY;MACXmB,QAAQ,EAAEA,QAAS;MACnBc,OAAO,EAAEA,OAAQ;MACjBmE,UAAU,EAAEA;IAAW;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF,EAEA9F,IAAI,KAAK,WAAW,KACnBX,SAAS,CAACqC,MAAM,KAAK,CAAC,gBACpB9C,OAAA;MAAKwG,SAAS,EAAC,sGAAsG;MAAAC,QAAA,eACnHzG,OAAA;QAAKwG,SAAS,EAAC,4GAA4G;QAAAC,QAAA,gBACzHzG,OAAA;UAAKwG,SAAS,EAAC,8HAA8H;UAAAC,QAAA,eAC3IzG,OAAA;YAAKwG,SAAS,EAAC,sBAAsB;YAACE,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,eAC3EzG,OAAA;cAAM4G,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mNAAmN;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlH,OAAA;UAAIwG,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ElH,OAAA;UAAGwG,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAE3D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlH,OAAA;UACEmH,OAAO,EAAErB,mBAAoB;UAC7BU,SAAS,EAAC,iNAAiN;UAAAC,QAAA,EAC5N;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GAENzG,SAAS,CAACE,qBAAqB,CAAC,iBAC9BX,OAAA,CAACF,YAAY;MACX0D,QAAQ,EAAE/C,SAAS,CAACE,qBAAqB,CAAE;MAC3CyG,aAAa,EAAEzG,qBAAsB;MACrC0G,cAAc,EAAE5G,SAAS,CAACqC,MAAO;MACjCwE,cAAc,EAAEzG,eAAe,CAACF,qBAAqB,CAAE;MACvD4G,cAAc,EAAGC,MAAM,IACrB1G,kBAAkB,CAAC;QACjB,GAAGD,eAAe;QAClB,CAACF,qBAAqB,GAAG6G;MAC3B,CAAC,CACF;MACDC,QAAQ,EAAEnG,WAAY;MACtBoG,QAAQ,EAAE,CAAA9F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,KAAI,SAAU;MAClCkE,SAAS,EAAE,CAAApH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkD,IAAI,KAAI,MAAO;MACpCmE,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAIjH,qBAAqB,KAAKF,SAAS,CAACqC,MAAM,GAAG,CAAC,EAAE;UAClDC,eAAe,CAAC,CAAC;QACnB,CAAC,MAAM;UACLnC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACFkH,UAAU,EAAEA,CAAA,KAAMjH,wBAAwB,CAACD,qBAAqB,GAAG,CAAC;IAAE;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CAEJ,CACF,EAEA9F,IAAI,KAAK,QAAQ,iBAChBpB,OAAA;MAAKwG,SAAS,EAAC,6EAA6E;MAAAC,QAAA,GACzF1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,iBAAI5E,OAAA,CAACT,QAAQ;QAACuC,KAAK,EAAEA,KAAM;QAACC,MAAM,EAAEA;MAAO;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExElH,OAAA;QAAKwG,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCzG,OAAA;UAAKwG,SAAS,EAAC,+FAA+F;UAAAC,QAAA,gBAE5GzG,OAAA;YAAKwG,SAAS,EAAG,mCACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACrB,sEAAsE,GACtE,oEACL,EAAE;YAAA6B,QAAA,eACDzG,OAAA;cAAKwG,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzG,OAAA;gBAAKwG,SAAS,EAAG,iFACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACrB,iDAAiD,GACjD,gDACL,EAAE;gBAAA6B,QAAA,eACDzG,OAAA;kBACE8H,GAAG,EAAE/G,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAGvF,IAAI,GAAGC,IAAK;kBAC7CyI,GAAG,EAAEhH,MAAM,CAAC6D,OAAQ;kBACpB4B,SAAS,EAAC;gBAA0B;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlH,OAAA;gBAAIwG,SAAS,EAAG,2CACdzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;gBAAA6B,QAAA,EACA1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,iBAAiB,GAAG;cAAe;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACLlH,OAAA;gBAAGwG,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAC/E1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACtB,+CAA+C,GAC/C;cAAgD;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlH,OAAA;YAAKwG,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBzG,OAAA;cAAKwG,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzDzG,OAAA;gBAAKwG,SAAS,EAAC,yKAAyK;gBAAAC,QAAA,gBACtLzG,OAAA;kBAAKwG,SAAS,EAAC;gBAAqI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3JlH,OAAA;kBAAKwG,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCzG,OAAA;oBAAKwG,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,GACnEuB,IAAI,CAACC,KAAK,CAAE,CAAC,EAAA9H,qBAAA,GAAAY,MAAM,CAACoD,cAAc,cAAAhE,qBAAA,uBAArBA,qBAAA,CAAuB2C,MAAM,KAAI,CAAC,IAAIrC,SAAS,CAACqC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlH,OAAA;oBAAKwG,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlH,OAAA;gBAAKwG,SAAS,EAAC,8KAA8K;gBAAAC,QAAA,gBAC3LzG,OAAA;kBAAKwG,SAAS,EAAC;gBAAwI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9JlH,OAAA;kBAAKwG,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCzG,OAAA;oBAAKwG,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,GACtE,EAAArG,sBAAA,GAAAW,MAAM,CAACoD,cAAc,cAAA/D,sBAAA,uBAArBA,sBAAA,CAAuB0C,MAAM,KAAI,CAAC,EAAC,GAAC,EAACrC,SAAS,CAACqC,MAAM;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNlH,OAAA;oBAAKwG,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,EAAC;kBAAO;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlH,OAAA;gBAAKwG,SAAS,EAAG,qGACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACrB,4EAA4E,GAC5E,yEACL,EAAE;gBAAA6B,QAAA,gBACDzG,OAAA;kBAAKwG,SAAS,EAAG,wGACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,oBAAoB,GAAG,kBACpD;gBAAiB;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBlH,OAAA;kBAAKwG,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCzG,OAAA;oBAAKwG,SAAS,EAAG,2CACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;oBAAA6B,QAAA,EACA1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG;kBAAO;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNlH,OAAA;oBAAKwG,SAAS,EAAG,8CACfzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,qBAAqB,GAAG,mBACrD,EAAE;oBAAA6B,QAAA,EACA1F,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,UAAU,GAAI,QAAOrE,QAAQ,CAACsE,YAAa;kBAAC;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlH,OAAA;cAAKwG,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBzG,OAAA;gBAAKwG,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDzG,OAAA;kBAAKwG,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BzG,OAAA;oBAAIwG,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/ElH,OAAA;oBAAGwG,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNlH,OAAA;kBAAKwG,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBzG,OAAA;oBAAKwG,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFzG,OAAA;sBACEwG,SAAS,EAAG,uFACVzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GACrB,6DAA6D,GAC7D,2DACL,EAAE;sBACHsD,KAAK,EAAE;wBAAEpG,KAAK,EAAG,GAAG,CAAC,EAAAzB,sBAAA,GAAAU,MAAM,CAACoD,cAAc,cAAA9D,sBAAA,uBAArBA,sBAAA,CAAuByC,MAAM,KAAI,CAAC,IAAIrC,SAAS,CAACqC,MAAM,GAAI,GAAI;sBAAG,CAAE;sBAAA2D,QAAA,eAExFzG,OAAA;wBAAKwG,SAAS,EAAC;sBAAgE;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlH,OAAA;oBAAKwG,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDzG,OAAA;sBAAMwG,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAE;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9DlH,OAAA;sBAAMwG,SAAS,EAAG,qCAChBzF,MAAM,CAAC6D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;sBAAA6B,QAAA,GACAuB,IAAI,CAACC,KAAK,CAAE,CAAC,EAAA3H,sBAAA,GAAAS,MAAM,CAACoD,cAAc,cAAA7D,sBAAA,uBAArBA,sBAAA,CAAuBwC,MAAM,KAAI,CAAC,IAAIrC,SAAS,CAACqC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;oBAAA;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPlH,OAAA;sBAAMwG,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlH,OAAA;cAAKwG,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7DzG,OAAA;gBACEwG,SAAS,EAAC,sPAAsP;gBAChQW,OAAO,EAAEA,CAAA,KAAM9F,OAAO,CAAC,QAAQ,CAAE;gBAAAoF,QAAA,gBAEjCzG,OAAA;kBAAKwG,SAAS,EAAC;gBAAkI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxJlH,OAAA;kBAAMwG,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAETlH,OAAA;gBACEwG,SAAS,EAAC,0PAA0P;gBACpQW,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACArG,kBAAkB,CAAC,CAAC,CAAC,CAAC;kBACtBF,wBAAwB,CAAC,CAAC,CAAC;kBAC3BW,cAAc,CAAChB,QAAQ,CAACmC,QAAQ,IAAI,CAAC,CAAC;kBACtCrB,OAAO,CAAC,cAAc,CAAC;kBACvBL,SAAS,CAAC,IAAI,CAAC;kBACfS,SAAS,CAAC,KAAK,CAAC;kBAChB;kBACA,IAAIC,UAAU,EAAE;oBACdmE,aAAa,CAACnE,UAAU,CAAC;oBACzBC,aAAa,CAAC,IAAI,CAAC;kBACrB;gBACF,CAAE;gBAAA8E,QAAA,gBAEFzG,OAAA;kBAAKwG,SAAS,EAAC;gBAAkI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxJlH,OAAA;kBAAMwG,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAETlH,OAAA;gBACEwG,SAAS,EAAC,sPAAsP;gBAChQW,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,iBAAiB,CAAE;gBAAAsF,QAAA,gBAE3CzG,OAAA;kBAAKwG,SAAS,EAAC;gBAAkI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxJlH,OAAA;kBAAMwG,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA9F,IAAI,KAAK,QAAQ,iBAChBpB,OAAA;MAAKwG,SAAS,EAAC,6EAA6E;MAAAC,QAAA,eAC1FzG,OAAA;QAAKwG,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErCzG,OAAA;UAAKwG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BzG,OAAA;YAAKwG,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAC/FzG,OAAA;cAAIwG,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAAC;YAEnH;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlH,OAAA;cAAGwG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA8B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlH,OAAA;UAAKwG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BhG,SAAS,CAAC0H,GAAG,CAAC,CAAC3E,QAAQ,EAAE4E,KAAK,KAAK;YAClC,MAAMzE,aAAa,GAAGH,QAAQ,CAACF,UAAU,KAAK,SAAS,GACnDE,QAAQ,CAACI,aAAa,GACtBJ,QAAQ,CAACG,aAAa;YAC1B,MAAMM,SAAS,GAAGN,aAAa,KAAK9C,eAAe,CAACuH,KAAK,CAAC;YAC1D,MAAMvE,UAAU,GAAGhD,eAAe,CAACuH,KAAK,CAAC;YAEzC,oBACEpI,OAAA;cAEEwG,SAAS,EAAC,oDAAoD;cAC9D0B,KAAK,EAAE;gBACLG,eAAe,EAAEpE,SAAS,GAAG,SAAS,GAAG,SAAS;gBAClDqE,WAAW,EAAErE,SAAS,GAAG,SAAS,GAAG;cACvC,CAAE;cAAAwC,QAAA,gBAGFzG,OAAA;gBAAKwG,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBzG,OAAA;kBAAKwG,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCzG,OAAA;oBAAKwG,SAAS,EAAC,iHAAiH;oBAAAC,QAAA,EAC7H2B,KAAK,GAAG;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNlH,OAAA;oBAAKwG,SAAS,EAAC,QAAQ;oBAAAC,QAAA,eACrBzG,OAAA;sBAAGwG,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAEjD,QAAQ,CAACC;oBAAI;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlH,OAAA;gBAAKwG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzG,OAAA;kBAAMwG,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3ElH,OAAA;kBAAMwG,SAAS,EAAG,eAAcvC,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;kBAAAwC,QAAA,EAC7EjD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC7BE,QAAQ,CAACkB,OAAO,IAAIb,UAAU,IAAIL,QAAQ,CAACkB,OAAO,CAACb,UAAU,CAAC,IAAKA,UAAU,IAAI,cAAc,GAChGA,UAAU,IAAI;gBAAc;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,EACNjD,SAAS,gBACRjE,OAAA;kBAAMwG,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAElElH,OAAA;kBAAMwG,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAChE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGL,CAACjD,SAAS,iBACTjE,OAAA;gBAAKwG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzG,OAAA;kBAAMwG,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9ElH,OAAA;kBAAMwG,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACzCjD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC7BE,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACkB,OAAO,CAAClB,QAAQ,CAACI,aAAa,CAAC,IAAKJ,QAAQ,CAACI,aAAa,GACvFJ,QAAQ,CAACG,aAAa,IAAIH,QAAQ,CAACI;gBAAc;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACPlH,OAAA;kBAAMwG,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CACN,EAGA,CAACjD,SAAS,iBACTjE,OAAA;gBAAKwG,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBzG,OAAA;kBACEwG,SAAS,EAAC,iKAAiK;kBAC3KW,OAAO,EAAEA,CAAA,KAAM;oBACboB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhF,QAAQ,CAACC,IAAI,CAAC;oBACvD2B,gBAAgB,CACd5B,QAAQ,CAACC,IAAI,EACbD,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC5BE,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACkB,OAAO,CAAClB,QAAQ,CAACI,aAAa,CAAC,IAAKJ,QAAQ,CAACI,aAAa,GACvFJ,QAAQ,CAACG,aAAa,IAAIH,QAAQ,CAACI,aAAc,EACtDJ,QAAQ,CAACF,UAAU,KAAK,SAAS,GAC5BE,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACkB,OAAO,CAACb,UAAU,CAAC,IAAKA,UAAU,IAAI,cAAc,GAClFA,UAAU,IAAI,cAAc,EAChCL,QAAQ,CAACiF,KACX,CAAC;kBACH,CAAE;kBAAAhC,QAAA,gBAEFzG,OAAA;oBAAAyG,QAAA,EAAM;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACflH,OAAA;oBAAAyG,QAAA,EAAM;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAGAlF,YAAY,CAACwB,QAAQ,CAACC,IAAI,CAAC,iBAC1BzD,OAAA;gBAAKwG,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,gBACzGzG,OAAA;kBAAKwG,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCzG,OAAA;oBAAMwG,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDlH,OAAA;oBAAIwG,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAAC,WACvC,EAAC2B,KAAK,GAAG,CAAC,EAAC,gBACtB;kBAAA;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAGL,CAAC1D,QAAQ,CAACiF,KAAK,IAAIjF,QAAQ,CAAC6B,QAAQ,kBACnCrF,OAAA;kBAAKwG,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,gBACpEzG,OAAA;oBAAKwG,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eACrCzG,OAAA;sBAAMwG,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eACNlH,OAAA;oBAAKwG,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClCzG,OAAA;sBACE8H,GAAG,EAAEtE,QAAQ,CAACiF,KAAK,IAAIjF,QAAQ,CAAC6B,QAAS;sBACzC0C,GAAG,EAAC,kBAAkB;sBACtBvB,SAAS,EAAC,6EAA6E;sBACvF0B,KAAK,EAAE;wBAAEQ,QAAQ,EAAE;sBAAQ;oBAAE;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAEDlH,OAAA;kBAAKwG,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAC9EzG,OAAA,CAACH,eAAe;oBAAC8I,IAAI,EAAE3G,YAAY,CAACwB,QAAQ,CAACC,IAAI;kBAAE;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,GAvGIkB,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwGP,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlH,OAAA;UAAKwG,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1EzG,OAAA;YACEwG,SAAS,EAAC,sPAAsP;YAChQW,OAAO,EAAEA,CAAA,KAAM9F,OAAO,CAAC,QAAQ,CAAE;YAAAoF,QAAA,gBAEjCzG,OAAA;cAAKwG,SAAS,EAAC;YAAkI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxJlH,OAAA;cAAMwG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAETlH,OAAA;YACEwG,SAAS,EAAC,sPAAsP;YAChQW,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,iBAAiB,CAAE;YAAAsF,QAAA,gBAE3CzG,OAAA;cAAKwG,SAAS,EAAC;YAAkI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxJlH,OAAA;cAAMwG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,GACJ,IAAI;AACV;AAAChH,EAAA,CA7oBQD,SAAS;EAAA,QAMDlB,SAAS,EACPH,WAAW,EACXE,WAAW,EAKXD,WAAW,EAEFW,aAAa;AAAA;AAAAoJ,EAAA,GAfhC3I,SAAS;AA+oBlB,eAAeA,SAAS;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}