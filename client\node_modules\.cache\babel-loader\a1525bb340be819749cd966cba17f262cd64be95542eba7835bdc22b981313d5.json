{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\"\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Render MCQ Question\n  const renderMCQ = () => {\n    if (!question.options) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-500 text-center p-4 w-full\",\n        children: \"No options available for this question.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 w-full max-w-none\",\n      style: {\n        width: '100%'\n      },\n      children: Object.entries(question.options).map(([key, value]) => {\n        const optionKey = String(key).trim();\n        const optionValue = String(value).trim();\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.1 * parseInt(optionKey.charCodeAt(0) - 65),\n            type: \"spring\",\n            stiffness: 120\n          },\n          className: \"w-full\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            onClick: () => handleAnswerSelect(optionKey),\n            whileHover: {\n              scale: 1.02\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            className: `w-full p-5 rounded-2xl text-left transition-all duration-300 flex items-center space-x-5 border-2 ${currentAnswer === optionKey ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white border-blue-600 shadow-xl transform scale-[1.02]' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 shadow-md hover:shadow-lg'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-14 h-14 rounded-full flex items-center justify-center font-bold text-xl flex-shrink-0 transition-all duration-300 ${currentAnswer === optionKey ? 'bg-white text-blue-600 shadow-lg' : 'bg-blue-100 text-blue-700 border-2 border-blue-200'}`,\n              children: optionKey\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-lg font-medium leading-relaxed flex-1 ${currentAnswer === optionKey ? 'text-white' : 'text-gray-800'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this), currentAnswer === optionKey && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0,\n                rotate: -180\n              },\n              animate: {\n                scale: 1,\n                rotate: 0\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 200,\n                damping: 15\n              },\n              className: \"w-8 h-8 text-white flex-shrink-0 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render Fill-in-the-blank Question\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border border-blue-200 shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-blue-800 font-bold mb-6 text-xl\",\n        children: \"Your Answer:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: currentAnswer,\n          onChange: e => handleAnswerSelect(e.target.value),\n          placeholder: \"Type your answer here...\",\n          className: \"w-full p-6 border-2 border-blue-300 rounded-xl text-lg focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 bg-white shadow-lg font-medium transition-all duration-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute right-4 top-1/2 transform -translate-y-1/2\",\n          children: currentAnswer && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 text-white\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), currentAnswer && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20,\n        scale: 0.95\n      },\n      animate: {\n        opacity: 1,\n        y: 0,\n        scale: 1\n      },\n      transition: {\n        type: \"spring\",\n        stiffness: 200,\n        damping: 20\n      },\n      className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-800 font-bold text-lg\",\n            children: \"Answer Recorded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-700 font-medium\",\n            children: [\"\\\"\", currentAnswer, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n\n  // Render Image-based Question\n  const renderImageQuestion = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: question.imageUrl,\n        alt: \"Question diagram\",\n        className: \"max-w-full max-h-96 rounded-lg shadow-lg border border-gray-200 mx-auto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this), question.options ? renderMCQ() : renderFillBlank()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 pb-24\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg font-semibold text-gray-800\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `inline-block px-4 py-2 rounded-lg font-mono font-bold text-lg ${timeLeft <= 60 ? 'bg-red-100 text-red-700 border border-red-200' : timeLeft <= 300 ? 'bg-orange-100 text-orange-700 border border-orange-200' : 'bg-green-100 text-green-700 border border-green-200'}`,\n            children: formatTime(timeLeft)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"h-full bg-blue-600 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progressPercentage}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex max-w-6xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 bg-blue-600 min-h-screen flex flex-col items-center py-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-500 rounded-lg cursor-pointer hover:bg-blue-400 transition-colors\",\n          title: \"Dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-700 rounded-lg cursor-pointer hover:bg-blue-600 transition-colors\",\n          title: \"Quiz\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 px-8 py-8 pb-16 w-full\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3,\n              ease: \"easeOut\"\n            },\n            className: \"w-full\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-8 w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-8 w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-3xl font-bold text-gray-900 leading-relaxed mb-4 w-full\",\n                  children: question.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6 w-full\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.image,\n                    alt: \"Question\",\n                    className: \"max-w-full max-h-80 rounded-lg border border-gray-200 mx-auto\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-20 w-full\",\n                children: [question.answerType === \"Options\" && renderMCQ(), (question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank(), question.imageUrl && renderImageQuestion()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, questionIndex, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl border border-gray-200 p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(motion.button, {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            whileHover: questionIndex !== 0 ? {\n              scale: 1.05,\n              y: -2\n            } : {},\n            whileTap: questionIndex !== 0 ? {\n              scale: 0.95\n            } : {},\n            className: `flex items-center space-x-3 px-8 py-4 rounded-xl font-bold text-base transition-all duration-300 ${questionIndex === 0 ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-lg hover:shadow-xl'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 px-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500 font-medium\",\n              children: [questionIndex + 1, \" / \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            onClick: onNext,\n            disabled: !isAnswered,\n            whileHover: isAnswered ? {\n              scale: 1.05,\n              y: -2\n            } : {},\n            whileTap: isAnswered ? {\n              scale: 0.95\n            } : {},\n            className: `flex items-center space-x-3 px-8 py-4 rounded-xl font-bold text-base transition-all duration-300 ${!isAnswered ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400' : questionIndex === totalQuestions - 1 ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl' : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next Question'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "username", "onNext", "onPrevious", "examTitle", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "handleAnswerSelect", "answer", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "progressPercentage", "renderMCQ", "options", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "Object", "entries", "map", "key", "value", "optionKey", "String", "trim", "optionValue", "div", "initial", "opacity", "y", "animate", "transition", "delay", "parseInt", "charCodeAt", "type", "stiffness", "button", "onClick", "whileHover", "scale", "whileTap", "rotate", "damping", "fill", "viewBox", "fillRule", "d", "clipRule", "renderFillBlank", "onChange", "e", "target", "placeholder", "renderImageQuestion", "imageUrl", "src", "alt", "duration", "title", "mode", "exit", "ease", "name", "image", "answerType", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Render MCQ Question\n  const renderMCQ = () => {\n    if (!question.options) {\n      return <div className=\"text-red-500 text-center p-4 w-full\">No options available for this question.</div>;\n    }\n\n    return (\n      <div className=\"space-y-4 w-full max-w-none\" style={{width: '100%'}}>\n        {Object.entries(question.options).map(([key, value]) => {\n          const optionKey = String(key).trim();\n          const optionValue = String(value).trim();\n\n          return (\n            <motion.div\n              key={optionKey}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{\n                delay: 0.1 * parseInt(optionKey.charCodeAt(0) - 65),\n                type: \"spring\",\n                stiffness: 120\n              }}\n              className=\"w-full\"\n            >\n              <motion.button\n                onClick={() => handleAnswerSelect(optionKey)}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                className={`w-full p-5 rounded-2xl text-left transition-all duration-300 flex items-center space-x-5 border-2 ${\n                  currentAnswer === optionKey\n                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white border-blue-600 shadow-xl transform scale-[1.02]'\n                    : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 shadow-md hover:shadow-lg'\n                }`}\n              >\n                {/* Letter badge */}\n                <div className={`w-14 h-14 rounded-full flex items-center justify-center font-bold text-xl flex-shrink-0 transition-all duration-300 ${\n                  currentAnswer === optionKey\n                    ? 'bg-white text-blue-600 shadow-lg'\n                    : 'bg-blue-100 text-blue-700 border-2 border-blue-200'\n                }`}>\n                  {optionKey}\n                </div>\n\n                {/* Answer text */}\n                <span className={`text-lg font-medium leading-relaxed flex-1 ${\n                  currentAnswer === optionKey ? 'text-white' : 'text-gray-800'\n                }`}>\n                  {optionValue}\n                </span>\n\n                {/* Check icon for selected */}\n                {currentAnswer === optionKey && (\n                  <motion.div\n                    initial={{ scale: 0, rotate: -180 }}\n                    animate={{ scale: 1, rotate: 0 }}\n                    transition={{ type: \"spring\", stiffness: 200, damping: 15 }}\n                    className=\"w-8 h-8 text-white flex-shrink-0 bg-white bg-opacity-20 rounded-full flex items-center justify-center\"\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </motion.div>\n                )}\n              </motion.button>\n            </motion.div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render Fill-in-the-blank Question\n  const renderFillBlank = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border border-blue-200 shadow-lg\">\n        <label className=\"block text-blue-800 font-bold mb-6 text-xl\">\n          Your Answer:\n        </label>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={currentAnswer}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"w-full p-6 border-2 border-blue-300 rounded-xl text-lg focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 bg-white shadow-lg font-medium transition-all duration-300\"\n          />\n          <div className=\"absolute right-4 top-1/2 transform -translate-y-1/2\">\n            {currentAnswer && (\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\"\n              >\n                <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n              </motion.div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {currentAnswer && (\n        <motion.div\n          initial={{ opacity: 0, y: 20, scale: 0.95 }}\n          animate={{ opacity: 1, y: 0, scale: 1 }}\n          transition={{ type: \"spring\", stiffness: 200, damping: 20 }}\n          className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200 shadow-lg\"\n        >\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\">\n              <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div>\n              <p className=\"text-green-800 font-bold text-lg\">Answer Recorded</p>\n              <p className=\"text-green-700 font-medium\">\"{currentAnswer}\"</p>\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n\n  // Render Image-based Question\n  const renderImageQuestion = () => (\n    <div className=\"space-y-6\">\n      {question.imageUrl && (\n        <div className=\"text-center mb-6\">\n          <img\n            src={question.imageUrl}\n            alt=\"Question diagram\"\n            className=\"max-w-full max-h-96 rounded-lg shadow-lg border border-gray-200 mx-auto\"\n          />\n        </div>\n      )}\n\n      {question.options ? renderMCQ() : renderFillBlank()}\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 pb-24\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          {/* Header Content */}\n          <div className=\"text-center mb-4\">\n            {/* Question counter */}\n            <div className=\"mb-3\">\n              <span className=\"text-lg font-semibold text-gray-800\">Question {questionIndex + 1} of {totalQuestions}</span>\n            </div>\n\n            {/* Timer */}\n            <div className={`inline-block px-4 py-2 rounded-lg font-mono font-bold text-lg ${\n              timeLeft <= 60 ? 'bg-red-100 text-red-700 border border-red-200' :\n              timeLeft <= 300 ? 'bg-orange-100 text-orange-700 border border-orange-200' :\n              'bg-green-100 text-green-700 border border-green-200'\n            }`}>\n              {formatTime(timeLeft)}\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mb-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"h-full bg-blue-600 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progressPercentage}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Sidebar and Main Content */}\n      <div className=\"flex max-w-6xl mx-auto\">\n        {/* Compact Sidebar */}\n        <div className=\"w-16 bg-blue-600 min-h-screen flex flex-col items-center py-6 space-y-4\">\n          {/* Navigation Icons */}\n          <div className=\"p-2 bg-blue-500 rounded-lg cursor-pointer hover:bg-blue-400 transition-colors\" title=\"Dashboard\">\n            <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\" />\n            </svg>\n          </div>\n\n          <div className=\"p-2 bg-blue-700 rounded-lg cursor-pointer hover:bg-blue-600 transition-colors\" title=\"Quiz\">\n            <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n        </div>\n\n        {/* Main Question Panel */}\n        <div className=\"flex-1 px-8 py-8 pb-16 w-full\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={questionIndex}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3, ease: \"easeOut\" }}\n              className=\"w-full\"\n            >\n              {/* Question Card */}\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8 w-full\">\n                {/* Question Header */}\n                <div className=\"mb-8 w-full\">\n                  <h2 className=\"text-3xl font-bold text-gray-900 leading-relaxed mb-4 w-full\">\n                    {question.name}\n                  </h2>\n                  {question.image && (\n                    <div className=\"mb-6 w-full\">\n                      <img\n                        src={question.image}\n                        alt=\"Question\"\n                        className=\"max-w-full max-h-80 rounded-lg border border-gray-200 mx-auto\"\n                      />\n                    </div>\n                  )}\n                </div>\n\n                {/* Question Options */}\n                <div className=\"mb-20 w-full\">\n                  {question.answerType === \"Options\" && renderMCQ()}\n                  {(question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank()}\n                  {question.imageUrl && renderImageQuestion()}\n                </div>\n              </div>\n            </motion.div>\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Bottom Navigation - Modern Floating Design */}\n      <div className=\"fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50\">\n        <div className=\"bg-white rounded-2xl shadow-2xl border border-gray-200 p-4\">\n          <div className=\"flex items-center justify-center gap-4\">\n            {/* Previous Button */}\n            <motion.button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              whileHover={questionIndex !== 0 ? { scale: 1.05, y: -2 } : {}}\n              whileTap={questionIndex !== 0 ? { scale: 0.95 } : {}}\n              className={`flex items-center space-x-3 px-8 py-4 rounded-xl font-bold text-base transition-all duration-300 ${\n                questionIndex === 0\n                  ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400'\n                  : 'bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 shadow-lg hover:shadow-xl'\n              }`}\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n              <span>Previous</span>\n            </motion.button>\n\n            {/* Question Navigator */}\n            <div className=\"flex items-center space-x-2 px-4\">\n              <div className=\"text-sm text-gray-500 font-medium\">\n                {questionIndex + 1} / {totalQuestions}\n              </div>\n            </div>\n\n            {/* Next/Submit Button */}\n            <motion.button\n              onClick={onNext}\n              disabled={!isAnswered}\n              whileHover={isAnswered ? { scale: 1.05, y: -2 } : {}}\n              whileTap={isAnswered ? { scale: 0.95 } : {}}\n              className={`flex items-center space-x-3 px-8 py-4 rounded-xl font-bold text-base transition-all duration-300 ${\n                !isAnswered\n                  ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400'\n                  : questionIndex === totalQuestions - 1\n                  ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl'\n                  : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl'\n              }`}\n            >\n              <span>\n                {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next Question'}\n              </span>\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                {questionIndex === totalQuestions - 1 ? (\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                ) : (\n                  <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                )}\n              </svg>\n            </motion.button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,QAAQ,GAAG,SAAS;EACpBC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAACU,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdkB,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrCJ,gBAAgB,CAACI,MAAM,CAAC;IACxBF,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACY,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACxB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,MAAMwB,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAAC1B,QAAQ,CAAC2B,OAAO,EAAE;MACrB,oBAAO7B,OAAA;QAAK8B,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAC3G;IAEA,oBACEnC,OAAA;MAAK8B,SAAS,EAAC,6BAA6B;MAACM,KAAK,EAAE;QAACC,KAAK,EAAE;MAAM,CAAE;MAAAN,QAAA,EACjEO,MAAM,CAACC,OAAO,CAACrC,QAAQ,CAAC2B,OAAO,CAAC,CAACW,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QACtD,MAAMC,SAAS,GAAGC,MAAM,CAACH,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC;QACpC,MAAMC,WAAW,GAAGF,MAAM,CAACF,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;QAExC,oBACE7C,OAAA,CAACH,MAAM,CAACkD,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YACVC,KAAK,EAAE,GAAG,GAAGC,QAAQ,CAACX,SAAS,CAACY,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACnDC,IAAI,EAAE,QAAQ;YACdC,SAAS,EAAE;UACb,CAAE;UACF3B,SAAS,EAAC,QAAQ;UAAAC,QAAA,eAElB/B,OAAA,CAACH,MAAM,CAAC6D,MAAM;YACZC,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC0B,SAAS,CAAE;YAC7CiB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1B/B,SAAS,EAAG,qGACVjB,aAAa,KAAK8B,SAAS,GACvB,wGAAwG,GACxG,2FACL,EAAE;YAAAZ,QAAA,gBAGH/B,OAAA;cAAK8B,SAAS,EAAG,uHACfjB,aAAa,KAAK8B,SAAS,GACvB,kCAAkC,GAClC,oDACL,EAAE;cAAAZ,QAAA,EACAY;YAAS;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAGNnC,OAAA;cAAM8B,SAAS,EAAG,8CAChBjB,aAAa,KAAK8B,SAAS,GAAG,YAAY,GAAG,eAC9C,EAAE;cAAAZ,QAAA,EACAe;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAGNtB,aAAa,KAAK8B,SAAS,iBAC1B3C,OAAA,CAACH,MAAM,CAACkD,GAAG;cACTC,OAAO,EAAE;gBAAEa,KAAK,EAAE,CAAC;gBAAEE,MAAM,EAAE,CAAC;cAAI,CAAE;cACpCZ,OAAO,EAAE;gBAAEU,KAAK,EAAE,CAAC;gBAAEE,MAAM,EAAE;cAAE,CAAE;cACjCX,UAAU,EAAE;gBAAEI,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEO,OAAO,EAAE;cAAG,CAAE;cAC5DlC,SAAS,EAAC,uGAAuG;cAAAC,QAAA,eAEjH/B,OAAA;gBAAK8B,SAAS,EAAC,SAAS;gBAACmC,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAnC,QAAA,eAC9D/B,OAAA;kBAAMmE,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,oHAAoH;kBAACC,QAAQ,EAAC;gBAAS;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY;QAAC,GAjDXQ,SAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkDJ,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMmC,eAAe,GAAGA,CAAA,kBACtBtE,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/B,OAAA;MAAK8B,SAAS,EAAC,6FAA6F;MAAAC,QAAA,gBAC1G/B,OAAA;QAAO8B,SAAS,EAAC,4CAA4C;QAAAC,QAAA,EAAC;MAE9D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRnC,OAAA;QAAK8B,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvB/B,OAAA;UACEwD,IAAI,EAAC,MAAM;UACXd,KAAK,EAAE7B,aAAc;UACrB0D,QAAQ,EAAGC,CAAC,IAAKvD,kBAAkB,CAACuD,CAAC,CAACC,MAAM,CAAC/B,KAAK,CAAE;UACpDgC,WAAW,EAAC,0BAA0B;UACtC5C,SAAS,EAAC;QAA6L;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxM,CAAC,eACFnC,OAAA;UAAK8B,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EACjElB,aAAa,iBACZb,OAAA,CAACH,MAAM,CAACkD,GAAG;YACTC,OAAO,EAAE;cAAEa,KAAK,EAAE;YAAE,CAAE;YACtBV,OAAO,EAAE;cAAEU,KAAK,EAAE;YAAE,CAAE;YACtB/B,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eAE9E/B,OAAA;cAAK8B,SAAS,EAAC,oBAAoB;cAACmC,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAnC,QAAA,eACzE/B,OAAA;gBAAMmE,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELtB,aAAa,iBACZb,OAAA,CAACH,MAAM,CAACkD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,EAAE;QAAEW,KAAK,EAAE;MAAK,CAAE;MAC5CV,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEW,KAAK,EAAE;MAAE,CAAE;MACxCT,UAAU,EAAE;QAAEI,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAE,GAAG;QAAEO,OAAO,EAAE;MAAG,CAAE;MAC5DlC,SAAS,EAAC,gGAAgG;MAAAC,QAAA,eAE1G/B,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C/B,OAAA;UAAK8B,SAAS,EAAC,kHAAkH;UAAAC,QAAA,eAC/H/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAACmC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAnC,QAAA,eACzE/B,OAAA;cAAMmE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnC,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAG8B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnEnC,OAAA;YAAG8B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GAAC,IAAC,EAAClB,aAAa,EAAC,IAAC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAMwC,mBAAmB,GAAGA,CAAA,kBAC1B3E,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,GACvB7B,QAAQ,CAAC0E,QAAQ,iBAChB5E,OAAA;MAAK8B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B/B,OAAA;QACE6E,GAAG,EAAE3E,QAAQ,CAAC0E,QAAS;QACvBE,GAAG,EAAC,kBAAkB;QACtBhD,SAAS,EAAC;MAAyE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAEAjC,QAAQ,CAAC2B,OAAO,GAAGD,SAAS,CAAC,CAAC,GAAG0C,eAAe,CAAC,CAAC;EAAA;IAAAtC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CACN;EAED,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5C/B,OAAA;MAAK8B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D/B,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1C/B,OAAA;UAAK8B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAE/B/B,OAAA;YAAK8B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB/B,OAAA;cAAM8B,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,WAAS,EAAC5B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eAGNnC,OAAA;YAAK8B,SAAS,EAAG,iEACfvB,QAAQ,IAAI,EAAE,GAAG,+CAA+C,GAChEA,QAAQ,IAAI,GAAG,GAAG,wDAAwD,GAC1E,qDACD,EAAE;YAAAwB,QAAA,EACAZ,UAAU,CAACZ,QAAQ;UAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB/B,OAAA;YAAK8B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClD/B,OAAA,CAACH,MAAM,CAACkD,GAAG;cACTjB,SAAS,EAAC,iCAAiC;cAC3CkB,OAAO,EAAE;gBAAEX,KAAK,EAAE;cAAE,CAAE;cACtBc,OAAO,EAAE;gBAAEd,KAAK,EAAG,GAAEV,kBAAmB;cAAG,CAAE;cAC7CyB,UAAU,EAAE;gBAAE2B,QAAQ,EAAE;cAAI;YAAE;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC/B,OAAA;QAAK8B,SAAS,EAAC,yEAAyE;QAAAC,QAAA,gBAEtF/B,OAAA;UAAK8B,SAAS,EAAC,+EAA+E;UAACkD,KAAK,EAAC,WAAW;UAAAjD,QAAA,eAC9G/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAACmC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAnC,QAAA,eACzE/B,OAAA;cAAMoE,CAAC,EAAC;YAAkM;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnC,OAAA;UAAK8B,SAAS,EAAC,+EAA+E;UAACkD,KAAK,EAAC,MAAM;UAAAjD,QAAA,eACzG/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAACmC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAnC,QAAA,eACzE/B,OAAA;cAAMmE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,+IAA+I;cAACC,QAAQ,EAAC;YAAS;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7L;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK8B,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5C/B,OAAA,CAACF,eAAe;UAACmF,IAAI,EAAC,MAAM;UAAAlD,QAAA,eAC1B/B,OAAA,CAACH,MAAM,CAACkD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BgC,IAAI,EAAE;cAAEjC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BE,UAAU,EAAE;cAAE2B,QAAQ,EAAE,GAAG;cAAEI,IAAI,EAAE;YAAU,CAAE;YAC/CrD,SAAS,EAAC,QAAQ;YAAAC,QAAA,eAGlB/B,OAAA;cAAK8B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBAE/E/B,OAAA;gBAAK8B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B/B,OAAA;kBAAI8B,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EACzE7B,QAAQ,CAACkF;gBAAI;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,EACJjC,QAAQ,CAACmF,KAAK,iBACbrF,OAAA;kBAAK8B,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1B/B,OAAA;oBACE6E,GAAG,EAAE3E,QAAQ,CAACmF,KAAM;oBACpBP,GAAG,EAAC,UAAU;oBACdhD,SAAS,EAAC;kBAA+D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNnC,OAAA;gBAAK8B,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC1B7B,QAAQ,CAACoF,UAAU,KAAK,SAAS,IAAI1D,SAAS,CAAC,CAAC,EAChD,CAAC1B,QAAQ,CAACoF,UAAU,KAAK,WAAW,IAAIpF,QAAQ,CAACoF,UAAU,KAAK,mBAAmB,KAAKhB,eAAe,CAAC,CAAC,EACzGpE,QAAQ,CAAC0E,QAAQ,IAAID,mBAAmB,CAAC,CAAC;cAAA;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA/BDhC,aAAa;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtE/B,OAAA;QAAK8B,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzE/B,OAAA;UAAK8B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD/B,OAAA,CAACH,MAAM,CAAC6D,MAAM;YACZC,OAAO,EAAEjD,UAAW;YACpB6E,QAAQ,EAAEpF,aAAa,KAAK,CAAE;YAC9ByD,UAAU,EAAEzD,aAAa,KAAK,CAAC,GAAG;cAAE0D,KAAK,EAAE,IAAI;cAAEX,CAAC,EAAE,CAAC;YAAE,CAAC,GAAG,CAAC,CAAE;YAC9DY,QAAQ,EAAE3D,aAAa,KAAK,CAAC,GAAG;cAAE0D,KAAK,EAAE;YAAK,CAAC,GAAG,CAAC,CAAE;YACrD/B,SAAS,EAAG,oGACV3B,aAAa,KAAK,CAAC,GACf,yDAAyD,GACzD,uHACL,EAAE;YAAA4B,QAAA,gBAEH/B,OAAA;cAAK8B,SAAS,EAAC,SAAS;cAACmC,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAnC,QAAA,eAC9D/B,OAAA;gBAAMmE,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,mHAAmH;gBAACC,QAAQ,EAAC;cAAS;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjK,CAAC,eACNnC,OAAA;cAAA+B,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGhBnC,OAAA;YAAK8B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,eAC/C/B,OAAA;cAAK8B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAC/C5B,aAAa,GAAG,CAAC,EAAC,KAAG,EAACC,cAAc;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA,CAACH,MAAM,CAAC6D,MAAM;YACZC,OAAO,EAAElD,MAAO;YAChB8E,QAAQ,EAAE,CAACxE,UAAW;YACtB6C,UAAU,EAAE7C,UAAU,GAAG;cAAE8C,KAAK,EAAE,IAAI;cAAEX,CAAC,EAAE,CAAC;YAAE,CAAC,GAAG,CAAC,CAAE;YACrDY,QAAQ,EAAE/C,UAAU,GAAG;cAAE8C,KAAK,EAAE;YAAK,CAAC,GAAG,CAAC,CAAE;YAC5C/B,SAAS,EAAG,oGACV,CAACf,UAAU,GACP,yDAAyD,GACzDZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GACpC,2HAA2H,GAC3H,uHACL,EAAE;YAAA2B,QAAA,gBAEH/B,OAAA;cAAA+B,QAAA,EACG5B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;YAAe;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACPnC,OAAA;cAAK8B,SAAS,EAAC,SAAS;cAACmC,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAnC,QAAA,EAC7D5B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCJ,OAAA;gBAAMmE,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErKnC,OAAA;gBAAMmE,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACrK;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA1UIX,YAAY;AAAAuF,EAAA,GAAZvF,YAAY;AA4UlB,eAAeA,YAAY;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}