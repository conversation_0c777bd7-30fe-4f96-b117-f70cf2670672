import React from "react";
import { useNavigate } from "react-router-dom";

function Instructions({ examData, setView, startTimer }) {
  const navigate = useNavigate();

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white">Instructions</h1>
          </div>
        </div>
      </div>

      <div className="max-w-3xl mx-auto px-4 py-12">
        {/* Main Content Card */}
        <div className="bg-white rounded-3xl shadow-2xl border-0 p-10 mb-8 backdrop-blur-sm bg-white/95">
          <div className="text-center mb-10">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4">
              {examData.category || "Subject"}
            </h1>
            <h2 className="text-2xl font-semibold text-gray-700 mb-6">
              {examData.name}
            </h2>
            <p className="text-xl text-gray-600 font-medium">Ready to demonstrate your knowledge?</p>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
            <div className="text-center p-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300">
              <div className="text-4xl font-bold text-white mb-3">{formatTime(examData.duration)}</div>
              <div className="text-lg font-semibold text-blue-100">Duration</div>
            </div>
            <div className="text-center p-8 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300">
              <div className="text-4xl font-bold text-white mb-3">{examData.totalMarks}</div>
              <div className="text-lg font-semibold text-green-100">Total Marks</div>
            </div>
            <div className="text-center p-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300">
              <div className="text-4xl font-bold text-white mb-3">{examData.passingMarks}</div>
              <div className="text-lg font-semibold text-orange-100">Pass Marks</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center max-w-2xl mx-auto gap-8">
            {/* Cancel Button Box - Left */}
            <div className="bg-gray-50 rounded-3xl p-6 shadow-lg border border-gray-200">
              <button
                className="px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-2xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-xl"
                onClick={() => navigate('/user/quiz')}
              >
                Cancel
              </button>
            </div>

            {/* Start Quiz Button Box - Right */}
            <div className="bg-blue-50 rounded-3xl p-6 shadow-lg border border-blue-200">
              <button
                className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-bold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-xl"
                onClick={() => {
                  startTimer();
                  setView("questions");
                }}
              >
                Start Quiz
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Instructions;