.layout {
  padding: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  background-color: var(--primary);
  padding: 20px 10px;
  border-radius: 5px;
  height: 100vh;
  /* display: flex; */
  /* justify-content: center; */
  /* align-items: center; */
  overflow-y: auto;
  box-sizing: border-box;
  scrollbar-width: thin;
}

.mobile-sidebar {
  width: 15vw;
  padding: 5px;
}

.menu {
  height: fit-content;
}

.mobile-sidebar .menu-item {
  padding: 10px 5px;
  margin-bottom: 15px;
  justify-content: center;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 17px 20px;
  margin: 5px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  gap: 15px;
  color: white !important;
}

.active-menu-item {
  border: 2px solid white;
  border-radius: 5px;
}

.body {
  overflow: hidden;
  flex: 1;
}

.collapsed-body {
  /* margin-left: 102px; */
}

.mobile-collapsed-body {
  /* margin-left: 20vw; */
}

.no-collapse-body {
  /* margin-left: 230px; */
}

.content {
  overflow-y: auto;
}

.header {
  background-color: var(--primary);
  color: white !important;
  padding: 15px;
  border-radius: 5px;
  align-items: center !important;
  box-sizing: border-box;
  width: 100%;
}

/* Quiz Fullscreen Mode - Instructions, Questions, and Results */
.quiz-fullscreen .layout {
  display: block !important;
}

.quiz-fullscreen .sidebar {
  display: none !important;
}

.quiz-fullscreen .body {
  width: 100vw !important;
  margin-left: 0 !important;
}

.quiz-fullscreen .header {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 10px 20px !important;
  background-color: var(--primary) !important;
  border-radius: 0 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  height: 60px !important;
  box-sizing: border-box !important;
}

.quiz-fullscreen .header > div:first-child,
.quiz-fullscreen .header > div:last-child {
  display: none !important;
}

.quiz-fullscreen .header .flex.items-center.gap-1 {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.quiz-fullscreen .content {
  padding: 0 !important;
  height: calc(100vh - 60px) !important;
  margin-top: 60px !important;
  overflow-y: auto;
}