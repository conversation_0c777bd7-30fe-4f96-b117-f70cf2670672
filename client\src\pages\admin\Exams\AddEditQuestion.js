import { Form, message, Modal } from "antd";
import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { addQuestionToExam, editQuestionById } from "../../../apicalls/exams";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";

function AddEditQuestion({
  showAddEditQuestionModal,
  setShowAddEditQuestionModal,
  refreshData,
  examId,
  selectedQuestion,
  setSelectedQuestion,
}) {
  const dispatch = useDispatch();
  const [questionType, setQuestionType] = useState(() => {
    if (selectedQuestion?.type) {
      return selectedQuestion.type;
    }
    if (selectedQuestion?.answerType === "Options") {
      return selectedQuestion?.image || selectedQuestion?.imageUrl ? "image" : "mcq";
    }
    if (selectedQuestion?.answerType === "Fill in the Blank" || selectedQuestion?.answerType === "Free Text") {
      return "fill";
    }
    // Default for AI-generated questions
    if (selectedQuestion?.isAIGenerated) {
      if (selectedQuestion?.questionType === "picture_based") return "image";
      if (selectedQuestion?.questionType === "fill_blank") return "fill";
      return "mcq";
    }
    return "mcq";
  });
  const [imageFile, setImageFile] = useState(null);

  const onFinish = async (values) => {
    try {
      dispatch(ShowLoading());

      // Prepare form data for file upload
      const formData = new FormData();

      // Append question details
      formData.append('name', values.name);
      formData.append('type', questionType);
      formData.append('exam', examId);
      formData.append('topic', values.topic || 'General');
      formData.append('classLevel', values.classLevel || 'General');

      // Set legacy answerType for backward compatibility
      if (questionType === "mcq") {
        formData.append('answerType', 'Options');
      } else if (questionType === "fill") {
        formData.append('answerType', 'Fill in the Blank');
      } else if (questionType === "image") {
        formData.append('answerType', 'Options'); // Image questions can have MCQ options
      }

      // Append correct answer - unified for all types
      formData.append('correctAnswer', values.correctAnswer);

      // Append options for MCQ and image questions
      if (questionType === "mcq" || questionType === "image") {
        formData.append('options[A]', values.A);
        formData.append('options[B]', values.B);
        formData.append('options[C]', values.C);
        formData.append('options[D]', values.D);
        // Legacy field for backward compatibility
        formData.append('correctOption', values.correctAnswer);
      }

      // Append image if selected
      if (imageFile) {
        formData.append("image", imageFile);
      } else if (selectedQuestion?.image) {
        formData.append("image", selectedQuestion.image); // Retain existing image if editing
      }

      let response;
      if (selectedQuestion) {
        // For editing, include question ID
        formData.append('questionId', selectedQuestion._id);
        response = await editQuestionById(formData);
      } else {
        response = await addQuestionToExam(formData);
      }

      if (response.success) {
        message.success(response.message);
        refreshData();
        setShowAddEditQuestionModal(false);
        setImageFile(null);
      } else {
        message.error(response.message);
      }

      setSelectedQuestion(null);
      dispatch(HideLoading());
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setImageFile(file || null);
  };

  return (
    <Modal
      title={selectedQuestion ? "Edit Question" : "Add Question"}
      open={showAddEditQuestionModal}
      footer={false}
      onCancel={() => {
        setShowAddEditQuestionModal(false);
        setSelectedQuestion(null);
        setImageFile(null);
      }}
    >
      <Form
        onFinish={onFinish}
        layout="vertical"
        initialValues={{
          name: selectedQuestion?.name,
          correctAnswer: selectedQuestion?.correctAnswer || selectedQuestion?.correctOption,
          topic: selectedQuestion?.topic || 'General',
          classLevel: selectedQuestion?.classLevel || 'General',
          A: selectedQuestion?.options?.A || selectedQuestion?.options?.a || '',
          B: selectedQuestion?.options?.B || selectedQuestion?.options?.b || '',
          C: selectedQuestion?.options?.C || selectedQuestion?.options?.c || '',
          D: selectedQuestion?.options?.D || selectedQuestion?.options?.d || '',
        }}
      >
        <Form.Item name="name" label="Question">
          <input type="text" />
        </Form.Item>

        {/* Question Type Selection */}
        <Form.Item name="questionType" label="Question Type">
          <select
            value={questionType}
            onChange={(e) => setQuestionType(e.target.value)}
          >
            <option value="mcq">Multiple Choice (MCQ)</option>
            <option value="fill">Fill in the Blank</option>
            <option value="image">Image-based Question</option>
          </select>
        </Form.Item>

        {/* Additional Fields */}
        <div className="flex gap-3">
          <Form.Item name="topic" label="Topic">
            <input type="text" placeholder="e.g., Mathematics, Science" />
          </Form.Item>
          <Form.Item name="classLevel" label="Class Level">
            <input type="text" placeholder="e.g., Primary 1, Secondary 2" />
          </Form.Item>
        </div>

        {/* Image Upload for Image-based Questions */}
        {questionType === "image" && (
          <Form.Item name="image" label="Question Image">
            <input
              type="file"
              accept="image/*"
              onChange={(e) => setImageFile(e.target.files[0])}
            />
            <small>Upload an image for this question</small>
          </Form.Item>
        )}

        {/* Correct Answer - Universal Field */}
        <Form.Item name="correctAnswer" label="Correct Answer">
          <input
            type="text"
            placeholder={
              questionType === "mcq" || questionType === "image"
                ? "Enter the correct option (A, B, C, or D)"
                : "Enter the correct answer"
            }
          />
        </Form.Item>

        {/* Options for MCQ and Image Questions */}
        {(questionType === "mcq" || questionType === "image") && (
          <>
            <div className="flex gap-3">
              <Form.Item name="A" label="Option A">
                <input type="text" />
              </Form.Item>
              <Form.Item name="B" label="Option B">
                <input type="text" />
              </Form.Item>
            </div>
            <div className="flex gap-3">
              <Form.Item name="C" label="Option C">
                <input type="text" />
              </Form.Item>
              <Form.Item name="D" label="Option D">
                <input type="text" />
              </Form.Item>
            </div>
          </>
        )}

        {/* Image Upload */}
        <Form.Item name="image" label="Question Image (Optional)">
          <input 
            type="file" 
            accept="image/*" 
            onChange={handleImageChange}
          />
          {imageFile && (
            <div className="mt-2 text-sm text-gray-600">
              Selected file: {imageFile.name}
            </div>
          )}
          {(selectedQuestion?.image || selectedQuestion?.imageUrl) && !imageFile && (
            <div className="mt-2">
              <img
                src={selectedQuestion.image || selectedQuestion.imageUrl}
                alt="Current question"
                className="max-w-[200px] max-h-[200px] object-cover"
              />
              <p className="text-sm text-gray-500 mt-1">Current image</p>
            </div>
          )}
        </Form.Item>

        {/* Buttons */}
        <div className="flex justify-end mt-2 gap-3">
          <button
            className="primary-outlined-btn"
            type="button"
            onClick={() => {
              setShowAddEditQuestionModal(false);
              setSelectedQuestion(null);
              setImageFile(null);
            }}
          >
            Cancel
          </button>
          <button className="primary-contained-btn">Save</button>
        </div>
      </Form>
    </Modal>
  );
}

export default AddEditQuestion;