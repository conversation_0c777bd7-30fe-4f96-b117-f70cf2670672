{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\WriteExam\\\\Instructions.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Instructions({\n  examData,\n  setView,\n  startTimer\n}) {\n  _s();\n  const navigate = useNavigate();\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-white\",\n            children: \"Instructions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-3xl mx-auto px-4 py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-3xl shadow-2xl border-0 p-10 mb-8 backdrop-blur-sm bg-white/95\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-6\",\n            children: examData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-700 font-medium\",\n            children: \"Ready to demonstrate your knowledge?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-white mb-3\",\n              children: formatTime(examData.duration)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-semibold text-blue-100\",\n              children: \"Duration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-8 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-white mb-3\",\n              children: examData.totalMarks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-semibold text-green-100\",\n              children: \"Total Marks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-white mb-3\",\n              children: examData.passingMarks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-semibold text-orange-100\",\n              children: \"Pass Marks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-8 py-3 bg-gray-500 text-white rounded-xl font-semibold hover:bg-gray-600 transition-colors shadow-lg\",\n            onClick: () => navigate('/user/quiz'),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-8 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors shadow-lg\",\n            onClick: () => {\n              startTimer();\n              setView(\"questions\");\n            },\n            children: \"Start Exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_s(Instructions, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = Instructions;\nexport default Instructions;\nvar _c;\n$RefreshReg$(_c, \"Instructions\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "Instructions", "examData", "<PERSON><PERSON><PERSON><PERSON>", "startTimer", "_s", "navigate", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "duration", "totalMarks", "passingMarks", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/WriteExam/Instructions.js"], "sourcesContent": ["import React from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nfunction Instructions({ examData, setView, startTimer }) {\r\n  const navigate = useNavigate();\r\n\r\n  const formatTime = (seconds) => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      {/* Header */}\r\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg\">\r\n        <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n          <div className=\"text-center\">\r\n            <h1 className=\"text-3xl font-bold text-white\">Instructions</h1>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-3xl mx-auto px-4 py-12\">\r\n        {/* Main Content Card */}\r\n        <div className=\"bg-white rounded-3xl shadow-2xl border-0 p-10 mb-8 backdrop-blur-sm bg-white/95\">\r\n          <div className=\"text-center mb-10\">\r\n            <h2 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-6\">\r\n              {examData.name}\r\n            </h2>\r\n            <p className=\"text-xl text-gray-700 font-medium\">Ready to demonstrate your knowledge?</p>\r\n          </div>\r\n\r\n          {/* Statistics */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-10\">\r\n            <div className=\"text-center p-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300\">\r\n              <div className=\"text-4xl font-bold text-white mb-3\">{formatTime(examData.duration)}</div>\r\n              <div className=\"text-lg font-semibold text-blue-100\">Duration</div>\r\n            </div>\r\n            <div className=\"text-center p-8 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300\">\r\n              <div className=\"text-4xl font-bold text-white mb-3\">{examData.totalMarks}</div>\r\n              <div className=\"text-lg font-semibold text-green-100\">Total Marks</div>\r\n            </div>\r\n            <div className=\"text-center p-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-300\">\r\n              <div className=\"text-4xl font-bold text-white mb-3\">{examData.passingMarks}</div>\r\n              <div className=\"text-lg font-semibold text-orange-100\">Pass Marks</div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex gap-4 justify-center\">\r\n            <button\r\n              className=\"px-8 py-3 bg-gray-500 text-white rounded-xl font-semibold hover:bg-gray-600 transition-colors shadow-lg\"\r\n              onClick={() => navigate('/user/quiz')}\r\n            >\r\n              Cancel\r\n            </button>\r\n\r\n            <button\r\n              className=\"px-8 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors shadow-lg\"\r\n              onClick={() => {\r\n                startTimer();\r\n                setView(\"questions\");\r\n              }}\r\n            >\r\n              Start Exam\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Instructions;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,YAAYA,CAAC;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAW,CAAC,EAAE;EAAAC,EAAA;EACvD,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExEhB,OAAA;MAAKe,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEhB,OAAA;QAAKe,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1ChB,OAAA;UAAKe,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BhB,OAAA;YAAIe,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAE3ChB,OAAA;QAAKe,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAC9FhB,OAAA;UAAKe,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChB,OAAA;YAAIe,SAAS,EAAC,oGAAoG;YAAAC,QAAA,EAC/Gd,QAAQ,CAACmB;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACLpB,OAAA;YAAGe,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF,CAAC,eAGNpB,OAAA;UAAKe,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC1DhB,OAAA;YAAKe,SAAS,EAAC,yIAAyI;YAAAC,QAAA,gBACtJhB,OAAA;cAAKe,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAET,UAAU,CAACL,QAAQ,CAACoB,QAAQ;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzFpB,OAAA;cAAKe,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACNpB,OAAA;YAAKe,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJhB,OAAA;cAAKe,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEd,QAAQ,CAACqB;YAAU;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/EpB,OAAA;cAAKe,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACNpB,OAAA;YAAKe,SAAS,EAAC,6IAA6I;YAAAC,QAAA,gBAC1JhB,OAAA;cAAKe,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEd,QAAQ,CAACsB;YAAY;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjFpB,OAAA;cAAKe,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpB,OAAA;UAAKe,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxChB,OAAA;YACEe,SAAS,EAAC,yGAAyG;YACnHU,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAAC,YAAY,CAAE;YAAAU,QAAA,EACvC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETpB,OAAA;YACEe,SAAS,EAAC,yGAAyG;YACnHU,OAAO,EAAEA,CAAA,KAAM;cACbrB,UAAU,CAAC,CAAC;cACZD,OAAO,CAAC,WAAW,CAAC;YACtB,CAAE;YAAAa,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACf,EAAA,CArEQJ,YAAY;EAAA,QACFH,WAAW;AAAA;AAAA4B,EAAA,GADrBzB,YAAY;AAuErB,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}