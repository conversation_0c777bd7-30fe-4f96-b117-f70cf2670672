{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [flaggedForReview, setFlaggedForReview] = useState(false);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Render MCQ Question\n  const renderMCQ = () => {\n    if (!question.options) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-500 text-center p-4 w-full\",\n        children: \"No options available for this question.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 w-full max-w-none\",\n      style: {\n        width: '100%'\n      },\n      children: Object.entries(question.options).map(([key, value]) => {\n        const optionKey = String(key).trim();\n        const optionValue = String(value).trim();\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.1 * parseInt(optionKey.charCodeAt(0) - 65),\n            type: \"spring\",\n            stiffness: 120\n          },\n          className: \"w-full\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            onClick: () => handleAnswerSelect(optionKey),\n            whileHover: {\n              scale: 1.01\n            },\n            whileTap: {\n              scale: 0.99\n            },\n            className: `w-full p-3 rounded-xl text-left transition-all duration-300 flex items-center space-x-4 border-2 ${currentAnswer === optionKey ? 'bg-blue-600 text-white border-blue-600 shadow-lg' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 shadow-sm hover:shadow-md'}`,\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0 transition-all duration-300 ${currentAnswer === optionKey ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-700 border-2 border-blue-200'}`,\n              children: optionKey\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-base font-medium leading-relaxed flex-1 ${currentAnswer === optionKey ? 'text-white' : 'text-gray-800'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this), currentAnswer === optionKey && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 200,\n                damping: 15\n              },\n              className: \"w-6 h-6 text-white flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render Fill-in-the-blank Question\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-50 rounded-xl p-6 border-2 border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-blue-800 font-semibold mb-4 text-lg\",\n        children: \"Your Answer:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentAnswer,\n        onChange: e => handleAnswerSelect(e.target.value),\n        placeholder: \"Type your answer here...\",\n        className: \"w-full p-4 border-2 border-blue-300 rounded-lg text-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 bg-white shadow-sm font-medium\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), currentAnswer && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-green-50 rounded-xl p-4 border-2 border-green-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-700 font-semibold\",\n          children: [\"Answer recorded: \\\"\", currentAnswer, \"\\\"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n\n  // Render Image-based Question\n  const renderImageQuestion = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [question.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: question.imageUrl,\n        alt: \"Question diagram\",\n        className: \"max-w-full max-h-96 rounded-lg shadow-lg border border-gray-200 mx-auto\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this), question.options ? renderMCQ() : renderFillBlank()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 pb-24\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-blue-600\",\n              children: [Math.round(progressPercentage), \"% Complete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"h-full bg-blue-600 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progressPercentage}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xl\",\n                children: \"\\uD83C\\uDDF9\\uD83C\\uDDFF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-bold text-gray-800\",\n                children: \"BrainWave\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-semibold text-gray-800\",\n              children: [\"Hello, \", username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-4 py-2 rounded-lg font-mono font-bold text-lg ${timeLeft <= 60 ? 'bg-red-100 text-red-700 border border-red-200' : timeLeft <= 300 ? 'bg-orange-100 text-orange-700 border border-orange-200' : 'bg-green-100 text-green-700 border border-green-200'}`,\n            children: formatTime(timeLeft)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex max-w-6xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 bg-blue-900 min-h-screen flex flex-col items-center py-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-800 rounded-lg cursor-pointer hover:bg-blue-700 transition-colors\",\n          title: \"Dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-700 rounded-lg cursor-pointer\",\n          title: \"Quiz\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-2 rounded-lg cursor-pointer transition-colors ${flaggedForReview ? 'bg-yellow-600' : 'bg-gray-600 hover:bg-gray-500'}`,\n          title: \"Flag for Review\",\n          onClick: () => setFlaggedForReview(!flaggedForReview),\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 px-8 py-8 pb-16 w-full\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3,\n              ease: \"easeOut\"\n            },\n            className: \"w-full\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-8 w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-8 w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-3xl font-bold text-gray-900 leading-relaxed mb-4 w-full\",\n                  children: question.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6 w-full\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.image,\n                    alt: \"Question\",\n                    className: \"max-w-full max-h-80 rounded-lg border border-gray-200 mx-auto\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-20 w-full\",\n                style: {\n                  width: '100%',\n                  paddingBottom: '2rem'\n                },\n                children: [question.answerType === \"Options\" && renderMCQ(), (question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank(), question.imageUrl && renderImageQuestion()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)\n          }, questionIndex, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: onPrevious,\n          disabled: questionIndex === 0,\n          whileHover: questionIndex !== 0 ? {\n            scale: 1.02\n          } : {},\n          whileTap: questionIndex !== 0 ? {\n            scale: 0.98\n          } : {},\n          className: `flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${questionIndex === 0 ? 'opacity-40 cursor-not-allowed bg-gray-200 text-gray-500' : 'bg-white text-gray-700 hover:bg-gray-50 shadow-lg border border-gray-200 hover:shadow-xl'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: onNext,\n          disabled: !isAnswered,\n          whileHover: isAnswered ? {\n            scale: 1.02\n          } : {},\n          whileTap: isAnswered ? {\n            scale: 0.98\n          } : {},\n          className: `flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${!isAnswered ? 'opacity-40 cursor-not-allowed bg-gray-200 text-gray-500' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl' : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"h8MQJ2a8/hPn+UQgN5e/y9c4kQE=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "username", "onNext", "onPrevious", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "flagged<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setFlaggedForReview", "handleAnswerSelect", "answer", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "progressPercentage", "renderMCQ", "options", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "Object", "entries", "map", "key", "value", "optionKey", "String", "trim", "optionValue", "div", "initial", "opacity", "y", "animate", "transition", "delay", "parseInt", "charCodeAt", "type", "stiffness", "button", "onClick", "whileHover", "scale", "whileTap", "damping", "fill", "viewBox", "fillRule", "d", "clipRule", "renderFillBlank", "onChange", "e", "target", "placeholder", "renderImageQuestion", "imageUrl", "src", "alt", "round", "duration", "title", "mode", "exit", "ease", "name", "image", "paddingBottom", "answerType", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  username = \"Student\",\n  onNext,\n  onPrevious,\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const [flaggedForReview, setFlaggedForReview] = useState(false);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Render MCQ Question\n  const renderMCQ = () => {\n    if (!question.options) {\n      return <div className=\"text-red-500 text-center p-4 w-full\">No options available for this question.</div>;\n    }\n\n    return (\n      <div className=\"space-y-4 w-full max-w-none\" style={{width: '100%'}}>\n        {Object.entries(question.options).map(([key, value]) => {\n          const optionKey = String(key).trim();\n          const optionValue = String(value).trim();\n\n          return (\n            <motion.div\n              key={optionKey}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{\n                delay: 0.1 * parseInt(optionKey.charCodeAt(0) - 65),\n                type: \"spring\",\n                stiffness: 120\n              }}\n              className=\"w-full\"\n            >\n              <motion.button\n                onClick={() => handleAnswerSelect(optionKey)}\n                whileHover={{ scale: 1.01 }}\n                whileTap={{ scale: 0.99 }}\n                className={`w-full p-3 rounded-xl text-left transition-all duration-300 flex items-center space-x-4 border-2 ${\n                  currentAnswer === optionKey\n                    ? 'bg-blue-600 text-white border-blue-600 shadow-lg'\n                    : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 shadow-sm hover:shadow-md'\n                }`}\n                style={{width: '100%'}}\n              >\n                {/* Letter badge - positioned outside */}\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0 transition-all duration-300 ${\n                  currentAnswer === optionKey\n                    ? 'bg-white text-blue-600'\n                    : 'bg-blue-100 text-blue-700 border-2 border-blue-200'\n                }`}>\n                  {optionKey}\n                </div>\n\n                {/* Answer text */}\n                <span className={`text-base font-medium leading-relaxed flex-1 ${\n                  currentAnswer === optionKey ? 'text-white' : 'text-gray-800'\n                }`}>\n                  {optionValue}\n                </span>\n\n                {/* Check icon for selected */}\n                {currentAnswer === optionKey && (\n                  <motion.div\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ type: \"spring\", stiffness: 200, damping: 15 }}\n                    className=\"w-6 h-6 text-white flex-shrink-0\"\n                  >\n                    <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </motion.div>\n                )}\n              </motion.button>\n            </motion.div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render Fill-in-the-blank Question\n  const renderFillBlank = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-blue-50 rounded-xl p-6 border-2 border-blue-200\">\n        <label className=\"block text-blue-800 font-semibold mb-4 text-lg\">\n          Your Answer:\n        </label>\n        <input\n          type=\"text\"\n          value={currentAnswer}\n          onChange={(e) => handleAnswerSelect(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full p-4 border-2 border-blue-300 rounded-lg text-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 bg-white shadow-sm font-medium\"\n        />\n      </div>\n      {currentAnswer && (\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-green-50 rounded-xl p-4 border-2 border-green-200\"\n        >\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <p className=\"text-green-700 font-semibold\">\n              Answer recorded: \"{currentAnswer}\"\n            </p>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n\n  // Render Image-based Question\n  const renderImageQuestion = () => (\n    <div className=\"space-y-6\">\n      {question.imageUrl && (\n        <div className=\"text-center mb-6\">\n          <img\n            src={question.imageUrl}\n            alt=\"Question diagram\"\n            className=\"max-w-full max-h-96 rounded-lg shadow-lg border border-gray-200 mx-auto\"\n          />\n        </div>\n      )}\n\n      {question.options ? renderMCQ() : renderFillBlank()}\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 pb-24\">\n      {/* Header with Tanzanian accent */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          {/* Top Progress Bar */}\n          <div className=\"mb-4\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <span className=\"text-sm font-medium text-gray-600\">Question {questionIndex + 1} of {totalQuestions}</span>\n              <span className=\"text-sm font-medium text-blue-600\">{Math.round(progressPercentage)}% Complete</span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"h-full bg-blue-600 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progressPercentage}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n          </div>\n\n          {/* Header Content */}\n          <div className=\"flex items-center justify-between\">\n            {/* Logo and Branding */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-xl\">🇹🇿</span>\n                <span className=\"text-lg font-bold text-gray-800\">BrainWave</span>\n              </div>\n            </div>\n\n            {/* User Greeting */}\n            <div className=\"text-center\">\n              <p className=\"text-lg font-semibold text-gray-800\">Hello, {username}</p>\n            </div>\n\n            {/* Timer */}\n            <div className={`px-4 py-2 rounded-lg font-mono font-bold text-lg ${\n              timeLeft <= 60 ? 'bg-red-100 text-red-700 border border-red-200' :\n              timeLeft <= 300 ? 'bg-orange-100 text-orange-700 border border-orange-200' :\n              'bg-green-100 text-green-700 border border-green-200'\n            }`}>\n              {formatTime(timeLeft)}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Sidebar and Main Content */}\n      <div className=\"flex max-w-6xl mx-auto\">\n        {/* Compact Sidebar */}\n        <div className=\"w-16 bg-blue-900 min-h-screen flex flex-col items-center py-6 space-y-4\">\n          {/* Navigation Icons */}\n          <div className=\"p-2 bg-blue-800 rounded-lg cursor-pointer hover:bg-blue-700 transition-colors\" title=\"Dashboard\">\n            <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\" />\n            </svg>\n          </div>\n\n          <div className=\"p-2 bg-blue-700 rounded-lg cursor-pointer\" title=\"Quiz\">\n            <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n\n          {/* Flag for Review */}\n          <div\n            className={`p-2 rounded-lg cursor-pointer transition-colors ${\n              flaggedForReview ? 'bg-yellow-600' : 'bg-gray-600 hover:bg-gray-500'\n            }`}\n            title=\"Flag for Review\"\n            onClick={() => setFlaggedForReview(!flaggedForReview)}\n          >\n            <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n        </div>\n\n        {/* Main Question Panel */}\n        <div className=\"flex-1 px-8 py-8 pb-16 w-full\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={questionIndex}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3, ease: \"easeOut\" }}\n              className=\"w-full\"\n            >\n              {/* Question Card */}\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-8 w-full\">\n                {/* Question Header */}\n                <div className=\"mb-8 w-full\">\n                  <h2 className=\"text-3xl font-bold text-gray-900 leading-relaxed mb-4 w-full\">\n                    {question.name}\n                  </h2>\n                  {question.image && (\n                    <div className=\"mb-6 w-full\">\n                      <img\n                        src={question.image}\n                        alt=\"Question\"\n                        className=\"max-w-full max-h-80 rounded-lg border border-gray-200 mx-auto\"\n                      />\n                    </div>\n                  )}\n                </div>\n\n                {/* Question Options */}\n                <div className=\"mb-20 w-full\" style={{width: '100%', paddingBottom: '2rem'}}>\n                  {question.answerType === \"Options\" && renderMCQ()}\n                  {(question.answerType === \"Free Text\" || question.answerType === \"Fill in the Blank\") && renderFillBlank()}\n                  {question.imageUrl && renderImageQuestion()}\n                </div>\n              </div>\n            </motion.div>\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Bottom Navigation - Centered with small gap */}\n      <div className=\"fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50\">\n        <div className=\"flex items-center justify-center gap-3\">\n          {/* Previous Button */}\n          <motion.button\n            onClick={onPrevious}\n            disabled={questionIndex === 0}\n            whileHover={questionIndex !== 0 ? { scale: 1.02 } : {}}\n            whileTap={questionIndex !== 0 ? { scale: 0.98 } : {}}\n            className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${\n              questionIndex === 0\n                ? 'opacity-40 cursor-not-allowed bg-gray-200 text-gray-500'\n                : 'bg-white text-gray-700 hover:bg-gray-50 shadow-lg border border-gray-200 hover:shadow-xl'\n            }`}\n          >\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n            </svg>\n            <span>Previous</span>\n          </motion.button>\n\n          {/* Next/Submit Button */}\n          <motion.button\n            onClick={onNext}\n            disabled={!isAnswered}\n            whileHover={isAnswered ? { scale: 1.02 } : {}}\n            whileTap={isAnswered ? { scale: 0.98 } : {}}\n            className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${\n              !isAnswered\n                ? 'opacity-40 cursor-not-allowed bg-gray-200 text-gray-500'\n                : questionIndex === totalQuestions - 1\n                ? 'bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl'\n                : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'\n            }`}\n          >\n            <span>\n              {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next'}\n            </span>\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              {questionIndex === totalQuestions - 1 ? (\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              ) : (\n                <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n              )}\n            </svg>\n          </motion.button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,QAAQ,GAAG,SAAS;EACpBC,MAAM;EACNC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAACU,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACdiB,gBAAgB,CAACR,cAAc,IAAI,EAAE,CAAC;IACtCU,aAAa,CAAC,CAAC,CAACV,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMe,kBAAkB,GAAIC,MAAM,IAAK;IACrCN,gBAAgB,CAACM,MAAM,CAAC;IACxBJ,aAAa,CAAC,IAAI,CAAC;IACnBT,cAAc,CAACa,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACzB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,MAAMyB,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAAC3B,QAAQ,CAAC4B,OAAO,EAAE;MACrB,oBAAO9B,OAAA;QAAK+B,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAC3G;IAEA,oBACEpC,OAAA;MAAK+B,SAAS,EAAC,6BAA6B;MAACM,KAAK,EAAE;QAACC,KAAK,EAAE;MAAM,CAAE;MAAAN,QAAA,EACjEO,MAAM,CAACC,OAAO,CAACtC,QAAQ,CAAC4B,OAAO,CAAC,CAACW,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QACtD,MAAMC,SAAS,GAAGC,MAAM,CAACH,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC;QACpC,MAAMC,WAAW,GAAGF,MAAM,CAACF,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;QAExC,oBACE9C,OAAA,CAACH,MAAM,CAACmD,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YACVC,KAAK,EAAE,GAAG,GAAGC,QAAQ,CAACX,SAAS,CAACY,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACnDC,IAAI,EAAE,QAAQ;YACdC,SAAS,EAAE;UACb,CAAE;UACF3B,SAAS,EAAC,QAAQ;UAAAC,QAAA,eAElBhC,OAAA,CAACH,MAAM,CAAC8D,MAAM;YACZC,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC0B,SAAS,CAAE;YAC7CiB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1B/B,SAAS,EAAG,oGACVnB,aAAa,KAAKgC,SAAS,GACvB,kDAAkD,GAClD,2FACL,EAAE;YACHP,KAAK,EAAE;cAACC,KAAK,EAAE;YAAM,CAAE;YAAAN,QAAA,gBAGvBhC,OAAA;cAAK+B,SAAS,EAAG,uHACfnB,aAAa,KAAKgC,SAAS,GACvB,wBAAwB,GACxB,oDACL,EAAE;cAAAZ,QAAA,EACAY;YAAS;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAGNpC,OAAA;cAAM+B,SAAS,EAAG,gDAChBnB,aAAa,KAAKgC,SAAS,GAAG,YAAY,GAAG,eAC9C,EAAE;cAAAZ,QAAA,EACAe;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAGNxB,aAAa,KAAKgC,SAAS,iBAC1B5C,OAAA,CAACH,MAAM,CAACmD,GAAG;cACTC,OAAO,EAAE;gBAAEa,KAAK,EAAE;cAAE,CAAE;cACtBV,OAAO,EAAE;gBAAEU,KAAK,EAAE;cAAE,CAAE;cACtBT,UAAU,EAAE;gBAAEI,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEM,OAAO,EAAE;cAAG,CAAE;cAC5DjC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAE5ChC,OAAA;gBAAKiE,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlC,QAAA,eAC1ChC,OAAA;kBAAMmE,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,oHAAoH;kBAACC,QAAQ,EAAC;gBAAS;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY;QAAC,GAlDXQ,SAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDJ,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAGA,CAAA,kBACtBtE,OAAA;IAAK+B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBhC,OAAA;MAAK+B,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEhC,OAAA;QAAO+B,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAElE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRpC,OAAA;QACEyD,IAAI,EAAC,MAAM;QACXd,KAAK,EAAE/B,aAAc;QACrB2D,QAAQ,EAAGC,CAAC,IAAKtD,kBAAkB,CAACsD,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;QACpD+B,WAAW,EAAC,0BAA0B;QACtC3C,SAAS,EAAC;MAAiK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5K,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACLxB,aAAa,iBACZZ,OAAA,CAACH,MAAM,CAACmD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BpB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eAEhEhC,OAAA;QAAK+B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ChC,OAAA;UAAK+B,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjFhC,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eACzEhC,OAAA;cAAMmE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpC,OAAA;UAAG+B,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAAC,qBACxB,EAACpB,aAAa,EAAC,IACnC;QAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAMuC,mBAAmB,GAAGA,CAAA,kBAC1B3E,OAAA;IAAK+B,SAAS,EAAC,WAAW;IAAAC,QAAA,GACvB9B,QAAQ,CAAC0E,QAAQ,iBAChB5E,OAAA;MAAK+B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BhC,OAAA;QACE6E,GAAG,EAAE3E,QAAQ,CAAC0E,QAAS;QACvBE,GAAG,EAAC,kBAAkB;QACtB/C,SAAS,EAAC;MAAyE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAEAlC,QAAQ,CAAC4B,OAAO,GAAGD,SAAS,CAAC,CAAC,GAAGyC,eAAe,CAAC,CAAC;EAAA;IAAArC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CACN;EAED,oBACEpC,OAAA;IAAK+B,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5ChC,OAAA;MAAK+B,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DhC,OAAA;QAAK+B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1ChC,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhC,OAAA;YAAK+B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDhC,OAAA;cAAM+B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,WAAS,EAAC7B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3GpC,OAAA;cAAM+B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAET,IAAI,CAACwD,KAAK,CAACnD,kBAAkB,CAAC,EAAC,YAAU;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNpC,OAAA;YAAK+B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDhC,OAAA,CAACH,MAAM,CAACmD,GAAG;cACTjB,SAAS,EAAC,iCAAiC;cAC3CkB,OAAO,EAAE;gBAAEX,KAAK,EAAE;cAAE,CAAE;cACtBc,OAAO,EAAE;gBAAEd,KAAK,EAAG,GAAEV,kBAAmB;cAAG,CAAE;cAC7CyB,UAAU,EAAE;gBAAE2B,QAAQ,EAAE;cAAI;YAAE;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpC,OAAA;UAAK+B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAEhDhC,OAAA;YAAK+B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1ChC,OAAA;cAAK+B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ChC,OAAA;gBAAM+B,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCpC,OAAA;gBAAM+B,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpC,OAAA;YAAK+B,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BhC,OAAA;cAAG+B,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,SAAO,EAACxB,QAAQ;YAAA;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eAGNpC,OAAA;YAAK+B,SAAS,EAAG,oDACfxB,QAAQ,IAAI,EAAE,GAAG,+CAA+C,GAChEA,QAAQ,IAAI,GAAG,GAAG,wDAAwD,GAC1E,qDACD,EAAE;YAAAyB,QAAA,EACAZ,UAAU,CAACb,QAAQ;UAAC;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErChC,OAAA;QAAK+B,SAAS,EAAC,yEAAyE;QAAAC,QAAA,gBAEtFhC,OAAA;UAAK+B,SAAS,EAAC,+EAA+E;UAACkD,KAAK,EAAC,WAAW;UAAAjD,QAAA,eAC9GhC,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eACzEhC,OAAA;cAAMoE,CAAC,EAAC;YAAkM;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,2CAA2C;UAACkD,KAAK,EAAC,MAAM;UAAAjD,QAAA,eACrEhC,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eACzEhC,OAAA;cAAMmE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,+IAA+I;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7L;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpC,OAAA;UACE+B,SAAS,EAAG,mDACVf,gBAAgB,GAAG,eAAe,GAAG,+BACtC,EAAE;UACHiE,KAAK,EAAC,iBAAiB;UACvBrB,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UAAAgB,QAAA,eAEtDhC,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eACzEhC,OAAA;cAAMmE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oGAAoG;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpC,OAAA;QAAK+B,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5ChC,OAAA,CAACF,eAAe;UAACoF,IAAI,EAAC,MAAM;UAAAlD,QAAA,eAC1BhC,OAAA,CAACH,MAAM,CAACmD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BgC,IAAI,EAAE;cAAEjC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BE,UAAU,EAAE;cAAE2B,QAAQ,EAAE,GAAG;cAAEI,IAAI,EAAE;YAAU,CAAE;YAC/CrD,SAAS,EAAC,QAAQ;YAAAC,QAAA,eAGlBhC,OAAA;cAAK+B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBAE/EhC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhC,OAAA;kBAAI+B,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,EACzE9B,QAAQ,CAACmF;gBAAI;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,EACJlC,QAAQ,CAACoF,KAAK,iBACbtF,OAAA;kBAAK+B,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1BhC,OAAA;oBACE6E,GAAG,EAAE3E,QAAQ,CAACoF,KAAM;oBACpBR,GAAG,EAAC,UAAU;oBACd/C,SAAS,EAAC;kBAA+D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNpC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAACM,KAAK,EAAE;kBAACC,KAAK,EAAE,MAAM;kBAAEiD,aAAa,EAAE;gBAAM,CAAE;gBAAAvD,QAAA,GACzE9B,QAAQ,CAACsF,UAAU,KAAK,SAAS,IAAI3D,SAAS,CAAC,CAAC,EAChD,CAAC3B,QAAQ,CAACsF,UAAU,KAAK,WAAW,IAAItF,QAAQ,CAACsF,UAAU,KAAK,mBAAmB,KAAKlB,eAAe,CAAC,CAAC,EACzGpE,QAAQ,CAAC0E,QAAQ,IAAID,mBAAmB,CAAC,CAAC;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA/BDjC,aAAa;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEhC,OAAA;QAAK+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDhC,OAAA,CAACH,MAAM,CAAC8D,MAAM;UACZC,OAAO,EAAElD,UAAW;UACpB+E,QAAQ,EAAEtF,aAAa,KAAK,CAAE;UAC9B0D,UAAU,EAAE1D,aAAa,KAAK,CAAC,GAAG;YAAE2D,KAAK,EAAE;UAAK,CAAC,GAAG,CAAC,CAAE;UACvDC,QAAQ,EAAE5D,aAAa,KAAK,CAAC,GAAG;YAAE2D,KAAK,EAAE;UAAK,CAAC,GAAG,CAAC,CAAE;UACrD/B,SAAS,EAAG,8FACV5B,aAAa,KAAK,CAAC,GACf,yDAAyD,GACzD,0FACL,EAAE;UAAA6B,QAAA,gBAEHhC,OAAA;YAAK+B,SAAS,EAAC,SAAS;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,eAC9DhC,OAAA;cAAMmE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mHAAmH;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjK,CAAC,eACNpC,OAAA;YAAAgC,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGhBpC,OAAA,CAACH,MAAM,CAAC8D,MAAM;UACZC,OAAO,EAAEnD,MAAO;UAChBgF,QAAQ,EAAE,CAAC3E,UAAW;UACtB+C,UAAU,EAAE/C,UAAU,GAAG;YAAEgD,KAAK,EAAE;UAAK,CAAC,GAAG,CAAC,CAAE;UAC9CC,QAAQ,EAAEjD,UAAU,GAAG;YAAEgD,KAAK,EAAE;UAAK,CAAC,GAAG,CAAC,CAAE;UAC5C/B,SAAS,EAAG,8FACV,CAACjB,UAAU,GACP,yDAAyD,GACzDX,aAAa,KAAKC,cAAc,GAAG,CAAC,GACpC,sEAAsE,GACtE,oEACL,EAAE;UAAA4B,QAAA,gBAEHhC,OAAA;YAAAgC,QAAA,EACG7B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;UAAM;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACPpC,OAAA;YAAK+B,SAAS,EAAC,SAAS;YAACkC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlC,QAAA,EAC7D7B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCJ,OAAA;cAAMmE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAErKpC,OAAA;cAAMmE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACrK;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAzUIV,YAAY;AAAAyF,EAAA,GAAZzF,YAAY;AA2UlB,eAAeA,YAAY;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}