import { message } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { getExamById } from "../../../apicalls/exams";
import { addReport } from "../../../apicalls/reports";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import Instructions from "./Instructions";
import Pass from "../../../assets/pass.gif";
import Fail from "../../../assets/fail.gif";
import Confetti from "react-confetti";
import useWindowSize from "react-use/lib/useWindowSize";
import PassSound from "../../../assets/pass.mp3";
import FailSound from "../../../assets/fail.mp3";
import { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from "../../../apicalls/chat";
import ContentRenderer from "../../../components/ContentRenderer";

import QuizRenderer from "../../../components/QuizRenderer";

function WriteExam() {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [result, setResult] = useState({});
  const params = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [view, setView] = useState("instructions");
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [timeUp, setTimeUp] = useState(false);
  const [intervalId, setIntervalId] = useState(null);
  const { user } = useSelector((state) => state.user);

  const { width, height } = useWindowSize();
  const [explanations, setExplanations] = useState({});

  const getExamData = useCallback(async () => {
    try {
      dispatch(ShowLoading());
      const response = await getExamById({ examId: params.id });
      dispatch(HideLoading());
      if (response.success) {
        setQuestions(response.data?.questions || []);
        setExamData(response.data);
        setSecondsLeft(response.data?.duration || 0);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  }, [params.id, dispatch]);

  const checkFreeTextAnswers = async (payload) => {
    if (!payload.length) return [];
    const { data } = await chatWithChatGPTToGetAns(payload);
    return data;
  };

  const calculateResult = useCallback(async () => {
    try {
      // Check if user is available
      if (!user || !user._id) {
        message.error("User not found. Please log in again.");
        navigate("/login");
        return;
      }

      dispatch(ShowLoading());

      const freeTextPayload = [];
      const indexMap = [];

      questions.forEach((q, idx) => {
        if (q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          indexMap.push(idx);
          freeTextPayload.push({
            question: q.name,
            expectedAnswer: q.correctAnswer || q.correctOption,
            userAnswer: selectedOptions[idx] || "",
          });
        }
      });

      const gptResults = await checkFreeTextAnswers(freeTextPayload);
      const gptMap = {};

      gptResults.forEach((r) => {
        if (r.result && typeof r.result.isCorrect === "boolean") {
          gptMap[r.question] = r.result;
        } else if (typeof r.isCorrect === "boolean") {
          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || "" };
        }
      });

      const correctAnswers = [];
      const wrongAnswers = [];
      const wrongPayload = [];

      questions.forEach((q, idx) => {
        const userAnswerKey = selectedOptions[idx] || "";

        if (q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          const { isCorrect = false, reason = "" } = gptMap[q.name] || {};
          const enriched = { ...q, userAnswer: userAnswerKey, reason };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
            wrongPayload.push({
              question: q.name,
              expectedAnswer: q.correctAnswer || q.correctOption,
              userAnswer: userAnswerKey,
            });
          }
        } else if (q.answerType === "Options") {
          const correctKey = q.correctOption;
          const correctValue = (q.options && q.options[correctKey]) || correctKey;
          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || "";

          const isCorrect = correctKey === userAnswerKey;
          const enriched = { ...q, userAnswer: userAnswerKey };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
            wrongPayload.push({
              question: q.name,
              expectedAnswer: correctValue,
              userAnswer: userValue,
            });
          }
        }
      });

      const verdict = correctAnswers.length >= examData.passingMarks ? "Pass" : "Fail";
      const tempResult = { correctAnswers, wrongAnswers, verdict };

      setResult(tempResult);

      const response = await addReport({
        exam: params.id,
        result: tempResult,
        user: user._id,
      });

      if (response.success) {
        setView("result");
        window.scrollTo(0, 0);
        new Audio(verdict === "Pass" ? PassSound : FailSound).play();
      } else {
        message.error(response.message);
      }
      dispatch(HideLoading());

    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);

  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {
    try {
      dispatch(ShowLoading());
      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });
      dispatch(HideLoading());

      if (response.success) {
        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));
      } else {
        message.error(response.error || "Failed to fetch explanation.");
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  const startTimer = () => {
    const totalSeconds = examData?.duration || 0;
    setSecondsLeft(totalSeconds);

    const newIntervalId = setInterval(() => {
      setSecondsLeft((prevSeconds) => {
        if (prevSeconds > 0) {
          return prevSeconds - 1;
        } else {
          setTimeUp(true);
          return 0;
        }
      });
    }, 1000);
    setIntervalId(newIntervalId);
  };

  useEffect(() => {
    if (timeUp && view === "questions") {
      clearInterval(intervalId);
      calculateResult();
    }
  }, [timeUp, view, intervalId, calculateResult]);

  useEffect(() => {
    if (params.id) {
      getExamData();
    }
  }, [params.id, getExamData]);

  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);

  // Repair function for fixing orphaned questions
  const repairExamQuestions = async () => {
    try {
      dispatch(ShowLoading());
      const response = await fetch('/api/exams/repair-exam-questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ examId: params.id })
      });

      const data = await response.json();
      if (data.success) {
        message.success(data.message);
        // Reload the exam data
        getExamData();
      } else {
        message.error(data.message);
      }
    } catch (error) {
      message.error("Failed to repair exam questions");
    } finally {
      dispatch(HideLoading());
    }
  };

  // Check if user is authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4">
          <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-8">Please log in to access the exam and start your learning journey.</p>
          <button
            className="w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg"
            onClick={() => navigate("/login")}
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return examData ? (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Enhanced Header for non-quiz views */}
      {view !== "instructions" && view !== "questions" && (
        <div className="bg-white/80 backdrop-blur-sm border-b border-blue-100 shadow-lg">
          <div className="max-w-6xl mx-auto px-6 py-8">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  {examData?.name || 'Loading...'}
                </h1>
                <p className="text-gray-600 mt-1">Exam Results & Review</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {view === "instructions" && (
        <Instructions
          examData={examData}
          setView={setView}
          startTimer={startTimer}
        />
      )}

      {view === "questions" && (
        questions.length === 0 ? (
          <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center">
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center">
              <div className="w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-amber-800 mb-4">No Questions Found</h3>
              <p className="text-amber-700 mb-8 text-lg leading-relaxed">
                This exam appears to have no questions. Let's fix this issue and get you started!
              </p>
              <button
                onClick={repairExamQuestions}
                className="w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg"
              >
                🔧 Repair Questions
              </button>
            </div>
          </div>
        ) : (
          questions[selectedQuestionIndex] && (
            <QuizRenderer
              question={questions[selectedQuestionIndex]}
              questionIndex={selectedQuestionIndex}
              totalQuestions={questions.length}
              selectedAnswer={selectedOptions[selectedQuestionIndex]}
              onAnswerChange={(answer) =>
                setSelectedOptions({
                  ...selectedOptions,
                  [selectedQuestionIndex]: answer,
                })
              }
              timeLeft={secondsLeft}
              username={user?.name || "Student"}
              examTitle={examData?.name || "Quiz"}
              onNext={() => {
                if (selectedQuestionIndex === questions.length - 1) {
                  calculateResult();
                } else {
                  setSelectedQuestionIndex(selectedQuestionIndex + 1);
                }
              }}
              onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}
            />
          )
        )
      )}

      {view === "result" && (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8">
          {result.verdict === "Pass" && <Confetti width={width} height={height} />}

          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden">
              {/* Modern Header */}
              <div className={`px-8 py-10 text-center relative ${
                result.verdict === "Pass"
                  ? "bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10"
                  : "bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10"
              }`}>
                <div className="relative">
                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${
                    result.verdict === "Pass"
                      ? "bg-gradient-to-br from-emerald-500 to-green-600"
                      : "bg-gradient-to-br from-amber-500 to-orange-600"
                  }`}>
                    <img
                      src={result.verdict === "Pass" ? Pass : Fail}
                      alt={result.verdict}
                      className="w-12 h-12 object-contain"
                    />
                  </div>
                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${
                    result.verdict === "Pass" ? "text-emerald-700" : "text-amber-700"
                  }`}>
                    {result.verdict === "Pass" ? "Excellent Work!" : "Keep Pushing!"}
                  </h1>
                  <p className="text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed">
                    {result.verdict === "Pass"
                      ? "You've mastered this exam with flying colors!"
                      : "Every challenge makes you stronger. Try again!"}
                  </p>
                </div>
              </div>

              {/* Modern Statistics Cards */}
              <div className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  {/* Score Card */}
                  <div className="group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="text-4xl font-black text-blue-600 mb-2 tracking-tight">
                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%
                      </div>
                      <div className="text-sm font-bold text-blue-700/80 uppercase tracking-wider">Your Score</div>
                    </div>
                  </div>

                  {/* Correct vs Total */}
                  <div className="group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300">
                    <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="text-4xl font-black text-emerald-600 mb-2 tracking-tight">
                        {result.correctAnswers?.length || 0}/{questions.length}
                      </div>
                      <div className="text-sm font-bold text-emerald-700/80 uppercase tracking-wider">Correct</div>
                    </div>
                  </div>

                  {/* Pass Status */}
                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${
                    result.verdict === "Pass"
                      ? "bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50"
                      : "bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50"
                  }`}>
                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${
                      result.verdict === "Pass" ? "from-emerald-500/5" : "from-amber-500/5"
                    } to-transparent`}></div>
                    <div className="relative text-center">
                      <div className={`text-4xl font-black mb-2 tracking-tight ${
                        result.verdict === "Pass" ? "text-emerald-600" : "text-amber-600"
                      }`}>
                        {result.verdict === "Pass" ? "PASS" : "RETRY"}
                      </div>
                      <div className={`text-sm font-bold uppercase tracking-wider ${
                        result.verdict === "Pass" ? "text-emerald-700/80" : "text-amber-700/80"
                      }`}>
                        {result.verdict === "Pass" ? "Success!" : `Need ${examData.passingMarks}`}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Modern Progress Visualization */}
                <div className="mb-8">
                  <div className="relative bg-slate-100 rounded-2xl p-6">
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-bold text-slate-700 mb-1">Performance Overview</h3>
                      <p className="text-sm text-slate-500">Your achievement level</p>
                    </div>
                    <div className="relative">
                      <div className="w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden">
                        <div
                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${
                            result.verdict === "Pass"
                              ? "bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500"
                              : "bg-gradient-to-r from-amber-500 via-orange-500 to-red-500"
                          }`}
                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-3">
                        <span className="text-xs font-medium text-slate-500">0%</span>
                        <span className={`text-lg font-black tracking-tight ${
                          result.verdict === "Pass" ? "text-emerald-600" : "text-amber-600"
                        }`}>
                          {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%
                        </span>
                        <span className="text-xs font-medium text-slate-500">100%</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Modern Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    className="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    onClick={() => setView("review")}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative">Review Answers</span>
                  </button>

                  <button
                    className="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    onClick={() => {
                      // Reset exam state
                      setSelectedOptions({});
                      setSelectedQuestionIndex(0);
                      setSecondsLeft(examData.duration || 0);
                      setView("instructions");
                      setResult(null);
                      setTimeUp(false);
                      // Clear any existing timer
                      if (intervalId) {
                        clearInterval(intervalId);
                        setIntervalId(null);
                      }
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative">Retry Exam</span>
                  </button>

                  <button
                    className="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    onClick={() => navigate("/user/dashboard")}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative">Dashboard</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {view === "review" && (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6">
          <div className="max-w-4xl mx-auto px-4">
            {/* Simple Header */}
            <div className="text-center mb-6">
              <div className="bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50">
                <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                  Answer Review
                </h2>
                <p className="text-slate-600">Quick overview of your answers</p>
              </div>
            </div>

            {/* Compact Questions Review */}
            <div className="space-y-3 mb-6">
              {questions.map((question, index) => {
                const correctAnswer = question.answerType === "Options"
                  ? question.correctOption
                  : question.correctAnswer;
                const isCorrect = correctAnswer === selectedOptions[index];
                const userAnswer = selectedOptions[index];

                return (
                  <div
                    key={index}
                    className="backdrop-blur-md rounded-lg shadow-md border-2 p-4"
                    style={{
                      backgroundColor: isCorrect ? '#bbf7d0' : '#fecaca',
                      borderColor: isCorrect ? '#22c55e' : '#ef4444'
                    }}
                  >
                    {/* Question */}
                    <div className="mb-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <p className="text-slate-800 font-medium leading-relaxed">{question.name}</p>
                        </div>
                      </div>
                    </div>

                    {/* Your Answer with Visual Indicator */}
                    <div className="mb-2">
                      <span className="text-sm font-semibold text-slate-600">Your Answer: </span>
                      <span className={`font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>
                        {question.answerType === "Options"
                          ? (question.options && userAnswer && question.options[userAnswer]) || userAnswer || "Not answered"
                          : userAnswer || "Not answered"}
                      </span>
                      {isCorrect ? (
                        <span className="ml-3 text-green-600 text-2xl font-black">✓</span>
                      ) : (
                        <span className="ml-3 text-red-600 text-2xl font-black">✗</span>
                      )}
                    </div>

                    {/* Correct Answer (only for wrong answers) */}
                    {!isCorrect && (
                      <div className="mb-2">
                        <span className="text-sm font-semibold text-slate-600">Correct Answer: </span>
                        <span className="font-medium text-green-700">
                          {question.answerType === "Options"
                            ? (question.options && question.options[question.correctOption]) || question.correctOption
                            : (question.correctAnswer || question.correctOption)}
                        </span>
                        <span className="ml-3 text-green-500 text-2xl font-black">✓</span>
                      </div>
                    )}

                    {/* See Explanation Button (only for wrong answers) */}
                    {!isCorrect && (
                      <div className="mt-2">
                        <button
                          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                          onClick={() => {
                            console.log('Fetching explanation for:', question.name);
                            fetchExplanation(
                              question.name,
                              question.answerType === "Options"
                                ? (question.options && question.options[question.correctOption]) || question.correctOption
                                : (question.correctAnswer || question.correctOption),
                              question.answerType === "Options"
                                ? (question.options && question.options[userAnswer]) || userAnswer || "Not answered"
                                : userAnswer || "Not answered",
                              question.image
                            );
                          }}
                        >
                          <span>💡</span>
                          <span>Get Explanation</span>
                        </button>
                      </div>
                    )}

                    {/* Explanation */}
                    {explanations[question.name] && (
                      <div className="mt-2 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200">
                        <div className="flex items-center mb-2">
                          <span className="text-blue-600 text-lg mr-2">💡</span>
                          <h6 className="font-bold text-gray-800 text-base">
                            Explanation
                          </h6>
                        </div>

                        {/* Show diagram/image for image-based questions */}
                        {(question.image || question.imageUrl) && (
                          <div className="mb-3 p-2 bg-gray-50 rounded border border-gray-200">
                            <div className="flex items-center mb-1">
                              <span className="text-gray-700 text-sm font-medium">📊 Reference Diagram:</span>
                            </div>
                            <div className="flex justify-center">
                              <img
                                src={question.image || question.imageUrl}
                                alt="Question diagram"
                                className="max-w-full max-h-48 object-contain rounded border border-gray-300"
                                style={{ maxWidth: '350px' }}
                              />
                            </div>
                          </div>
                        )}

                        <div className="text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded">
                          <ContentRenderer text={explanations[question.name]} />
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Modern Navigation */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                className="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                onClick={() => setView("result")}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <span className="relative">← Back to Results</span>
              </button>

              <button
                className="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                onClick={() => {
                  // Reset exam state and restart
                  setView("instructions");
                  setSelectedQuestionIndex(0);
                  setSelectedOptions({});
                  setResult({});
                  setTimeUp(false);
                  setSecondsLeft(examData?.duration || 0);
                  setExplanations({});
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <span className="relative">🔄 Retake Quiz</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  ) : null;
}

export default WriteExam;
